package com.data.datatools.stub.rest;

import java.util.Random;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class AreaController {

  @PostMapping("/area/get")
  @ResponseBody
  public String test(@RequestBody TestDto dto) throws InterruptedException {
    Random rd = new Random();
    int sleep = rd.nextInt(1000);
    Thread.sleep(sleep);
    return "{\"msg\":\"" + Thread.currentThread().getName() + " - " + dto.getAddress() + "\",\"code\":200,\"data\":{\"prov\":\"上海\",\"city\":\"上海市\",\"district\":\"宝山区\",\"town\":\"友谊路街道\",\"road\":\"漠河路\",\"roads\":\"\",\"subroad\":\"\"}}";
  }
}
