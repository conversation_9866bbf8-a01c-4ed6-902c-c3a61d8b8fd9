package com.data.datatools.stub;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication
public class DataAnalyticsStubApplication {

  private static final Logger logger = LoggerFactory.getLogger(DataAnalyticsStubApplication.class);

  public static void main(String[] args) {
    SpringApplication.run(DataAnalyticsStubApplication.class, args);
    String notice = "「「「「「Data-Analytics Stub Application start successfully.」」」」」";
    logger.info(notice);
  }
}
