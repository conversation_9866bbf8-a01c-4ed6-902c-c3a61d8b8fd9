<?xml version="1.0" encoding="UTF-8"?>
<configuration>

  <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
  <statusListener class="ch.qos.logback.core.status.NopStatusListener"/>

  <property name="LOG_DIR" value="/opt/data-analytics/stub"/>
  <property name="DEBUG_COMMON_ITEM"
            value="%d{yyyy-MM-dd HH:mm:ss.SSS}|%-5level|%thread|%logger{5}"/>

  <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
    <layout class="ch.qos.logback.classic.PatternLayout">
      <Pattern>${DEBUG_COMMON_ITEM} - %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}</Pattern>
    </layout>
  </appender>

  <logger name="org.springframework.boot.autoconfigure.logging" level="ERROR"/>
  <logger name="com.zaxxer.hikari.pool.HikariPool" level="ERROR"/>
  <logger name="org.springframework.security.web.util.matcher.AntPathRequestMatcher" level="ERROR"/>
  <logger name="org.springframework.security.web.FilterChainProxy" level="ERROR"/>
  <logger name="org.jxls.area.XlsArea" level="ERROR"/>

  <root level="INFO">
    <appender-ref ref="STDOUT"/>
  </root>

</configuration>
