<?xml version="1.0" encoding="UTF-8"?>
<configuration>

  <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
  <statusListener class="ch.qos.logback.core.status.NopStatusListener"/>

  <property name="LOG_DIR" value="/opt/data-analytics/log"/>
  <property name="DEBUG_COMMON_ITEM"
            value="%d{yyyy-MM-dd HH:mm:ss.SSS}|%-5level|%thread|%logger{5}"/>
  <property name="FILE_COMMON_ITEM"
            value="%d{yyyy-MM-dd HH:mm:ss.SSS}|%-5level|%X{session_id}|%X{remote_ip}|%X{request_id}|%X{user_id}|%logger{5}"/>

  <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
    <layout class="ch.qos.logback.classic.PatternLayout">
      <Pattern>${DEBUG_COMMON_ITEM} - %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}</Pattern>
    </layout>
  </appender>

  <appender name="FILE_OUT" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>${LOG_DIR}/system.log</file>
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <fileNamePattern>${LOG_DIR}/system.%d{yyyy-MM-dd}-%i.log</fileNamePattern>
      <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
        <maxFileSize>100MB</maxFileSize>
      </timeBasedFileNamingAndTriggeringPolicy>
      <maxHistory>60</maxHistory>
    </rollingPolicy>
    <encoder>
      <pattern>${FILE_COMMON_ITEM} - %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}</pattern>
      <charset>UTF-8</charset>
    </encoder>
  </appender>

  <logger name="org.springframework.boot.autoconfigure.logging" level="ERROR"/>
  <logger name="com.zaxxer.hikari.pool.HikariPool" level="ERROR"/>
  <logger name="org.springframework.security.web.util.matcher.AntPathRequestMatcher" level="ERROR"/>
  <logger name="org.springframework.security.web.FilterChainProxy" level="ERROR"/>
  <logger name="org.jxls.area.XlsArea" level="ERROR"/>

  <springProfile name="local">
    <root level="DEBUG">
      <appender-ref ref="STDOUT"/>
    </root>
  </springProfile>

  <springProfile name="!local">
    <root level="INFO">
      <appender-ref ref="FILE_OUT"/>
    </root>
  </springProfile>

</configuration>
