<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.data.datatools.repository.slave.mapper.DisplayTableMapper">

  <select id="selectDisplayDataList" resultType="java.util.Map">
    select
        t.*
    from (${param.sql}) t
    <where>
      1=1
      <if test="param.district != null">
        <if test="param.district == 'BLANK'">
          and (t.district = '' or t.district is null)
        </if>
        <if test="param.district != 'BLANK'">
          and t.district = #{param.district}
        </if>
      </if>
      <if test="param.town != null">
        <if test="param.town == 'BLANK'">
          and (t.town = '' or t.town is null)
        </if>
        <if test="param.town != 'BLANK'">
          and t.town = #{param.town}
        </if>
      </if>
      <include refid="com.data.datatools.repository.slave.mapper.SlaveControlTableMapper.itemFiltersSqlFragment"/>
    </where>
  </select>

  <resultMap id="selectNeedAnalyticsDataList_ResultMap" type="com.data.datatools.repository.slave.entity.AnalyticsSourceEntity">
    <result column="id" jdbcType="INTEGER" property="id" />
    <result column="import_key" jdbcType="VARCHAR" property="importKey" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="exist" jdbcType="BIT" property="exist" />
  </resultMap>
  <select id="selectNeedAnalyticsDataList" resultMap="selectNeedAnalyticsDataList_ResultMap">
    select
        t.*,
        false as exist
    from
        ${tableId} t
    where t.id not in (select v.data_id from ${displayId} v)
    union all
    select
      t.*,
      true as exist
    from
      ${tableId} t,
      ${displayId} v
    where t.id = v.data_id
      and t.updated_time > v.updated_time
  </select>
</mapper>
