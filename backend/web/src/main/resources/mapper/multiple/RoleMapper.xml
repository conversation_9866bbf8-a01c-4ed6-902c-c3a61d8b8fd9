<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.data.datatools.web.system.role.RoleMapper">

  <resultMap id="RoleResultMap" type="com.data.datatools.web.system.role.entity.RoleInfoEntity">
    <id column="id" property="id" />
    <result column="role_id" property="roleId" />
    <result column="role_name" property="roleName" />
    <result column="role_type" property="roleType" />
    <result column="show_sequence" property="showSequence" />
  </resultMap>

  <select id="selectList" resultMap="RoleResultMap">
    SELECT
    a.*
    FROM sys_role a
    <where>
      1 = 1
      <if test="param.roleName != null and param.roleName != ''">
        and a.`role_name` like CONC<PERSON>('%', #{param.roleName}, '%')
      </if>
      <if test="param.roleType != null and param.roleType != ''">
        and a.`role_type` = #{param.roleType}
      </if>
    </where>
    order by a.created_at desc
  </select>

  <select id="findMaxShowSequence" resultType="Integer">
    SELECT max(show_sequence) FROM sys_role
  </select>

  <select id="findById" parameterType="String" resultMap="RoleResultMap">
    SELECT a.*
    FROM sys_role a
    <where>
      a.role_id = #{id}
    </where>
  </select>

  <resultMap id="RoleAuthResultMap" type="com.data.datatools.web.system.role.entity.RoleAuthInfoEntity">
    <result column="auth_id" property="authId" />
    <result column="auth_name" property="authName" />
    <result column="role_auth_value" property="roleAuthValue" />
    <result column="auth_group_id" property="authGroupId" />
    <result column="auth_parent_group_id" property="authParentGroupId" />
    <result column="show_sequence" property="showSequence" />
  </resultMap>

  <select id="findAuthsByRoleId" resultMap="RoleAuthResultMap">
    select
      a.auth_id as auth_id,
      c.auth_name as auth_name,
      '1' as role_auth_value,
      c.auth_group_id as auth_group_id,
      'noParentAuth' as auth_parent_group_id,
      c.show_sequence as show_sequence
    from sys_role_auth a left join sys_auth c on c.auth_id = a.auth_id
    where a.role_id=#{id}
    union all
    select
      aa.auth_id as auth_id,
      auth_name as auth_name,
      '0' as role_auth_value,
      aa.auth_group_id as auth_group_id,
      'noParentAuth' as auth_parent_group_id,
      aa.show_sequence as show_sequence
    from sys_auth as aa
    where not exists (select * from sys_role_auth as bb where aa.auth_id=bb.auth_id and bb.role_id=#{id})
    union all
    select
      '' as auth_id,
      g.auth_group_name as auth_name,
      '0' as role_auth_value,
      g.auth_group_id as auth_group_id,
      g.auth_group_id as auth_parent_group_id,
      g.show_sequence as show_sequence
    from sys_auth_group g
    order by show_sequence
  </select>
</mapper>
