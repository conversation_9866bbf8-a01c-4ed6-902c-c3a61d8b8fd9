<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.data.datatools.web.workbench.table.TableCtrlMapper">

  <resultMap id="selectTableList_BaseResultMap" type="com.data.datatools.web.workbench.table.entity.TableRecordEntity">
    <id column="table_id" jdbcType="CHAR" property="tableId" />
    <result column="table_name" jdbcType="VARCHAR" property="tableName" />
    <result column="project_id" jdbcType="CHAR" property="projectId" />
    <result column="match_status" jdbcType="CHAR" property="matchStatus" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="score_template_name" jdbcType="VARCHAR" property="scoreTemplateName" />
  </resultMap>
  <select id="selectTableList" resultMap="selectTableList_BaseResultMap">
    select
      a.*,
      b.user_name
    from
      m_table_meta a
      left join view_sys_use_auth b on a.created_by = b.user_id
    where
      a.project_id = ${@com.data.datatools.common.security.SecuritySupport@getCurrentProjectParam()}
    <if test="param.searchKey != '' and param.searchKey != null">
      and a.table_name like CONCAT('%', #{param.searchKey}, '%')
    </if>
    order by a.updated_at desc
  </select>

  <resultMap id="selectImportHistoryList_BaseResultMap" type="com.data.datatools.repository.master.entity.MDataInsertHistoryEntity">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="table_id" jdbcType="VARCHAR" property="tableId" />
    <result column="import_key" jdbcType="CHAR" property="importKey" />
    <result column="import_type" jdbcType="INTEGER" property="importType" />
    <result column="import_time" jdbcType="TIMESTAMP" property="importTime" />
    <result column="import_meta" jdbcType="VARCHAR" property="importMeta" />
  </resultMap>

  <select id="selectImportHistoryList" resultMap="selectImportHistoryList_BaseResultMap">
    select
      *
    from
      m_data_insert_history
    <where>
      table_id = #{param.tableId}
      <if test="param.startTime != null">
        and import_time >= #{param.startTime} and import_time <![CDATA[ <= ]]> #{param.endTime}
      </if>
      <if test="param.tags != null">
        and
        <foreach collection="param.tags" index="index" item="tag" separator="or" close=")" open="(">
          JSON_CONTAINS(import_meta ->> "$.tags", #{tag})
        </foreach>
      </if>
    </where>
  </select>

  <resultMap id="selectEditHistoryList_ResultMap" type="com.data.datatools.web.workbench.table.entity.DataEditHistoryEntity">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="table_id" jdbcType="CHAR" property="tableId" />
    <result column="record_id" jdbcType="INTEGER" property="recordId" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="created_by" jdbcType="VARCHAR" property="createdUser" />
    <result column="user_name" jdbcType="VARCHAR" property="createdUserName" />
    <result column="edit_info" jdbcType="VARCHAR" property="editInfo" />
  </resultMap>
  <select id="selectEditHistoryList" resultMap="selectEditHistoryList_ResultMap">
    select
        a.*,
        coalesce(b.user_name, a.created_by) as user_name
    from
        m_data_edit_history a left join view_sys_use_auth b on a.created_by = b.user_id
    where
        a.table_id = #{tableId}
        and a.record_id = #{recordId}
    order by a.created_at desc
  </select>

  <resultMap id="selectExportHistoryList_ResultMap" type="com.data.datatools.web.workbench.table.entity.ExportHistoryEntity">
    <id column="file_id" jdbcType="VARCHAR" property="fileId" />
    <result column="table_id" jdbcType="CHAR" property="tableId" />
    <result column="table_mode" jdbcType="CHAR" property="tableMode" />
    <result column="type" jdbcType="CHAR" property="type" />
    <result column="status" jdbcType="CHAR" property="status" />
    <result column="export_file" jdbcType="VARCHAR" property="exportFile" />
    <result column="data_count" jdbcType="INTEGER" property="dataCount" />
    <result column="extra_spec" jdbcType="VARCHAR" property="extraSpec" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
  </resultMap>
  <select id="selectExportHistoryList" resultMap="selectExportHistoryList_ResultMap">
    select
      a.*,
      b.user_name
    from
      m_import_export_history a
      left join view_sys_use_auth b on a.created_by = b.user_id
    where
        a.table_id = #{tableId}
    and a.table_mode = #{tableMode}
    order by a.created_at desc
  </select>

  <resultMap id="selectNeedMatchData_ResultMap" type="com.data.datatools.web.workbench.table.entity.NeedMatchDataEntity">
    <id column="areaId" jdbcType="VARCHAR" property="areaId" />
    <result column="address" jdbcType="VARCHAR" property="address" />
  </resultMap>
  <select id="selectNeedMatchData" resultMap="selectNeedMatchData_ResultMap">
    select
      a.area_id as areaId,
      a.content ->> "\$.${areaColumn}" as address
    from ${dataTable} a
    where a.area_id not in (
        select area_id from m_area
    )
  </select>
</mapper>
