<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.data.datatools.web.workbench.display.DisplayMapper">

  <resultMap id="selectList_BaseResultMap" type="com.data.datatools.web.workbench.display.entity.DisplayRecordEntity">
    <id column="display_id" jdbcType="VARCHAR" property="displayId" />
    <result column="display_name" jdbcType="VARCHAR" property="displayName" />
    <result column="table_id" jdbcType="VARCHAR" property="tableId" />
    <result column="table_name" jdbcType="VARCHAR" property="tableName" />
    <result column="model_type" jdbcType="VARCHAR" property="modelType" />
    <result column="project_id" jdbcType="VARCHAR" property="projectId" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="user_name" jdbcType="VARCHAR" property="createUser" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="analytics_count" jdbcType="INTEGER" property="analyticsCount" />
    <result column="analytics_time" jdbcType="TIMESTAMP" property="analyticsTime" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <select id="selectList" resultMap="selectList_BaseResultMap">
    select
      a.*,
      b.table_name,
      c.user_name
    from
      m_display_meta a
      inner join m_table_meta b on a.table_id = b.table_id
      left join view_sys_use_auth c on a.created_by = c.user_id
    where
      a.project_id = ${@com.data.datatools.common.security.SecuritySupport@getCurrentProjectParam()}
    <if test="param.searchKey != '' and param.searchKey != null">
      and a.display_name like CONCAT('%', #{param.searchKey}, '%')
    </if>
    order by a.updated_at desc
  </select>

</mapper>
