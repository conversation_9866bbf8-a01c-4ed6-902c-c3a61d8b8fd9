<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.data.datatools.web.system.user.UserMapper">

  <resultMap id="UserResultMap" type="com.data.datatools.web.system.user.entity.UserRoleInfoEntity">
    <id column="user_id" property="userId"/>
    <result column="user_name" property="userName"/>
    <result column="user_email" property="userEmail"/>
    <result column="user_type" property="userType"/>
    <result column="district" property="district"/>
    <result column="town" property="town"/>
    <result column="deleted" property="userState"/>
  </resultMap>

  <select id="findById" parameterType="String" resultMap="UserResultMap">
    select
      a.user_id,
      a.user_name,
      a.user_email,
      a.user_type,
      a.district,
      a.town
    from
      sys_user a
    <where>
      a.user_id = #{id}
      and a.deleted = 0
    </where>
    group by a.user_id
  </select>

  <select id="selectList" resultMap="UserResultMap">
    select
      a.user_id,
      a.user_name,
      a.user_email,
      a.user_type,
      a.district,
      a.town,
      a.deleted
    from
      sys_user a
    <where>
      <if test="param.searchInput != null and param.searchInput != ''">
        and (a.`user_id` like CONCAT('%', #{param.userId}, '%') or a.`user_name` like CONCAT('%', #{param.userName}, '%'))
      </if>
      <if test="param.district != null and param.district != ''">
        and a.district = #{param.district}
      </if>
      <if test="param.userTypes != null and param.userTypes.length>0">
        and user_type in
        <foreach item='type' collection="param.userTypes" index="index" open="(" separator=","
          close=")">
            #{type}
        </foreach>
      </if>
    </where>
    group by a.user_id
  </select>
</mapper>
