<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.data.datatools.web.workbench.model.ModelMapper">

  <resultMap id="selectModelList_BaseResultMap" type="com.data.datatools.web.workbench.model.entity.ModelRecordEntity">
    <id column="model_id" jdbcType="CHAR" property="modelId" />
    <result column="model_name" jdbcType="VARCHAR" property="modelName" />
    <result column="model_type" jdbcType="VARCHAR" property="modelType" />
    <result column="project_id" jdbcType="CHAR" property="projectId" />
    <result column="status" jdbcType="CHAR" property="status" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="ai_model_name" jdbcType="VARCHAR" property="aiModelName" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
  </resultMap>
  <select id="selectModelList" resultMap="selectModelList_BaseResultMap">
    select
      a.*,
      b.user_name,
      c.name as ai_model_name
    from
      m_model_meta a
      left join view_sys_use_auth b on a.created_by = b.user_id
      left join m_ai_model c on a.ai_model_id = c.model_id
    where
      a.project_id = ${@com.data.datatools.common.security.SecuritySupport@getCurrentProjectParam()}
    <if test="param.searchKey != '' and param.searchKey != null">
      and a.model_name like CONCAT('%', #{param.searchKey}, '%')
    </if>
    order by a.updated_at desc
  </select>

  <select id="selectAiModelList" resultType="com.data.datatools.repository.master.entity.MAiModelEntity">
    select
      model_id as modelId,
      name
    from m_ai_model t1
    where
      not exists (
        select 1
        from m_model_meta t2
        where
            t2.ai_model_id = t1.model_id
            <if test="modelId != '' and modelId != null">
            and t2.model_id != #{modelId}
            </if>
      )
  </select>

</mapper>
