<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.data.datatools.repository.slave.mapper.SlaveControlTableMapper">

  <resultMap id="ResultMap" type="com.data.datatools.repository.slave.entity.SlaveDataEntity">
    <result column="id" jdbcType="INTEGER" property="id" />
    <result column="import_key" jdbcType="VARCHAR" property="importKey" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="district" jdbcType="VARCHAR" property="district" />
    <result column="town" jdbcType="VARCHAR" property="town" />
    <result column="road" jdbcType="VARCHAR" property="road" />
  </resultMap>

  <select id="selectDataList" resultMap="ResultMap">
    select
      t.id, t.import_key, t.content, ma.city, ma.district, ma.town, ma.road
    from ${param.tableId} t
    left join m_area ma on t.area_id = ma.area_id
    where
      t.deleted = #{param.deleted}
      <if test="param.district != null">
        <if test="param.district == 'BLANK'">
          and (ma.district = '' or ma.district is null)
        </if>
        <if test="param.district != 'BLANK'">
          and ma.district = #{param.district}
        </if>
      </if>
      <if test="param.town != null">
        <if test="param.town == 'BLANK'">
          and (ma.town = '' or ma.town is null)
        </if>
        <if test="param.town != 'BLANK'">
          and ma.town = #{param.town}
        </if>
      </if>
      <if test="param.keys != null">
        and t.import_key in
        <foreach collection="param.keys" index="index" item="key" separator="," close=")" open="(">
            #{key}
        </foreach>
      </if>
      <include refid="itemFiltersSqlFragment"/>
  </select>

  <sql id="itemFiltersSqlFragment">
    <if test="param.itemFilters != null and !param.itemFilters.isEmpty()">
      and
      <foreach collection="param.itemFilters" index="index" item="filter" separator="" open="(" close=")">
        <if test="index != 0">
          ${filter.relation}
        </if>
        <foreach collection="filter.items" index="i" item="f" separator="" open="(" close=")">
          <if test="i != 0">
            ${f.operator}
          </if>
          <choose>
            <when test="f.comparison == 'in' or f.comparison == 'not in'">
              <if test="param.type == 'table'">
                t.content->>"\$.${filter.field.id}" ${f.comparison}
              </if>
              <if test="param.type == 'display'">
                t.${filter.field.id} ${f.comparison}
              </if>
              <foreach collection="f.values" index="i1" item="v" separator="," close=")" open="(">
                #{v}
              </foreach>
            </when>
            <when test="f.comparison == 'work_day'">
              <if test="param.type == 'table'">
                <if test="f.value == 0">
                  (WEEKDAY(t.content->>"\$.${filter.field.id}") = 0 or WEEKDAY(t.content->>"\$.${filter.field.id}") = 6 or WEEKDAY(t.content->>"\$.${filter.field.id}") is null)
                </if>
                <if test="f.value == 1">
                  (WEEKDAY(t.content->>"\$.${filter.field.id}") > 0 and WEEKDAY(t.content->>"\$.${filter.field.id}") &lt; 6)
                </if>
              </if>
              <if test="param.type == 'display'">
                <if test="f.value == 0">
                  (WEEKDAY(t.${filter.field.id}) = 0 or WEEKDAY(t.${filter.field.id}) = 6 or WEEKDAY(t.${filter.field.id}) is null)
                </if>
                <if test="f.value == 1">
                  (WEEKDAY(t.${filter.field.id}) > 0 and WEEKDAY(t.${filter.field.id}) &lt; 6)
                </if>
              </if>
            </when>
            <when test="f.comparison == 'date_range' or f.comparison == 'time_range'">
              <if test="param.type == 'table'">
                (t.content->>"\$.${filter.field.id}" >= #{f.values[0]} and t.content->>"\$.${filter.field.id}" &lt;= #{f.values[1]})
              </if>
              <if test="param.type == 'display'">
                (t.${filter.field.id} >= #{f.values[0]} and t.${filter.field.id} &lt;= #{f.values[1]})
              </if>
            </when>
            <otherwise>
              <if test="param.type == 'table'">
                t.content->>"\$.${filter.field.id}" ${f.comparison} #{f.value}
              </if>
              <if test="param.type == 'display'">
                t.${filter.field.id} ${f.comparison} #{f.value}
              </if>
            </otherwise>
          </choose>
        </foreach>
      </foreach>
    </if>
  </sql>

  <update id="deleteTableData">
    update ${tableId}
    set deleted = '1', updated_time = current_timestamp(), updated_by = ${@com.data.datatools.common.security.SecuritySupport@getLoginUserIdParam()}
    where
    id in
    <foreach collection="dataIds" index="index" item="id" separator="," close=")" open="(">
      #{id}
    </foreach>
  </update>

  <delete id="deleteAlwaysTableData">
    delete from ${tableId} where id in
    <foreach collection="dataIds" index="index" item="id" separator="," close=")" open="(">
      #{id}
    </foreach>
  </delete>

  <update id="batchUpdate">
    update ${param.tableId} t
      set
        t.content = json_set(t.content,
        <foreach collection="param.dataList" item="item" index="index" separator=",">
          "$.${item.column}", #{item.value}
        </foreach>
        ),
        t.updated_time = current_timestamp(),
        t.updated_by = ${@com.data.datatools.common.security.SecuritySupport@getLoginUserIdParam()}
    <where>
      <if test="param.keys != null and param.keys.size() > 0">
        and t.import_key in
        <foreach collection="param.keys" index="index" item="key" separator="," close=")" open="(">
          #{key}
        </foreach>
      </if>
      <include refid="itemFiltersSqlFragment"/>
    </where>
  </update>
</mapper>
