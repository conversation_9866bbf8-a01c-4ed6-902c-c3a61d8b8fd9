<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="single.MDataInsertHistoryMapper">
  <resultMap id="BaseResultMap" type="com.data.datatools.repository.master.entity.MDataInsertHistoryEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="table_id" jdbcType="CHAR" property="tableId" />
    <result column="import_key" jdbcType="CHAR" property="importKey" />
    <result column="import_type" jdbcType="INTEGER" property="importType" />
    <result column="import_time" jdbcType="TIMESTAMP" property="importTime" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, table_id, import_key, import_type, import_time, created_at, created_by
  </sql>
  <sql id="Alias_Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    ${alias}.id as ${alias}_id, ${alias}.table_id as ${alias}_table_id, ${alias}.import_key as ${alias}_import_key, 
      ${alias}.import_type as ${alias}_import_type, ${alias}.import_time as ${alias}_import_time, 
      ${alias}.created_at as ${alias}_created_at, ${alias}.created_by as ${alias}_created_by, 
      ${alias}.import_meta as ${alias}_import_meta
  </sql>
</mapper>
