<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="single.MDataEditHistoryMapper">
  <resultMap id="BaseResultMap" type="com.data.datatools.repository.master.entity.MDataEditHistoryEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="table_id" jdbcType="CHAR" property="tableId" />
    <result column="record_id" jdbcType="INTEGER" property="recordId" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, table_id, record_id, created_at, created_by
  </sql>
  <sql id="Alias_Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    ${alias}.id as ${alias}_id, ${alias}.table_id as ${alias}_table_id, ${alias}.record_id as ${alias}_record_id, 
      ${alias}.created_at as ${alias}_created_at, ${alias}.created_by as ${alias}_created_by, 
      ${alias}.edit_info as ${alias}_edit_info
  </sql>
</mapper>
