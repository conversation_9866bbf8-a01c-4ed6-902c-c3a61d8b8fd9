<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="single.SysUserMapper">
  <resultMap id="BaseResultMap" type="com.data.datatools.repository.master.entity.SysUserEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="user_email" jdbcType="VARCHAR" property="userEmail" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="password_status" jdbcType="CHAR" property="passwordStatus" />
    <result column="password_updated_at" jdbcType="TIMESTAMP" property="passwordUpdatedAt" />
    <result column="current_project" jdbcType="CHAR" property="currentProject" />
    <result column="token" jdbcType="VARCHAR" property="token" />
    <result column="login_wrong_count" jdbcType="INTEGER" property="loginWrongCount" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="login_time" jdbcType="VARCHAR" property="loginTime" />
    <result column="login_ip" jdbcType="VARCHAR" property="loginIp" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    user_id, user_name, user_email, password, password_status, password_updated_at, current_project, 
    token, login_wrong_count, remark, login_time, login_ip, deleted, created_at, created_by, 
    updated_at, updated_by
  </sql>
  <sql id="Alias_Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    ${alias}.user_id as ${alias}_user_id, ${alias}.user_name as ${alias}_user_name, ${alias}.user_email as ${alias}_user_email, 
      ${alias}.password as ${alias}_password, ${alias}.password_status as ${alias}_password_status, 
      ${alias}.password_updated_at as ${alias}_password_updated_at, ${alias}.current_project as ${alias}_current_project, 
      ${alias}.token as ${alias}_token, ${alias}.login_wrong_count as ${alias}_login_wrong_count, 
      ${alias}.remark as ${alias}_remark, ${alias}.login_time as ${alias}_login_time, 
      ${alias}.login_ip as ${alias}_login_ip, ${alias}.deleted as ${alias}_deleted, ${alias}.created_at as ${alias}_created_at, 
      ${alias}.created_by as ${alias}_created_by, ${alias}.updated_at as ${alias}_updated_at, 
      ${alias}.updated_by as ${alias}_updated_by
  </sql>
</mapper>
