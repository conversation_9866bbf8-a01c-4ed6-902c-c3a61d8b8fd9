<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="single.MProjectMapper">
  <resultMap id="BaseResultMap" type="com.data.datatools.repository.master.entity.MProjectEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="project_id" jdbcType="CHAR" property="projectId" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    project_id, project_name, remark, deleted, created_at, created_by, updated_at, updated_by
  </sql>
  <sql id="Alias_Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    ${alias}.project_id as ${alias}_project_id, ${alias}.project_name as ${alias}_project_name, 
      ${alias}.remark as ${alias}_remark, ${alias}.deleted as ${alias}_deleted, ${alias}.created_at as ${alias}_created_at, 
      ${alias}.created_by as ${alias}_created_by, ${alias}.updated_at as ${alias}_updated_at, 
      ${alias}.updated_by as ${alias}_updated_by
  </sql>
</mapper>
