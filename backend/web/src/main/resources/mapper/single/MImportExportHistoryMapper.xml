<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="single.MImportExportHistoryMapper">
  <resultMap id="BaseResultMap" type="com.data.datatools.repository.master.entity.MImportExportHistoryEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="file_id" jdbcType="VARCHAR" property="fileId" />
    <result column="table_id" jdbcType="CHAR" property="tableId" />
    <result column="table_mode" jdbcType="CHAR" property="tableMode" />
    <result column="type" jdbcType="CHAR" property="type" />
    <result column="status" jdbcType="CHAR" property="status" />
    <result column="export_file" jdbcType="VARCHAR" property="exportFile" />
    <result column="data_count" jdbcType="INTEGER" property="dataCount" />
    <result column="extra_spec" jdbcType="VARCHAR" property="extraSpec" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    file_id, table_id, table_mode, type, status, export_file, data_count, extra_spec, 
    deleted, created_at, created_by, updated_at, updated_by
  </sql>
  <sql id="Alias_Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    ${alias}.file_id as ${alias}_file_id, ${alias}.table_id as ${alias}_table_id, ${alias}.table_mode as ${alias}_table_mode, 
      ${alias}.type as ${alias}_type, ${alias}.status as ${alias}_status, ${alias}.export_file as ${alias}_export_file, 
      ${alias}.data_count as ${alias}_data_count, ${alias}.extra_spec as ${alias}_extra_spec, 
      ${alias}.deleted as ${alias}_deleted, ${alias}.created_at as ${alias}_created_at, 
      ${alias}.created_by as ${alias}_created_by, ${alias}.updated_at as ${alias}_updated_at, 
      ${alias}.updated_by as ${alias}_updated_by
  </sql>
</mapper>
