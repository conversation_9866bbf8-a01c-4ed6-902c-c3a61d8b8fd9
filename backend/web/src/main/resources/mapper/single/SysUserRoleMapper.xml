<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="single.SysUserRoleMapper">
  <resultMap id="BaseResultMap" type="com.data.datatools.repository.master.entity.SysUserRoleEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="role_id" jdbcType="VARCHAR" property="roleId" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, user_id, role_id, deleted, created_at, created_by, updated_at, updated_by
  </sql>
  <sql id="Alias_Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    ${alias}.id as ${alias}_id, ${alias}.user_id as ${alias}_user_id, ${alias}.role_id as ${alias}_role_id, 
      ${alias}.deleted as ${alias}_deleted, ${alias}.created_at as ${alias}_created_at, 
      ${alias}.created_by as ${alias}_created_by, ${alias}.updated_at as ${alias}_updated_at, 
      ${alias}.updated_by as ${alias}_updated_by
  </sql>
</mapper>
