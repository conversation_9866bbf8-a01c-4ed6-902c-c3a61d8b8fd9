<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="single.ViewSysUseAuthMapper">
  <resultMap id="BaseResultMap" type="com.data.datatools.repository.master.entity.ViewSysUseAuthEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="password_status" jdbcType="CHAR" property="passwordStatus" />
    <result column="password_updated_at" jdbcType="TIMESTAMP" property="passwordUpdatedAt" />
    <result column="current_project" jdbcType="CHAR" property="currentProject" />
    <result column="login_wrong_count" jdbcType="INTEGER" property="loginWrongCount" />
    <result column="user_email" jdbcType="VARCHAR" property="userEmail" />
    <result column="role_name" jdbcType="VARCHAR" property="roleName" />
    <result column="solid_user" jdbcType="INTEGER" property="solidUser" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    user_id, user_name, password, password_status, password_updated_at, current_project, 
    login_wrong_count, user_email, role_name, solid_user
  </sql>
  <sql id="Alias_Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    ${alias}.user_id as ${alias}_user_id, ${alias}.user_name as ${alias}_user_name, ${alias}.password as ${alias}_password, 
      ${alias}.password_status as ${alias}_password_status, ${alias}.password_updated_at as ${alias}_password_updated_at, 
      ${alias}.current_project as ${alias}_current_project, ${alias}.login_wrong_count as ${alias}_login_wrong_count, 
      ${alias}.user_email as ${alias}_user_email, ${alias}.role_name as ${alias}_role_name, 
      ${alias}.solid_user as ${alias}_solid_user
  </sql>
</mapper>
