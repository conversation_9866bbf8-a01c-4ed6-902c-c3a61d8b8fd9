spring.profiles.active=local

build.showinfo=false
build.version=@project.version@
build.timestamp=@maven.build.timestamp@

# for datasource
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.hikari.minimum-idle=10
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000

# for spring exception
spring.mvc.throw-exception-if-no-handler-found=true
spring.resources.add-mappings=false

spring.servlet.multipart.max-file-size=100MB
spring.servlet.multipart.max-request-size=128MB

# message
spring.messages.basename=i18n/messages

# session timeout (30m)
server.servlet.session.timeout=7200s
server.servlet.contextPath=/data-analytics

# server port
server.port=9019
excel.template.path=classpath:templates

# mapper
mybatis.mapper-locations=classpath*:mapper/**/*.xml
