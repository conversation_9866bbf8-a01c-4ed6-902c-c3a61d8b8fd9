# for database

spring.datasource.url=************************************************************************************
spring.datasource.username=root
spring.datasource.password=root

# for local only
spring.datasource.hikari.minimum-idle=3

#data-analytics.area.url=http://*************:180/bk-api/bk/thirdApi/aliIdentityAddress
data-analytics.area.url=http://localhost:9039/area/get
data-analytics.hanlp-path=D:/Project/wenj/nlp/data-for-1.7.5/hanlp.properties

# ai url
data-analytics.ai.url=http://*************:60012/v1
data-analytics.ai.token-ds=dataset-KSn7pQWXe83498N8I3mAoTFE
data-analytics.ai.token-app=app-IuXSypqAzyt0cxmabK2G2He6
data-analytics.ai.token-app_clean-text=app-tE6KCs2kbXNyhsUb174G2qly

export.excel.base.path=D:/data/data-analytics/
## 地址解析接口URL
#address.parse.api.url=http://*************:60012/v1/chat-messages
#
## 地址解析模式：local(本地解析) 或 api(接口调用)
#address.parse.mode=local
#
## 地址解析API调用用户名
#address.parse.api.user=王韬
#
## 地址解析API调用Token
#address.parse.api.token=app-HA2DDqGPLXxYxUpSilonkItC
#
## HTTP客户端超时配置（毫秒）
#http.client.connect.timeout=120000
#http.client.read.timeout=180000
#
## 地址解析API重试配置
#address.parse.api.retry.count=3
