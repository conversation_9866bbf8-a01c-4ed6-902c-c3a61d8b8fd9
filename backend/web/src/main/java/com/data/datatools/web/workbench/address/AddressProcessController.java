package com.data.datatools.web.workbench.address;

import com.data.datatools.common.response.BaseResponse;
import com.data.datatools.utils.StringUtils;
import com.data.datatools.web.workbench.address.dto.AddressProcessDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

@RestController
@RequestMapping("/workbench/address")
public class AddressProcessController {

    private static final Logger logger = LoggerFactory.getLogger(AddressProcessController.class);

    @Autowired
    private AddressProcessService addressProcessService;

    @Value("${export.excel.base.path}")
    private String basePath;

    /**
     * 启动地址处理任务
     */
    @PostMapping("/process")
    @PreAuthorize("hasAnyAuthority('A', 'C', 'D')")
    public BaseResponse processAddress(AddressProcessDto dto) {
        logger.info("启动地址处理任务");
        return addressProcessService.processAddressExcel(dto);
    }

    /**
     * 查询任务状态
     */
    @GetMapping("/status/{taskId}")
    @PreAuthorize("hasAnyAuthority('A', 'C', 'D')")
    public BaseResponse getTaskStatus(@PathVariable String taskId) {
        return addressProcessService.getTaskStatus(taskId);
    }

    /**
     * 下载处理结果文件
     */
    @GetMapping("/download/{taskId}")
    @PreAuthorize("hasAnyAuthority('A', 'C', 'D')")
    public ResponseEntity<Resource> downloadResult(@PathVariable String taskId,
                                                  @RequestParam(required = false) String fileName) {
        try {
            File file = new File(StringUtils.concatPath(basePath, "excel", taskId));
            
            if (!file.exists()) {
                logger.warn("下载文件不存在，任务ID: {}", taskId);
                return ResponseEntity.notFound().build();
            }

            Resource resource = new FileSystemResource(file);
            
            // 设置文件名
            String downloadFileName = fileName != null ? fileName : "processed_addresses.xlsx";
            String encodedFileName = URLEncoder.encode(downloadFileName, StandardCharsets.UTF_8.name());
            
            return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                .header(HttpHeaders.CONTENT_DISPOSITION,
                       "attachment; filename=\"" + encodedFileName + "\"; filename*=UTF-8''" + encodedFileName)
                .body(resource);
                
        } catch (UnsupportedEncodingException e) {
            logger.error("文件名编码失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        } catch (Exception e) {
            logger.error("下载文件时发生错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
