package com.data.datatools.repository.slave.mapper;

import com.data.datatools.common.base.dao.IBasePagingMapper;
import com.data.datatools.common.response.ListResponse;
import com.data.datatools.repository.slave.entity.AnalyticsSourceEntity;
import com.data.datatools.web.workbench.display.entity.DisplaySelectParam;
import io.swagger.models.auth.In;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface DisplayTableMapper extends IBasePagingMapper {

  @Update("CREATE TABLE `${tableId}`(`data_id` int NOT NULL,`content` json,`correct_content` json,`debug_content` json,"
    + "`updated_time` timestamp   NOT NULL,"
    + "PRIMARY KEY (`data_id`)"
    + ")ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Compact;")
  int createDisplayTable(String tableId);

  @Update("drop table `${displayId}`")
  int deleteTable(String displayId);

  @Update("truncate table `${displayId}`")
  void truncateTable(String displayId);

  // 视图检索
  default List<Map<String, Object>> selectDisplayDataListWithPage(DisplaySelectParam param,
    ListResponse<Map<String, Object>> response, Function<Map<String, Object>, Map<String, Object>> convert) {
    return selectWithPage(param, response, convert, this::selectDisplayDataList);
  }
  List<Map<String, Object>> selectDisplayDataList(@Param("param") DisplaySelectParam param);

  List<AnalyticsSourceEntity> selectNeedAnalyticsDataList(String tableId, String displayId);

  @Insert("insert into ${displayId} (data_id, content, debug_content, updated_time)"
    + " values (#{dataId}, #{content}, #{debugContent}, current_timestamp())")
  int insert(String displayId, Integer dataId, String content, String debugContent);

  @Update("update ${displayId} set content = #{content}, debug_content=#{debugContent}, updated_time=current_timestamp() where data_id=#{dataId}")
  int update(String displayId, Integer dataId, String content, String debugContent);

  @Select("select correct_content from ${displayId} where data_id = #{dataId}")
  String getCorrectContent(String displayId, Integer dataId);

  @Update("update ${displayId} set correct_content = #{correctContent}, updated_time=current_timestamp() where data_id=#{dataId}")
  int updateCorrectContent(String displayId, Integer dataId, String correctContent);

  @Select("select content from ${tableId} where id = #{dataId}")
  String getContent(String tableId, Integer dataId);
}
