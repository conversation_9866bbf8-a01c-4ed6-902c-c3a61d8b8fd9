package com.data.datatools.web.system.role;

import com.data.datatools.common.code.OptType;
import com.data.datatools.common.response.BaseResponse;
import com.data.datatools.common.response.DataResponse;
import com.data.datatools.common.response.ListResponse;
import com.data.datatools.common.response.MessageResponse;
import com.data.datatools.web.system.role.dto.RoleCondition;
import com.data.datatools.web.system.role.dto.RoleInfoModel;
import com.data.datatools.web.system.role.dto.RoleKeyParam;
import java.util.List;
import javax.validation.Valid;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class RoleController {

  @Autowired
  private RoleService roleService;

  @PostMapping("/role/search")
  public BaseResponse searchUserByPaging(@RequestBody RoleCondition condition) {
    ListResponse<RoleInfoModel> listResponse = new ListResponse<>();
    roleService.searchByPagingWithCond(condition, listResponse);
    return listResponse;
  }

  @PostMapping("/role/get")
  public BaseResponse findById(@RequestBody RoleKeyParam param) {
    DataResponse<RoleInfoModel> dataResponse = new DataResponse<>();
    RoleInfoModel model;
    if (!OptType.ADD.is(param.getOptType())) {
      model = roleService.findInfoById(param.getId());
    } else {
      model = roleService.findInfoById(StringUtils.EMPTY);
    }
    return dataResponse.setValue(model);
  }

  @PostMapping("/role/getUserCountByRoleId")
  public BaseResponse getUserCountByRoleId(String roleId) {
    DataResponse<Void> dataResponse = new DataResponse<>();
    dataResponse.putData("data",roleService.getUserCountByRoleId(roleId));
    return dataResponse;
  }

  @PostMapping("/role/delete")
  public BaseResponse deleteById(String roleId) {

    int count = roleService.deleteById(roleId);
    if (count < 1) {
      return MessageResponse.newErrorMessage("删除失败。");
    }
    return new BaseResponse();
  }

  @PostMapping("/role/multipleDelete")
  public BaseResponse deleteMultiple(@RequestBody List<String> roleIds) {
    MessageResponse messageResponse = MessageResponse.newInstance();
    roleService.deleteRoles(roleIds,messageResponse);
    if (messageResponse.hasError()) {
      return messageResponse;
    }
    return new BaseResponse();
  }

  @PostMapping("/role/modify")
  public BaseResponse modifyRole(@RequestBody RoleInfoModel model) {
    return roleService.updateRole(model);
  }

  @PostMapping("/role/add")
  public BaseResponse addRole(@RequestBody RoleInfoModel model) {
    return roleService.addRole(model);
  }
}
