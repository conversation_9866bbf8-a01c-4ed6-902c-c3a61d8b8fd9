package com.data.datatools.common.response;

import com.data.datatools.common.constants.ICommonConstants;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

public class MessageResponse extends BaseResponse {

  @Getter
  @JsonProperty("messages")
  private final List<MessageEx> messages = new ArrayList<>();

  @Getter
  private String type;

  @Setter
  @Getter
  private boolean custom;

  @Setter
  @Getter
  @JsonProperty("data")
  @JsonInclude(Include.NON_NULL)
  private Object value;

//  @JsonIgnore
//  private final Messages messageHandler;

  private MessageResponse() {
    super(ICommonConstants.RESPONSE_STATUS_CODE_FAILED_BIZ);
//    messageHandler = SpringContext.getBean(Messages.class);
    custom = false;
  }

  public static MessageResponse newInstance() {
    return new MessageResponse();
  }

  public static MessageResponse newInfoMessage(String message, Object... args) {
    MessageResponse messageResponse = newInstance();
    messageResponse.addMessage("I", message, args);
    return messageResponse;
  }

  public static MessageResponse newErrorMessage(String message, Object... args) {
    MessageResponse messageResponse = newInstance();
    messageResponse.addMessage("E", message, args);
    return messageResponse;
  }

  public static MessageResponse newWarningMessage(String message, Object... args) {
    MessageResponse messageResponse = newInstance();
    messageResponse.addMessage("W", message, args);
    return messageResponse;
  }

  public MessageResponse addErrorMessage(String message, Object... args) {
    addMessage("E", message, args);
    return this;
  }

//  public static MessageResponse newInstance(String messageId, String... args) {
//    MessageResponse messageResponse = new MessageResponse();
//    messageResponse.addMessageId(messageId, args);
//    return messageResponse;
//  }
//  public MessageResponse addMessageId(String messageId, String... args) {
//    addMessageInner(String.valueOf(messageId.charAt(0)), messageId, messageHandler
//      .getMessage(messageId, args));
//    updateType();
//    return this;
//  }

  public MessageResponse addMessage(String type, String message, Object... args) {
    String msg = message;
    if (args != null) {
      for (int i = 0; i < args.length; i++) {
        msg = msg.replaceAll("\\{" + i + "}", String.valueOf(args[i]));
      }
    }
    addMessageInner(type, "", msg);
    updateType();
    return this;
  }

  public MessageResponse custom() {
    this.setCustom(true);
    return this;
  }

  private void updateType() {
    if (messages.stream().anyMatch(m -> "E".equals(m.getType()))) {
      type = "E";
    } else if (messages.stream().anyMatch(m -> "W".equals(m.getType()))) {
      type = "W";
    } else {
      type = "I";
    }
  }

  public boolean hasError() {
    return messages.stream().anyMatch(m -> "E".equals(m.getType()));
  }

  protected void addMessageInner(String type, String messageId, String message) {
    messages.add(new MessageEx(type, messageId, message));
  }

  @Data
  public static class MessageEx {

    private String type;
    private String messageId;
    private String message;

    public MessageEx() {
    }

    public MessageEx(String type, String messageId, String message) {
      this.type = type;
      this.messageId = messageId;
      this.message = message;
    }
  }
}

