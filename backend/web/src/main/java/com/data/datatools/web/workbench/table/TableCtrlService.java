package com.data.datatools.web.workbench.table;

import com.data.datatools.common.code.*;
import com.data.datatools.common.listener.AreaMatchEvent;
import com.data.datatools.component.*;
import com.data.datatools.common.listener.AnalyticsEvent;
import com.data.datatools.component.dto.meta.FilterKeyResult;
import com.data.datatools.component.dto.excel.ExcelCreateModel;
import com.data.datatools.repository.master.entity.MDataInsertHistoryEntity;
import com.data.datatools.repository.master.entity.MDisplayMetaEntity;
import com.data.datatools.repository.master.entity.MImportExportHistoryEntity;
import com.data.datatools.repository.master.entity.MTableMetaEntity;
import com.data.datatools.repository.master.mapper.MDataInsertHistoryMapper;
import com.data.datatools.repository.master.mapper.MDataInsertHistoryNames;
import com.data.datatools.repository.master.mapper.MDisplayMetaMapper;
import com.data.datatools.repository.master.mapper.MDisplayMetaNames;
import com.data.datatools.repository.master.mapper.MImportExportHistoryMapper;
import com.data.datatools.repository.master.mapper.MTableMetaMapper;
import com.data.datatools.repository.master.mapper.MTableMetaNames;
import com.data.datatools.web.workbench.table.dto.*;
import com.data.datatools.web.workbench.table.entity.*;
import com.google.common.collect.Maps;
import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.data.datatools.common.exception.BusinessException;
import com.data.datatools.common.model.FilterModel;
import com.data.datatools.common.model.ItemFilters;
import com.data.datatools.common.model.SelectOption;
import com.data.datatools.common.response.BaseResponse;
import com.data.datatools.common.response.DataResponse;
import com.data.datatools.common.response.ListResponse;
import com.data.datatools.common.response.MessageResponse;
import com.data.datatools.common.security.SecuritySupport;
import com.data.datatools.component.ExcelComponent.ExcelData;
import com.data.datatools.component.dto.meta.TableColumnMeta;
import com.data.datatools.config.security.ResponseSupport;
import com.data.datatools.repository.slave.entity.SlaveDataEntity;
import com.data.datatools.repository.slave.entity.SlaveTableEntity;
import com.data.datatools.repository.slave.mapper.SlaveControlTableMapper;
import com.data.datatools.utils.DateUtils;
import com.data.datatools.utils.ListUtils;
import com.data.datatools.utils.SqlUtils;
import com.data.datatools.utils.StringUtils;

import java.io.File;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeMap;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.mybatis.dynamic.sql.SqlBuilder;
import org.mybatis.dynamic.sql.select.CountDSL;
import org.mybatis.dynamic.sql.select.SelectModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class TableCtrlService {

  @Autowired
  private MTableMetaMapper mTableMetaMapper;

  @Autowired
  private MDataInsertHistoryMapper mDataInsertHistoryMapper;

  @Autowired
  private TableCtrlMapper tableCtrlMapper;

  @Autowired
  private SlaveControlTableMapper slaveControlTableMapper;

  @Autowired
  private ExcelComponent excelComponent;

  @Autowired
  private ExcelCreateComponent excelCreateComponent;

  @Autowired
  private SqlCommonComponent sqlCommonComponent;

  @Autowired
  private ExportComponent exportComponent;

  @Autowired
  private MImportExportHistoryMapper mImportExportHistoryMapper;

  @Autowired
  private AreaComponent areaComponent;

  @Autowired
  private MDisplayMetaMapper mDisplayMetaMapper;

  @Autowired
  private ApplicationEventPublisher applicationEventPublisher;

  @Autowired
  private PulldownComponent pulldownComponent;

  private static final Logger logger = LoggerFactory.getLogger(TableCtrlService.class);

  @Transactional
  public BaseResponse saveTable(TableMetaDto dto) {

    String tableName = dto.getTableName();
    long count = mTableMetaMapper.count(c -> {
      CountDSL<SelectModel>.CountWhereBuilder where = c.where();
      if (OptType.EDIT.is(dto.getOptType())) {
        where.and(MTableMetaNames.tableId, SqlBuilder.isNotEqualTo(dto.getTableId()));
      }
      where.and(MTableMetaNames.projectId, SqlBuilder.isEqualTo(SecuritySupport.getCurrentProject()));
      where.and(MTableMetaNames.tableName, SqlBuilder.isEqualTo(tableName));
      where.and(MTableMetaNames.deleted, SqlBuilder.isEqualTo(false));
      return c;
    });
    if (count > 0) {
      return MessageResponse.newErrorMessage("表名【{0}】已经存在。", tableName);
    }
    MTableMetaEntity entity;
    String tableId = null;
    if (OptType.EDIT.is(dto.getOptType())) {
      entity = mTableMetaMapper.selectByPrimaryKey(dto.getTableId())
        .orElseThrow(() -> new BusinessException("该数据表已经不存在，请返回一览刷新后再操作。"));
    } else {
      entity = new MTableMetaEntity();
      tableId = SqlUtils.generateTableId();
      entity.setTableId(tableId);
      entity.setMatchStatus(MatchStatus.COMPLETE.status());
    }

    entity.setTableName(tableName);
    entity.setProjectId(SecuritySupport.getCurrentProject());
    entity.setRemark(dto.getRemark());
    entity.setDeleted(false);
    List<TableColumnMetaDto> columnList = dto.getColumnList();

    List<TableColumnMeta> columnMetaList = new ArrayList<>();
    for (int i = 0; i < columnList.size(); i++) {
      TableColumnMetaDto columnDto = columnList.get(i);
      TableColumnMeta columnMeta = new TableColumnMeta();
      if (OptType.EDIT.is(dto.getOptType())) {
        String columnId = columnDto.getColumnId();
        if (StringUtils.isNotEmpty(columnId)) {
          columnMeta.setId(columnId);
        } else {
          columnMeta.setId(SqlUtils.generateColumnId());
        }
      } else {
        columnMeta.setId(SqlUtils.generateColumnId());
      }
      columnMeta.setName(columnDto.getColumnName());
      columnMeta.setType(columnDto.getColumnType());
      columnMeta.setIndex(i);
      columnMeta.setKey(columnDto.isPrimaryKey());
      columnMeta.setLength(columnDto.getLength());
      columnMeta.setAddress(columnDto.getAddress());
      columnMeta.setUnique(false);
      columnMeta.setNullAble(columnDto.isNullAble());
      columnMetaList.add(columnMeta);
    }

    entity.setTableMeta(JSON.toJSONString(columnMetaList));

    if (OptType.EDIT.is(dto.getOptType())) {
      mTableMetaMapper.updateByPrimaryKey(entity);
    } else {
      mTableMetaMapper.insert(entity);
      slaveControlTableMapper.createTable(tableId);
    }

    return MessageResponse.newInfoMessage(OptType.EDIT.is(dto.getOptType()) ? "数据表修改成功。" : "数据表创建成功");
  }

  public BaseResponse getTable(String tableId) {
    DataResponse<TableMetaDto> response = new DataResponse<>();
    Optional<MTableMetaEntity> optEntity = mTableMetaMapper.selectByPrimaryKey(tableId);
    if (!optEntity.isPresent()) {
      return MessageResponse.newErrorMessage("该数据表已经不存在，请返回一览刷新后再操作。").custom();
    }
    MTableMetaEntity entity = optEntity.get();
    TableMetaDto dto = new TableMetaDto();
    dto.setTableId(entity.getTableId());
    dto.setTableName(entity.getTableName());
    dto.setRemark(entity.getRemark());
    String tableMeta = entity.getTableMeta();
    if (StringUtils.isNotEmpty(tableMeta)) {
      List<TableColumnMeta> columnMetaList = JSONArray.parseArray(tableMeta, TableColumnMeta.class);
      List<TableColumnMetaDto> metaDtoList = columnMetaList.stream().map(e -> {
        TableColumnMetaDto columnMeta = new TableColumnMetaDto();
        columnMeta.setColumnId(e.getId());
        columnMeta.setColumnName(e.getName());
        columnMeta.setPrimaryKey(e.isKey());
        columnMeta.setNullAble(e.isNullAble());
        columnMeta.setColumnType(e.getType());
        columnMeta.setLength(e.getLength());
        columnMeta.setIndex(e.getIndex());
        columnMeta.setAddress(e.getAddress());
        columnMeta.setErrors(Maps.newHashMap());
        return columnMeta;
      }).collect(Collectors.toList());
      dto.setColumnList(metaDtoList);
    } else {
      dto.setColumnList(Lists.newArrayList());
    }
    return response.setValue(dto);
  }

  @Transactional
  public BaseResponse deleteTable(String tableId) {

    mTableMetaMapper.deleteByPrimaryKey(tableId);
    mDataInsertHistoryMapper.delete(c -> c.where().and(MDataInsertHistoryNames.tableId, SqlBuilder.isEqualTo(tableId)));
    slaveControlTableMapper.deleteTable(tableId);

    List<MDisplayMetaEntity> displayEntityList = mDisplayMetaMapper.select(
      c -> c.where().and(MDisplayMetaNames.tableId, SqlBuilder.isEqualTo(tableId)));

    if (!displayEntityList.isEmpty()) {
      for (MDisplayMetaEntity displayEntity : displayEntityList) {
        slaveControlTableMapper.deleteTable(displayEntity.getDisplayId());
        mDisplayMetaMapper.deleteByPrimaryKey(displayEntity.getDisplayId());
      }
    }

    return MessageResponse.newInfoMessage("数据表删除成功。");
  }

  @Transactional
  public BaseResponse clearTableData(String tableId) {
    mDataInsertHistoryMapper.delete(c -> c.where().and(MDataInsertHistoryNames.tableId, SqlBuilder.isEqualTo(tableId)));
    slaveControlTableMapper.truncateTable(tableId);

    List<MDisplayMetaEntity> displayEntityList = mDisplayMetaMapper.select(
      c -> c.where().and(MDisplayMetaNames.tableId, SqlBuilder.isEqualTo(tableId)));

    if (!displayEntityList.isEmpty()) {
      for (MDisplayMetaEntity displayEntity : displayEntityList) {
        slaveControlTableMapper.truncateTable(displayEntity.getDisplayId());
      }
    }

    return MessageResponse.newInfoMessage("数据表数据清空操作成功。");
  }

  public BaseResponse selectTableList(TableCtrlCondition condition) {
    ListResponse<TableCtrlRecord> response = new ListResponse<>();
    tableCtrlMapper.selectTableListWithPage(condition, response, e -> {
      TableCtrlRecord record = new TableCtrlRecord();
      record.setTableId(e.getTableId());
      record.setTableName(e.getTableName());
      record.setMatchStatus(e.getMatchStatus());
      record.setMatchStatusName(MatchStatus.statusName(e.getMatchStatus()));
      record.setCreateTime(e.getCreatedAt());
      record.setCreateUser(e.getUserName());
      record.setRemark(e.getRemark());
      return record;
    });
    return response;
  }

  public BaseResponse selectTableDataList(String tableId, TableDataCondition condition) {
    ListResponse<Map<String, Object>> response = new ListResponse<>();

    Optional<MTableMetaEntity> optEntity = mTableMetaMapper.selectOne(
      c -> c.where().and(MTableMetaNames.tableId, SqlBuilder.isEqualTo(tableId))
        .and(MTableMetaNames.projectId, SqlBuilder.isEqualTo(SecuritySupport.getCurrentProject())));
    if (!optEntity.isPresent()) {
      return MessageResponse.newErrorMessage("数据表不存在，请返回一览页面刷新后再进行操作。").custom();
    }
    MTableMetaEntity entity = optEntity.get();
    String tableMeta = entity.getTableMeta();

    List<MDataInsertHistoryEntity> allHistoryList = mDataInsertHistoryMapper.select(
      c -> c.where().and(MDataInsertHistoryNames.tableId, SqlBuilder.isEqualTo(tableId)));

    Map<String, MDataInsertHistoryEntity> historyMap = allHistoryList.stream()
      .collect(Collectors.toMap(MDataInsertHistoryEntity::getImportKey, e -> e));

    Set<SelectOption> tagsOptions = new HashSet<>();

    allHistoryList.forEach(e -> {
      String importMetaText = e.getImportMeta();
      ImportMeta importMeta = JSONObject.parseObject(importMetaText, ImportMeta.class);
      if (importMeta != null) {
        String[] tags = importMeta.getTags();
        if (tags != null) {
          for (String tag : tags) {
            tagsOptions.add(new SelectOption(tag, tag));
          }
        }
      }
    });

    response.putData("tableName", entity.getTableName());
    response.putData("meta", tableMeta);
    response.putData("history", historyMap);
    response.putData("tagsOptions", tagsOptions);
    if (condition.isInit()) {
      response.putData("areaOptions", pulldownComponent.townOptions());
    }

    if (allHistoryList.isEmpty()) {
      return response.copyPagination(condition);
    }

    DataImportHistoryParam param = new DataImportHistoryParam();
    param.setTableId(tableId);

    FilterModel filter = condition.getFilter();

    FilterKeyResult filterKeyResult = sqlCommonComponent.getFilterKey(tableId, filter,
      allHistoryList.stream().map(MDataInsertHistoryEntity::getImportKey).collect(Collectors.toList()));
    List<String> keyList = filterKeyResult.getKeys();
    if (filterKeyResult.isHasHistoryFilter() && keyList.isEmpty()) {
      return response.copyPagination(condition);
    }

    TableDataSelectParam selectParam = new TableDataSelectParam();
    selectParam.setTableId(tableId);
    if (!ListUtils.isEmpty(keyList)) {
      selectParam.setKeys(keyList);
    }

    if (UserType.DISTRICT.is(SecuritySupport.getUserType())) {
      selectParam.setDistrict(SecuritySupport.getDistrict());
    }

    if (UserType.STREET.is(SecuritySupport.getUserType())) {
      selectParam.setDistrict(SecuritySupport.getDistrict());
      selectParam.setTown(SecuritySupport.getTown());
    }

    if (filter != null) {
      if ((SecuritySupport.isAdmin() || SecuritySupport.isCityManager()) && filter.getArea() != null
        && filter.getArea().length > 0) {
        String district = filter.getArea()[0];
        if ("空白".equals(district)) {
          selectParam.setDistrict("BLANK");
          selectParam.setTown("BLANK");
        } else {
          selectParam.setDistrict(district);
          if (filter.getArea().length > 1) {
            selectParam.setTown(filter.getArea()[1]);
          }
        }
      }

      if ("1".equals(filter.getItemFilterMode())) {
        List<ItemFilters> itemFilters = filter.getItemFilters();
        if (ListUtils.isNotEmpty(itemFilters)) {
          selectParam.setItemFilters(SqlUtils.cleanFilter(itemFilters));
        }
      } else {
        // TODO
      }
    }

    selectParam.setPagination(condition.getPagination());
    selectParam.setSorts(condition.getSorts());
    selectParam.setDeleted(condition.isDeleted());

    try {
      slaveControlTableMapper.selectDataListWithPage(selectParam, response, (data) -> {
        Map<String, Object> dataMap = new HashMap<>();
        String importKey = data.getImportKey();
        String content = data.getContent();

        dataMap.putAll(JSON.parseObject(content, Map.class));
        dataMap.put("id", data.getId());
        dataMap.put("importKey", importKey);

        dataMap.put("city", data.getCity());
        dataMap.put("district", data.getDistrict());
        dataMap.put("town", data.getTown());
        dataMap.put("road", data.getRoad());
        return dataMap;
      });
    } catch (Exception e) {
      logger.error(e.getMessage(), e);
      return MessageResponse.newErrorMessage("检索失败！（推测原因：过滤条件设置有误。）");
    }

    return response;
  }

  @Transactional
  public BaseResponse importTableData(String tableId, ImportTableDataDto dto) {

    Optional<MTableMetaEntity> optEntity = mTableMetaMapper.selectOne(
      c -> c.where().and(MTableMetaNames.tableId, SqlBuilder.isEqualTo(tableId))
        .and(MTableMetaNames.projectId, SqlBuilder.isEqualTo(SecuritySupport.getCurrentProject())));
    if (!optEntity.isPresent()) {
      return MessageResponse.newErrorMessage("数据表不存在，请确认后再进行操作。");
    }
    MTableMetaEntity entity = optEntity.get();
    String tableMeta = entity.getTableMeta();
    List<TableColumnMeta> columnMetaList = JSONArray.parseArray(tableMeta, TableColumnMeta.class);

    Map<String, String> columnMap = columnMetaList.stream()
      .collect(Collectors.toMap(TableColumnMeta::getName, TableColumnMeta::getId));

    Set<String> notNullSet = columnMetaList.stream().filter(e -> !e.isNullAble()).map(TableColumnMeta::getName)
      .collect(Collectors.toSet());

    TableColumnMeta addressColumn = columnMetaList.stream().filter(e -> e.getAddress() == 1).findFirst()
      .orElseThrow(() -> new BusinessException("数据表中没有设置区域字段。"));

    // 一次导入数据有相同的key，通过key，跟导入履历表进行mapping。
    String importKey = SqlUtils.generateKey(5);
    LocalDateTime importTime = LocalDateTime.now();

    // header check。
    Function<Map<Integer, String>, Boolean> headerCheck = (headerMap) -> {
      Collection<String> values = headerMap.values();
      List<String> collect = notNullSet.stream().filter(e -> !values.contains(e)).collect(Collectors.toList());
      if (!collect.isEmpty()) {
        logger.warn("data import error. not match filed: {}", String.join(",", collect));
      }
      return notNullSet.stream().anyMatch(e -> !values.contains(e));
    };

    Map<String, String> areaMap = new HashMap<>();
    // read excel with check header.
    ExcelData excelData = excelComponent.readExcelWithHeaderCheck(dto.getFile(), 200, headerCheck, rowList -> {

      List<SlaveTableEntity> valueList = new ArrayList<>();
      rowList.forEach(row -> {
        Map<String, String> dataMap = new TreeMap<>();
        row.getData().forEach((k, v) -> {
          String field = columnMap.get(k);
          if (field != null) {
            dataMap.put(field, v);
          }
        });

        String addressText = dataMap.get(addressColumn.getId());
        String addressMd5 = StringUtils.encodeByMD5(addressText);
        areaMap.put(addressMd5, addressText);

        String content = JSON.toJSONString(dataMap);
        SlaveTableEntity slaveEntity = new SlaveTableEntity();
        slaveEntity.setMd5(StringUtils.encodeByMD5(content));
        slaveEntity.setImportKey(importKey);
        slaveEntity.setContent(content);
        slaveEntity.setAreaId(addressMd5);
        slaveEntity.setDeleted(false);
        slaveEntity.setCreatedBy(SecuritySupport.getLoginUserId());
        slaveEntity.setUpdatedBy(SecuritySupport.getLoginUserId());
        slaveEntity.setUpdatedTime(LocalDateTime.now());
        valueList.add(slaveEntity);
      });
      slaveControlTableMapper.insertMultiple(entity.getTableId(), valueList);
    });

    if (excelData.isHeaderError()) {
      return MessageResponse.newErrorMessage("Excel文件与数据表结构不一致，请确认后再导入。");
    }
    if (excelData.isNodataError()) {
      return MessageResponse.newErrorMessage("Excel文件没有数据，请确认后再导入。");
    }

    MDataInsertHistoryEntity historyEntity = new MDataInsertHistoryEntity();
    historyEntity.setTableId(tableId);
    historyEntity.setImportKey(importKey);
    historyEntity.setImportType(ImportType.IMPORT_FROM_FILE.type());
    historyEntity.setImportTime(importTime);
    historyEntity.setCreatedAt(LocalDateTime.now());
    historyEntity.setImportMeta(JSON.toJSONString(new ImportMeta(dto.getTags())));
    mDataInsertHistoryMapper.insert(historyEntity);

    applicationEventPublisher.publishEvent(new AreaMatchEvent(tableId, areaMap));
    applicationEventPublisher.publishEvent(new AnalyticsEvent(tableId));

    return MessageResponse.newInfoMessage("数据导入成功。");
  }

  public BaseResponse matchArea(String tableId) {
    Optional<MTableMetaEntity> optEntity = mTableMetaMapper.selectOne(
      c -> c.where().and(MTableMetaNames.tableId, SqlBuilder.isEqualTo(tableId))
        .and(MTableMetaNames.projectId, SqlBuilder.isEqualTo(SecuritySupport.getCurrentProject())));
    if (!optEntity.isPresent()) {
      return MessageResponse.newErrorMessage("数据表不存在，请确认后再进行操作。");
    }

    MTableMetaEntity entity = optEntity.get();
    String matchStatus = entity.getMatchStatus();
    if (MatchStatus.PROCESS_ING.is(matchStatus)) {
      return MessageResponse.newErrorMessage("地区匹配正在进行中。");
    }
    String dataTableId = entity.getTableId();
    String tableMeta = entity.getTableMeta();
    List<TableColumnMeta> columnMetaList = JSONArray.parseArray(tableMeta, TableColumnMeta.class);
    TableColumnMeta areaColumnMeta = columnMetaList.stream().filter(e -> e.getAddress() == 1).findFirst()
      .orElseThrow(null);

    List<NeedMatchDataEntity> dataList = tableCtrlMapper.selectNeedMatchData(dataTableId, areaColumnMeta.getId());

    TableCtrlRecord record = new TableCtrlRecord();
    if (!dataList.isEmpty()) {
      Map<String, String> areaMap = new HashMap<>();
      for (NeedMatchDataEntity matchData : dataList) {
        areaMap.put(matchData.getAreaId(), matchData.getAddress());
      }
      applicationEventPublisher.publishEvent(new AreaMatchEvent(tableId, areaMap));

      record.setMatchStatus(MatchStatus.PROCESS_ING.status());
      record.setMatchStatusName(MatchStatus.PROCESS_ING.statusName());
    } else {
      record.setMatchStatus(MatchStatus.COMPLETE.status());
      record.setMatchStatusName(MatchStatus.COMPLETE.statusName());
    }
    return new DataResponse<>().setValue(record);
  }

  @Transactional
  public BaseResponse editTableData(String tableId, EditTableDataDto dto) {

    Optional<MTableMetaEntity> optMetaEntity = mTableMetaMapper.selectByPrimaryKey(tableId);
    if (!optMetaEntity.isPresent()) {
      return MessageResponse.newErrorMessage("数据表不存在，请确认后再进行操作。");
    }
    MTableMetaEntity metaEntity = optMetaEntity.get();
    String tableMeta = metaEntity.getTableMeta();
    List<TableColumnMeta> columnMetaList = JSONArray.parseArray(tableMeta, TableColumnMeta.class);

    Map<String, Object> dataMap = new TreeMap<>();
    columnMetaList.forEach(m -> {
      String columnId = m.getId();
      dataMap.put(columnId, dto.getData().get(columnId));
    });
    String content = JSON.toJSONString(dataMap);

    String md5 = StringUtils.encodeByMD5(content);

    Optional<SlaveTableEntity> optSlaveEntity = slaveControlTableMapper.selectOne(tableId, dto.getRowId());
    if (!optSlaveEntity.isPresent()) {
      return MessageResponse.newErrorMessage("该数据不存在，请刷新主页面后再进行编辑操作。");
    }
    SlaveTableEntity salveEntity = optSlaveEntity.get();
    if (md5.equals(salveEntity.getMd5())) {
      return new BaseResponse();
    }

    String oldContent = salveEntity.getContent();
    Map<String, Object> oldMap = JSON.parseObject(oldContent, Map.class);

    // Map<String, DiffCellValue> diffMap = diff(dataMap, oldMap);

    salveEntity.setMd5(md5);
    salveEntity.setContent(content);

    slaveControlTableMapper.updateByPrimaryKey(tableId, salveEntity);

//    MDataEditHistoryEntity editHistoryEntity = new MDataEditHistoryEntity();
//    editHistoryEntity.setTableId(tableId);
//    editHistoryEntity.setRecordId(dto.getRowId());
//    editHistoryEntity.setEditInfo(JSON.toJSONString(diffMap));
//
//    mDataEditHistoryMapper.insert(editHistoryEntity);

    applicationEventPublisher.publishEvent(new AnalyticsEvent(tableId));

    return MessageResponse.newInfoMessage("数据编辑成功。");
  }

  private Map<String, DiffCellValue> diff(Map<String, Object> dataMap, Map<String, Object> oldMap) {
    Map<String, DiffCellValue> diffMap = new HashMap<>();
    dataMap.forEach((k, v) -> {
      Object ov = oldMap.get(k);
      if (!Objects.equals(v, ov)) {
        diffMap.put(k, new DiffCellValue(ov, v));
      }
    });
    return diffMap;
  }

  @Transactional
  public BaseResponse deleteTableData(String tableId, TableDataParam param) {

    if (param.isDel()) {
      List<Integer> dataIdList = new ArrayList<>();
      for (String dataId : param.getDataIds()) {
        dataIdList.add(Integer.parseInt(dataId));
      }
//      mDataEditHistoryMapper.delete(c -> c.where().and(MDataEditHistoryNames.tableId, SqlBuilder.isEqualTo(tableId))
//        .and(MDataEditHistoryNames.recordId, SqlBuilder.isIn(dataIdList)));
      slaveControlTableMapper.deleteAlwaysTableData(tableId, param.getDataIds());
    } else {
      slaveControlTableMapper.deleteTableData(tableId, param.getDataIds());
    }
    return MessageResponse.newInfoMessage("数据删除成功。");
  }

  public BaseResponse exportTableData(String tableId, ExportTableDataDto dto) {

    Optional<MTableMetaEntity> optMetaEntity = mTableMetaMapper.selectByPrimaryKey(tableId);
    if (!optMetaEntity.isPresent()) {
      return MessageResponse.newErrorMessage("数据表不存在，请确认后再进行操作。");
    }

    MTableMetaEntity metaEntity = optMetaEntity.get();

    String tableMeta = metaEntity.getTableMeta();
    List<TableColumnMeta> columnMetaList = JSONArray.parseArray(tableMeta, TableColumnMeta.class);

    List<String> headers = new ArrayList<>(Arrays.asList("市/区", "街道", "路"));
    List<String> props = new ArrayList<>(Arrays.asList("district", "town", "road"));

    for (TableColumnMeta columnMeta : columnMetaList) {
      if ("1".equals(dto.getColumnType())) {
        headers.add(columnMeta.getName());
        props.add(columnMeta.getId());
      } else {
        List<String> exportColumns = dto.getExportColumns();
        if (exportColumns.contains(columnMeta.getId())) {
          headers.add(columnMeta.getName());
          props.add(columnMeta.getId());
        }
      }
    }

    FilterModel filter = dto.getFilter();
    FilterKeyResult filterKeyResult = sqlCommonComponent.getFilterKey(tableId, filter, null);
    List<String> keys = filterKeyResult.getKeys();

    Map<String, Object> model = new HashMap<>();
    model.put("tableName", metaEntity.getTableName());
    model.put("date", DateUtils.formatLocalDateTime(LocalDateTime.now()));

    String fileId = SqlUtils.generateExportFileId();

    MImportExportHistoryEntity entity = new MImportExportHistoryEntity();
    entity.setFileId(fileId);
    entity.setTableId(tableId);
    entity.setTableMode(TableMode.ORIGIN.getMode());
    entity.setType(ImportExportType.EXPORT.getType());
    entity.setStatus(ImportExportStatus.ING.getStatus());
    entity.setExportFile(dto.getFileName());
    entity.setDataCount(0);
    entity.setExtraSpec("");
    entity.setDeleted(false);
    mImportExportHistoryMapper.insert(entity);

    exportComponent.exportExcel(fileId, tableId, () -> {

      TableDataSelectParam selectParam = new TableDataSelectParam();
      selectParam.setTableId(tableId);
      selectParam.setDeleted(false);
      selectParam.setKeys(keys);
      List<SlaveDataEntity> dataEntityList = new ArrayList<>();
      if (!keys.isEmpty()) {
        dataEntityList = slaveControlTableMapper.selectDataList(selectParam);
      }
      List<Object> dataList = dataEntityList.stream().map(e -> {
          Map map = JSON.parseObject(e.getContent(), Map.class);
          map.put("district", e.getDistrict());
          map.put("town", e.getTown());
          map.put("road", e.getRoad());
          return map;
        })
        .collect(Collectors.toList());

      ExcelCreateModel createModel = new ExcelCreateModel();
      createModel.setFileName(dto.getFileName());
      createModel.setTemplateName("data_export_template");
      createModel.setModel(model);
      createModel.setHeaders(headers);
      createModel.setObjectProps(props);
      createModel.setData(dataList);
      createModel.setTableMode(TableMode.ORIGIN);

      return createModel;
    }, SecuritySupport.getLoginUserId());

    return new BaseResponse();
  }

  public BaseResponse getTableColumnMeta(String tableId) {
    DataResponse<Object> response = new DataResponse<>();
    MTableMetaEntity tableMetaEntity = mTableMetaMapper.selectByPrimaryKey(tableId).orElseThrow(RuntimeException::new);
    String tableMeta = tableMetaEntity.getTableMeta();

    response.putData("columnMeta", tableMeta);
    return response;
  }


  public BaseResponse dataDuplicationCheck(DuplicationCheckCondition condition) {
    ListResponse<Map<String, Object>> response = new ListResponse<>();

    List<String> keys = dataDuplicationKeys(condition);

    condition.setKeys(keys);

    slaveControlTableMapper.selectDuplicationDataWithPage(condition, response, (data) -> {
      Map<String, Object> dataMap = new HashMap<>();
      String importKey = data.getImportKey();
      String content = data.getContent();
      int rowRank = data.getRowRank();
      int count = data.getCount();

      dataMap.putAll(JSON.parseObject(content, Map.class));
      dataMap.put("id", data.getId());
      dataMap.put("importKey", importKey);
      dataMap.put("rowRank", rowRank);
      dataMap.put("count", count);
      return dataMap;
    });

    return response;
  }

  @Transactional
  public BaseResponse deleteDuplicationData(DuplicationCheckCondition condition) {

    List<String> keys = dataDuplicationKeys(condition);
    condition.setKeys(keys);

    int size = slaveControlTableMapper.deleteDuplicationData(condition);
    logger.info("删除重复数据：tableId: {}, size: {}", condition.getTableId(), size);
    return MessageResponse.newInfoMessage("重复数据删除成功！");
  }

  private List<String> dataDuplicationKeys(DuplicationCheckCondition condition) {
    DataImportHistoryParam param = new DataImportHistoryParam();
    param.setTableId(condition.getTableId());

    String[] dateRange = condition.getDateRange();
    boolean hasFilter = false;
    if (StringUtils.isNotEmpty(dateRange)) {
      param.setStartTime(DateUtils.parseLocalDate(dateRange[0]).atTime(LocalTime.MIN));
      param.setEndTime(DateUtils.parseLocalDate(dateRange[1]).atTime(LocalTime.MAX));
      hasFilter = true;
    }

    if (StringUtils.isNotEmpty(condition.getTags())) {
      param.setTags(Arrays.stream(condition.getTags()).map(SqlUtils::jsonParam).collect(Collectors.toList()));
      hasFilter = true;
    }

    List<String> keys = new ArrayList<>();
    if (hasFilter) {
      List<MDataInsertHistoryEntity> historyList = tableCtrlMapper.selectImportHistoryList(param);
      if (!historyList.isEmpty()) {
        keys = historyList.stream().map(MDataInsertHistoryEntity::getImportKey).collect(Collectors.toList());
      }
    }
    return keys;
  }

  public BaseResponse searchEditHistoryData(String tableId, Integer recordId) {
    ListResponse<TableDataEditHistoryDto> response = new ListResponse<>();

    List<DataEditHistoryEntity> entityList = tableCtrlMapper.selectEditHistoryList(tableId, recordId);

    List<TableDataEditHistoryDto> dataList = entityList.stream().map(e -> {
      TableDataEditHistoryDto dto = new TableDataEditHistoryDto();
      dto.setId(e.getId());
      dto.setCreatedTime(DateUtils.formatLocalDateTime(e.getCreatedTime()));
      dto.setCreatedUser(e.getCreatedUser());
      dto.setCreatedUserName(e.getCreatedUserName());
      dto.setEditInfo(e.getEditInfo());
      return dto;
    }).collect(Collectors.toList());
    return response.setValue(dataList);
  }

  public ResponseEntity<byte[]> templateDownload(String template) {

    String fileName = "";
    switch (template) {
      case "dataset_template":
        fileName = "数据集数据列自动生成模板.xlsx";
        break;
      case "score_template":
        fileName = "物业服务实效检查指标模板.xlsx";
        break;
    }
    return excelCreateComponent.excelDownload(template, fileName);
  }

  public BaseResponse getExportHistoryList(String tableId, ExportHistoryCondition condition) {
    ListResponse<ExportHistoryDto> response = new ListResponse<>();

    List<ExportHistoryEntity> entityList = tableCtrlMapper.selectExportHistoryList(tableId, condition.getTableMode());
    List<ExportHistoryDto> dataList = entityList.stream().map(e -> {
      ExportHistoryDto dto = new ExportHistoryDto();
      BeanUtils.copyProperties(e, dto);
      String status = e.getStatus();
      if (ImportExportStatus.ING.is(status)) {
        LocalDateTime createdAt = e.getCreatedAt();
        if (LocalDateTime.now().minusHours(3).isAfter(createdAt)) {
          status = ImportExportStatus.ERR_TO.getStatus();
        }
      }
      dto.setStatus(status);
      dto.setStatusName(ImportExportStatus.getStatusName(status));
      return dto;
    }).collect(Collectors.toList());
    return response.setValue(dataList);
  }

  public ResponseEntity<byte[]> downloadExportedFile(String fileId) {
    Optional<MImportExportHistoryEntity> optEntity = mImportExportHistoryMapper.selectByPrimaryKey(fileId);
    if (!optEntity.isPresent()) {
      return ResponseSupport.messageResponseEntity(MessageResponse.newErrorMessage("文件不存在，请重新导出后再下载。"));
    }
    MImportExportHistoryEntity entity = optEntity.get();

    String fileName = entity.getExportFile();

    File exportedFile = exportComponent.getExportedFile(fileId);
    if (exportedFile == null || !exportedFile.isFile()) {
      return ResponseSupport.messageResponseEntity(MessageResponse.newErrorMessage("文件不存在，请重新导出后再下载。"));
    }

    return excelCreateComponent.downloadFile(exportedFile, fileName);
  }

  public BaseResponse updateArea(UpdateAreaDto dto) {
    String addressMd5 = StringUtils.encodeByMD5(dto.getAddress());
    areaComponent.updateArea(addressMd5, dto.getAddress(), dto.getArea(), dto.getRoad());
    return new BaseResponse();
  }
}
