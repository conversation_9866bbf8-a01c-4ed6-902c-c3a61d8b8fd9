package com.data.datatools.repository.slave.mapper;

import java.sql.JDBCType;
import java.time.LocalDateTime;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public class SlaveTableNames {

  public static final class SlaveTable extends SqlTable {

    public final SqlColumn<Integer> id = column("id", JDBCType.INTEGER);

    public final SqlColumn<String> importKey = column("import_key", JDBCType.VARCHAR);

    public final SqlColumn<String> md5 = column("md5", JDBCType.VARCHAR);

    public final SqlColumn<String> areaId = column("area_id", JDBCType.VARCHAR);

    public final SqlColumn<String> content = column("content", JDBCType.VARCHAR);

    public final SqlColumn<Boolean> deleted = column("deleted", JDBCType.BIT);

    public final SqlColumn<Integer> createdBy = column("created_by", JDBCType.INTEGER);

    public final SqlColumn<String> updatedBy = column("updated_by", JDBCType.VARCHAR);

    public final SqlColumn<LocalDateTime> updatedTime = column("updated_time", JDBCType.TIMESTAMP);

    public SlaveTable(String tableId) {
      super(tableId);
    }
  }
}
