package com.data.datatools.repository.master.mapper;

import static com.data.datatools.repository.master.mapper.MDataInsertHistoryNames.*;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

import com.data.datatools.common.mybatis.CommonField;
import com.data.datatools.common.mybatis.Operator;
import com.data.datatools.common.mybatis.VersionAdd;
import com.data.datatools.repository.master.entity.MDataInsertHistoryEntity;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.insert.render.MultiRowInsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface MDataInsertHistoryMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_data_insert_history")
    BasicColumn[] selectList = BasicColumn.columnList(id, tableId, importKey, importType, importTime, createdAt, createdBy, importMeta);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_data_insert_history")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_data_insert_history")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_data_insert_history")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    int insert(InsertStatementProvider<MDataInsertHistoryEntity> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_data_insert_history")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultiple")
    int insertMultiple(MultiRowInsertStatementProvider<MDataInsertHistoryEntity> multipleInsertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_data_insert_history")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("MDataInsertHistoryEntityResult")
    Optional<MDataInsertHistoryEntity> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_data_insert_history")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="MDataInsertHistoryEntityResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.INTEGER, id=true),
        @Result(column="table_id", property="tableId", jdbcType=JdbcType.CHAR),
        @Result(column="import_key", property="importKey", jdbcType=JdbcType.CHAR),
        @Result(column="import_type", property="importType", jdbcType=JdbcType.INTEGER),
        @Result(column="import_time", property="importTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="created_by", property="createdBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="import_meta", property="importMeta", jdbcType=JdbcType.LONGVARCHAR)
    })
    List<MDataInsertHistoryEntity> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_data_insert_history")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_data_insert_history")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, MDataInsertHistory, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_data_insert_history")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, MDataInsertHistory, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_data_insert_history")
    default int deleteByPrimaryKey(Integer id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_data_insert_history")
    default int insert(MDataInsertHistoryEntity record) {
        CommonField.fill(record);
        return MyBatis3Utils.insert(this::insert, record, MDataInsertHistory, c ->
            c.map(id).toProperty("id")
            .map(tableId).toProperty("tableId")
            .map(importKey).toProperty("importKey")
            .map(importType).toProperty("importType")
            .map(importTime).toProperty("importTime")
            .map(createdAt).toProperty("createdAt")
            .map(createdBy).toProperty("createdBy")
            .map(importMeta).toProperty("importMeta")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_data_insert_history")
    default int insertMultiple(Collection<MDataInsertHistoryEntity> records) {
        CommonField.fill(records);
        return MyBatis3Utils.insertMultiple(this::insertMultiple, records, MDataInsertHistory, c ->
            c.map(id).toProperty("id")
            .map(tableId).toProperty("tableId")
            .map(importKey).toProperty("importKey")
            .map(importType).toProperty("importType")
            .map(importTime).toProperty("importTime")
            .map(createdAt).toProperty("createdAt")
            .map(createdBy).toProperty("createdBy")
            .map(importMeta).toProperty("importMeta")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_data_insert_history")
    default int insertSelective(MDataInsertHistoryEntity record) {
        CommonField.fill(record);
        return MyBatis3Utils.insert(this::insert, record, MDataInsertHistory, c ->
            c.map(id).toPropertyWhenPresent("id", record::getId)
            .map(tableId).toPropertyWhenPresent("tableId", record::getTableId)
            .map(importKey).toPropertyWhenPresent("importKey", record::getImportKey)
            .map(importType).toPropertyWhenPresent("importType", record::getImportType)
            .map(importTime).toPropertyWhenPresent("importTime", record::getImportTime)
            .map(createdAt).toPropertyWhenPresent("createdAt", record::getCreatedAt)
            .map(createdBy).toPropertyWhenPresent("createdBy", record::getCreatedBy)
            .map(importMeta).toPropertyWhenPresent("importMeta", record::getImportMeta)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_data_insert_history")
    default Optional<MDataInsertHistoryEntity> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, MDataInsertHistory, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_data_insert_history")
    default List<MDataInsertHistoryEntity> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, MDataInsertHistory, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_data_insert_history")
    default List<MDataInsertHistoryEntity> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, MDataInsertHistory, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_data_insert_history")
    default Optional<MDataInsertHistoryEntity> selectByPrimaryKey(Integer id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_data_insert_history")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, MDataInsertHistory, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_data_insert_history")
    static UpdateDSL<UpdateModel> updateAllColumns(MDataInsertHistoryEntity record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalTo(record::getId)
                .set(tableId).equalTo(record::getTableId)
                .set(importKey).equalTo(record::getImportKey)
                .set(importType).equalTo(record::getImportType)
                .set(importTime).equalTo(record::getImportTime)
                .set(importMeta).equalTo(record::getImportMeta);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_data_insert_history")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(MDataInsertHistoryEntity record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalToWhenPresent(record::getId)
                .set(tableId).equalToWhenPresent(record::getTableId)
                .set(importKey).equalToWhenPresent(record::getImportKey)
                .set(importType).equalToWhenPresent(record::getImportType)
                .set(importTime).equalToWhenPresent(record::getImportTime)
                .set(importMeta).equalToWhenPresent(record::getImportMeta);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_data_insert_history")
    default int updateByPrimaryKey(MDataInsertHistoryEntity record) {
        return update(c ->
            c.set(tableId).equalTo(record::getTableId)
            .set(importKey).equalTo(record::getImportKey)
            .set(importType).equalTo(record::getImportType)
            .set(importTime).equalTo(record::getImportTime)
            .set(importMeta).equalTo(record::getImportMeta)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_data_insert_history")
    default int updateByPrimaryKeySelective(MDataInsertHistoryEntity record) {
        return update(c ->
            c.set(tableId).equalToWhenPresent(record::getTableId)
            .set(importKey).equalToWhenPresent(record::getImportKey)
            .set(importType).equalToWhenPresent(record::getImportType)
            .set(importTime).equalToWhenPresent(record::getImportTime)
            .set(importMeta).equalToWhenPresent(record::getImportMeta)
            .where(id, isEqualTo(record::getId))
        );
    }
}