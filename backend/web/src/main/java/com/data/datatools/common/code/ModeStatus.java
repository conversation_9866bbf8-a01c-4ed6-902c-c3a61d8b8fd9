package com.data.datatools.common.code;

import java.util.Objects;

public enum ModeStatus {

  OK("0", "正常"),

  NG("1", "异常");

  private final String status;

  private final String name;

  ModeStatus(String status, String name) {
    this.status = status;
    this.name = name;
  }

  public String status() {
    return this.status;
  }

  public String statusName() {
    return this.name;
  }

  public static String status(String status) {
    for (ModeStatus value : ModeStatus.values()) {
      if (Objects.equals(value.status, status)) {
        return value.status;
      }
    }
    return "";
  }

  public static String statusName(String status) {
    for (ModeStatus value : ModeStatus.values()) {
      if (Objects.equals(value.status, status)) {
        return value.name;
      }
    }
    return "-";
  }

  public boolean is(String status) {
    return Objects.equals(this.status, status);
  }
}
