package com.data.datatools.common.base.dao;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.data.datatools.common.base.BaseCondition;
import com.data.datatools.common.base.OrderItem;
import com.data.datatools.common.response.ListResponse;
import com.data.datatools.utils.ListUtils;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

public interface IBasePagingMapper {

  default <E, C extends BaseCondition, M> List<M> selectWithPage(
    C condition, ListResponse<M> response, Function<E, M> convert, Function<C, List<E>> func) {

    List<OrderItem> sorts = condition.getSorts();
    Pagination pagination = condition.getPagination();
    String sortStr = "";
    if (ListUtils.isNotEmpty(sorts)) {
      List<String> sortsList = sorts.stream().map(OrderItem::toString).collect(Collectors.toList());
      sortStr = String.join(",", sortsList);
    }
    try (Page<E> dataPage = PageHelper
      .startPage(pagination.getPageNumber(), pagination.getPageSize(), sortStr)
      .doSelectPage(() -> func.apply(condition))) {
      List<M> collect = dataPage.getResult().stream().map(convert).collect(Collectors.toList());

      response.createPagination(collect, dataPage);

      return collect;
    }
  }
}
