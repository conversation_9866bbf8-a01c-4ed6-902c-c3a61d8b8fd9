package com.data.datatools.web.workbench.table;

import com.data.datatools.common.base.dao.IBasePagingMapper;
import com.data.datatools.common.response.ListResponse;
import com.data.datatools.repository.master.entity.MDataInsertHistoryEntity;
import com.data.datatools.web.workbench.table.dto.TableCtrlCondition;
import com.data.datatools.web.workbench.table.dto.TableCtrlRecord;
import com.data.datatools.web.workbench.table.entity.DataEditHistoryEntity;
import com.data.datatools.web.workbench.table.entity.DataImportHistoryParam;
import com.data.datatools.web.workbench.table.entity.ExportHistoryEntity;
import com.data.datatools.web.workbench.table.entity.NeedMatchDataEntity;
import com.data.datatools.web.workbench.table.entity.TableRecordEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.function.Function;

@Mapper
public interface TableCtrlMapper extends IBasePagingMapper {

  default List<TableCtrlRecord> selectTableListWithPage(TableCtrlCondition condition,
    ListResponse<TableCtrlRecord> response, Function<TableRecordEntity, TableCtrlRecord> convert) {
    return selectWithPage(condition, response, convert, this::selectTableList);
  }

  List<TableRecordEntity> selectTableList(@Param("param") TableCtrlCondition condition);

  List<MDataInsertHistoryEntity> selectImportHistoryList(@Param("param") DataImportHistoryParam param);

  List<DataEditHistoryEntity> selectEditHistoryList(String tableId, Integer recordId);

  List<ExportHistoryEntity> selectExportHistoryList(String tableId, String tableMode);

  List<NeedMatchDataEntity> selectNeedMatchData(String dataTable, String areaColumn);
}
