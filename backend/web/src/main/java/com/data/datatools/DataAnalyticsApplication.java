package com.data.datatools;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication
public class DataAnalyticsApplication {

  private static final Logger logger = LoggerFactory.getLogger(DataAnalyticsApplication.class);

  public static void main(String[] args) {
    SpringApplication.run(DataAnalyticsApplication.class, args);
    String notice = "「「「「「Data-Analytics Application start successfully.」」」」」";
    logger.info(notice);
  }
}
