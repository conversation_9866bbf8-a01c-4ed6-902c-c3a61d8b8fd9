package com.data.datatools.common.listener;

import com.data.datatools.common.mybatis.Operator;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class AnalyticsEvent extends ApplicationEvent {

  private final String tableId;
  private final String displayId;
  private final Operator operator;

  public AnalyticsEvent(String tableId) {
    super("");
    this.tableId = tableId;
    this.displayId = null;
    this.operator = Operator.copy();
  }

  public AnalyticsEvent(String tableId, String displayId) {
    super("");
    this.tableId = tableId;
    this.displayId = displayId;
    this.operator = Operator.copy();
  }
}
