package com.data.datatools.utils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import org.springframework.web.multipart.MultipartFile;

public class FileUtils {

  public static String readMultipartFile(MultipartFile file) {
    if (file.isEmpty()) {
      return "";
    }
    byte[] bytes;
    try {
      bytes = file.getBytes();
    } catch (IOException e) {
      throw new RuntimeException(e);
    }

    return new String(bytes, StandardCharsets.UTF_8);
  }
}
