package com.data.datatools.repository.master.mapper;

import static com.data.datatools.repository.master.mapper.SysAreaShanghaiNames.*;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

import com.data.datatools.common.mybatis.CommonField;
import com.data.datatools.common.mybatis.Operator;
import com.data.datatools.common.mybatis.VersionAdd;
import com.data.datatools.repository.master.entity.SysAreaShanghaiEntity;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.insert.render.MultiRowInsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface SysAreaShanghaiMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_area_shanghai")
    BasicColumn[] selectList = BasicColumn.columnList(id, prov, city, district, town);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_area_shanghai")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_area_shanghai")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_area_shanghai")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    int insert(InsertStatementProvider<SysAreaShanghaiEntity> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_area_shanghai")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultiple")
    int insertMultiple(MultiRowInsertStatementProvider<SysAreaShanghaiEntity> multipleInsertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_area_shanghai")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("SysAreaShanghaiEntityResult")
    Optional<SysAreaShanghaiEntity> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_area_shanghai")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="SysAreaShanghaiEntityResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.INTEGER, id=true),
        @Result(column="prov", property="prov", jdbcType=JdbcType.VARCHAR),
        @Result(column="city", property="city", jdbcType=JdbcType.VARCHAR),
        @Result(column="district", property="district", jdbcType=JdbcType.VARCHAR),
        @Result(column="town", property="town", jdbcType=JdbcType.VARCHAR)
    })
    List<SysAreaShanghaiEntity> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_area_shanghai")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_area_shanghai")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, sysAreaShanghai, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_area_shanghai")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, sysAreaShanghai, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_area_shanghai")
    default int deleteByPrimaryKey(Integer id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_area_shanghai")
    default int insert(SysAreaShanghaiEntity record) {
        CommonField.fill(record);
        return MyBatis3Utils.insert(this::insert, record, sysAreaShanghai, c ->
            c.map(id).toProperty("id")
            .map(prov).toProperty("prov")
            .map(city).toProperty("city")
            .map(district).toProperty("district")
            .map(town).toProperty("town")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_area_shanghai")
    default int insertMultiple(Collection<SysAreaShanghaiEntity> records) {
        CommonField.fill(records);
        return MyBatis3Utils.insertMultiple(this::insertMultiple, records, sysAreaShanghai, c ->
            c.map(id).toProperty("id")
            .map(prov).toProperty("prov")
            .map(city).toProperty("city")
            .map(district).toProperty("district")
            .map(town).toProperty("town")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_area_shanghai")
    default int insertSelective(SysAreaShanghaiEntity record) {
        CommonField.fill(record);
        return MyBatis3Utils.insert(this::insert, record, sysAreaShanghai, c ->
            c.map(id).toPropertyWhenPresent("id", record::getId)
            .map(prov).toPropertyWhenPresent("prov", record::getProv)
            .map(city).toPropertyWhenPresent("city", record::getCity)
            .map(district).toPropertyWhenPresent("district", record::getDistrict)
            .map(town).toPropertyWhenPresent("town", record::getTown)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_area_shanghai")
    default Optional<SysAreaShanghaiEntity> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, sysAreaShanghai, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_area_shanghai")
    default List<SysAreaShanghaiEntity> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, sysAreaShanghai, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_area_shanghai")
    default List<SysAreaShanghaiEntity> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, sysAreaShanghai, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_area_shanghai")
    default Optional<SysAreaShanghaiEntity> selectByPrimaryKey(Integer id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_area_shanghai")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, sysAreaShanghai, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_area_shanghai")
    static UpdateDSL<UpdateModel> updateAllColumns(SysAreaShanghaiEntity record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalTo(record::getId)
                .set(prov).equalTo(record::getProv)
                .set(city).equalTo(record::getCity)
                .set(district).equalTo(record::getDistrict)
                .set(town).equalTo(record::getTown);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_area_shanghai")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(SysAreaShanghaiEntity record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalToWhenPresent(record::getId)
                .set(prov).equalToWhenPresent(record::getProv)
                .set(city).equalToWhenPresent(record::getCity)
                .set(district).equalToWhenPresent(record::getDistrict)
                .set(town).equalToWhenPresent(record::getTown);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_area_shanghai")
    default int updateByPrimaryKey(SysAreaShanghaiEntity record) {
        return update(c ->
            c.set(prov).equalTo(record::getProv)
            .set(city).equalTo(record::getCity)
            .set(district).equalTo(record::getDistrict)
            .set(town).equalTo(record::getTown)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_area_shanghai")
    default int updateByPrimaryKeySelective(SysAreaShanghaiEntity record) {
        return update(c ->
            c.set(prov).equalToWhenPresent(record::getProv)
            .set(city).equalToWhenPresent(record::getCity)
            .set(district).equalToWhenPresent(record::getDistrict)
            .set(town).equalToWhenPresent(record::getTown)
            .where(id, isEqualTo(record::getId))
        );
    }
}