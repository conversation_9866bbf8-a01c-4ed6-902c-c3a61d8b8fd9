package com.data.datatools.web.workbench.model;

import com.alibaba.fastjson.JSON;
import com.data.datatools.common.code.DisplayStatus;
import com.data.datatools.common.code.ModeStatus;
import com.data.datatools.common.code.ModelType;
import com.data.datatools.common.code.OptType;
import com.data.datatools.common.exception.BusinessException;
import com.data.datatools.common.model.SelectOption;
import com.data.datatools.common.response.BaseResponse;
import com.data.datatools.common.response.DataResponse;
import com.data.datatools.common.response.ListResponse;
import com.data.datatools.common.response.MessageResponse;
import com.data.datatools.common.security.SecuritySupport;
import com.data.datatools.component.AiAnalyticsComponent;
import com.data.datatools.component.DownloadComponent;
import com.data.datatools.component.PulldownComponent;
import com.data.datatools.config.security.ResponseSupport;
import com.data.datatools.repository.master.entity.MAiCategoryEntity;
import com.data.datatools.repository.master.entity.MAiModelEntity;
import com.data.datatools.repository.master.entity.MDisplayMetaEntity;
import com.data.datatools.repository.master.entity.MModelMetaEntity;
import com.data.datatools.repository.master.mapper.MAiCategoryMapper;
import com.data.datatools.repository.master.mapper.MDisplayMetaMapper;
import com.data.datatools.repository.master.mapper.MDisplayMetaNames;
import com.data.datatools.repository.master.mapper.MModelMetaMapper;
import com.data.datatools.repository.master.mapper.MModelMetaNames;
import com.data.datatools.utils.CipherUtils;
import com.data.datatools.utils.DateUtils;
import com.data.datatools.utils.FileUtils;
import com.data.datatools.utils.SqlUtils;
import com.data.datatools.utils.StringUtils;
import com.data.datatools.web.workbench.display.dto.MatchFieldColumn;
import com.data.datatools.web.workbench.display.entity.DisplayMeta;
import com.data.datatools.web.workbench.model.dto.AiModelColumnMetaDto;
import com.data.datatools.web.workbench.model.dto.AiModelRuleMetaDto;
import com.data.datatools.web.workbench.model.dto.ImportModelDto;
import com.data.datatools.web.workbench.model.dto.ModelColumnMetaDto;
import com.data.datatools.web.workbench.model.dto.ModelListCondition;
import com.data.datatools.web.workbench.model.dto.ModelListRecord;
import com.data.datatools.web.workbench.model.dto.ModelMetaData;
import com.data.datatools.web.workbench.model.dto.ModelMetaDto;
import com.data.datatools.web.workbench.model.entity.AiModelColumnMeta;
import com.data.datatools.web.workbench.model.entity.AiModelRuleMeta;
import com.data.datatools.web.workbench.model.entity.ModelColumnMeta;
import com.data.datatools.web.workbench.model.entity.ModelMeta;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.mybatis.dynamic.sql.SqlBuilder;
import org.mybatis.dynamic.sql.select.CountDSL;
import org.mybatis.dynamic.sql.select.SelectModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
public class ModelService {

  @Autowired
  private MModelMetaMapper mModelMetaMapper;

  @Autowired
  private MAiCategoryMapper mAiCategoryMapper;

  @Autowired
  private ModelMapper modelMapper;

  @Autowired
  private MDisplayMetaMapper mDisplayMetaMapper;

  @Autowired
  private DownloadComponent downloadComponent;

  @Autowired
  private PulldownComponent pulldownComponent;

  @Autowired
  private AiAnalyticsComponent aiAnalyticsComponent;

  public BaseResponse searchModelList(ModelListCondition condition) {
    ListResponse<ModelListRecord> response = new ListResponse<>();
    modelMapper.selectModelListWithPage(condition, response, e -> {
      ModelListRecord record = new ModelListRecord();
      record.setModelId(e.getModelId());
      record.setModelName(e.getModelName());
      record.setModelType(e.getModelType());
      record.setModelTypeName(ModelType.typeName(e.getModelType()));
      record.setStatus(e.getStatus());
      record.setAiModelName(e.getAiModelName());
      record.setProjectId(e.getProjectId());
      record.setUserName(e.getUserName());
      record.setRemark(e.getRemark());
      record.setVersion(e.getVersion());
      record.setCreateTime(DateUtils.formatLocalDateTime(e.getCreatedAt()));

      return record;
    });
    return response;
  }

  public BaseResponse saveModel(ModelMetaDto dto) {
    String modelName = dto.getModelName();
    long count = mModelMetaMapper.count(c -> {
      CountDSL<SelectModel>.CountWhereBuilder where = c.where();
      if (OptType.EDIT.is(dto.getOptType())) {
        where.and(MModelMetaNames.modelId, SqlBuilder.isNotEqualTo(dto.getModelId()));
      }
      where.and(MModelMetaNames.projectId, SqlBuilder.isEqualTo(SecuritySupport.getCurrentProject()));
      where.and(MModelMetaNames.modelName, SqlBuilder.isEqualTo(modelName));
      where.and(MModelMetaNames.deleted, SqlBuilder.isEqualTo(false));
      return c;
    });
    if (count > 0) {
      return MessageResponse.newErrorMessage("模型名【{0}】已经存在。", modelName);
    }
    MModelMetaEntity entity;
    if (OptType.EDIT.is(dto.getOptType())) {
      entity = mModelMetaMapper.selectByPrimaryKey(dto.getModelId())
        .orElseThrow(() -> new BusinessException("该数据表已经不存在，请返回一览刷新后再操作。"));
      entity.setVersion(entity.getVersion() + 1);
    } else {
      entity = new MModelMetaEntity();
      String modelId = SqlUtils.generateModelId();
      entity.setModelId(modelId);
      entity.setVersion(0);
    }

    entity.setModelName(modelName);
    entity.setModelType(dto.getModelType());
    if (ModelType.AI.is(dto.getModelType())) {
      entity.setAiModelId(dto.getAiModelId());
    } else {
      entity.setAiModelId(null);
    }
    entity.setProjectId(SecuritySupport.getCurrentProject());
    entity.setRemark(dto.getRemark());
    entity.setDeleted(false);

    ModelMeta modelMeta = new ModelMeta();

    if (ModelType.TAG.is(dto.getModelType())) {
      List<ModelColumnMetaDto> labelList = dto.getLabelList();
      List<ModelColumnMeta> columnMetaList = new ArrayList<>();
      for (int i = 0; i < labelList.size(); i++) {
        ModelColumnMetaDto columnDto = labelList.get(i);
        ModelColumnMeta columnMeta = new ModelColumnMeta();
        columnMeta.setIndex(i + 1);
        columnMeta.setLabelText(columnDto.getLabelText());
        columnMeta.setLabelKeyText(columnDto.getLabelKeyText());
        columnMeta.setBasicWeight(columnDto.getBasicWeight());
        columnMetaList.add(columnMeta);
      }
      modelMeta.setLabelList(columnMetaList);
      modelMeta.setAiLabelList(Collections.emptyList());
      modelMeta.setAiRuleList(Collections.emptyList());
    } else {
      modelMeta.setLabelList(Collections.emptyList());
      List<AiModelColumnMetaDto> aiLabelList = dto.getAiLabelList();
      List<AiModelColumnMeta> aiModelColumnList = new ArrayList<>();
      for (int i = 0; i < aiLabelList.size(); i++) {
        AiModelColumnMetaDto columnDto = aiLabelList.get(i);
        AiModelColumnMeta columnMeta = new AiModelColumnMeta();
        columnMeta.setIndex(i + 1);
        columnMeta.setCategory(columnDto.getCategory());
        columnMeta.setLabelText(columnDto.getLabelText());
        columnMeta.setCaseList(columnDto.getCaseList());
        columnMeta.setRemarkText(columnDto.getRemarkText());
        aiModelColumnList.add(columnMeta);
      }
      modelMeta.setAiLabelList(aiModelColumnList);

      List<AiModelRuleMetaDto> aiRuleList = dto.getAiRuleList();
      List<AiModelRuleMeta> aiModelRuleList = new ArrayList<>();
      for (int i = 0; i < aiRuleList.size(); i++) {
        AiModelRuleMetaDto columnDto = aiRuleList.get(i);
        AiModelRuleMeta columnMeta = new AiModelRuleMeta();
        columnMeta.setIndex(i + 1);
        columnMeta.setRuleText(columnDto.getRuleText());
        aiModelRuleList.add(columnMeta);
      }
      modelMeta.setAiRuleList(aiModelRuleList);
    }

    entity.setModelMeta(JSON.toJSONString(modelMeta));

    boolean warning = false;
    boolean isRunning = false;
    if (ModelType.AI.is(dto.getModelType())) {
      if (!isRunning(dto.getModelId())) {
        boolean result = aiAnalyticsComponent.createDocument(dto.getAiModelId(), modelMeta);
        entity.setStatus(result ? ModeStatus.OK.status() : ModeStatus.NG.status());
        warning = !result;
      } else {
        warning = true;
        isRunning = true;
      }
    } else {
      entity.setStatus(ModeStatus.OK.status());
    }
    if (OptType.EDIT.is(dto.getOptType())) {
      mModelMetaMapper.updateByPrimaryKey(entity);
    } else {
      mModelMetaMapper.insert(entity);
    }

    if (warning) {
      if (isRunning) {
        return MessageResponse.newWarningMessage("数据匹配模型修改成功(有正在匹配运行中的视图使用了该模型，"
          + "AI模型库同步失败，请点击“模型训练”按钮进行同步训练)。");
      }
      return MessageResponse.newWarningMessage(
        OptType.EDIT.is(dto.getOptType()) ? "数据匹配模型修改成功(AI模型库同步失败，请确认)。"
          : "数据匹配模型创建成功(AI模型库同步失败，请确认)");
    } else {
      return MessageResponse.newInfoMessage(
        OptType.EDIT.is(dto.getOptType()) ? "数据匹配模型修改成功。" : "数据匹配模型创建成功");
    }
  }

  private boolean isRunning(String modelId) {
    String projectId = SecuritySupport.getCurrentProject();
    List<MDisplayMetaEntity> displayMetaEntityList = mDisplayMetaMapper.select(
      c -> c.where().and(MDisplayMetaNames.projectId, SqlBuilder.isEqualTo(projectId))
        .and(MDisplayMetaNames.status, SqlBuilder.isEqualTo(DisplayStatus.PROCESS_ING.status())));

    Set<String> modelSet = new HashSet<>();
    displayMetaEntityList.stream().map(MDisplayMetaEntity::getDisplayMeta).forEach(e -> {
      DisplayMeta meta = JSON.parseObject(e, DisplayMeta.class);
      List<MatchFieldColumn> matchColumnList = meta.getMatchColumnList();
      matchColumnList.stream().map(MatchFieldColumn::getModelId).forEach(modelSet::add);
    });

    return modelSet.contains(modelId);
  }

  public BaseResponse refreshModel(String modelId) {
    Optional<MModelMetaEntity> optEntity = mModelMetaMapper.selectByPrimaryKey(modelId);
    if (!optEntity.isPresent()) {
      return MessageResponse.newErrorMessage("该数据模型已经不存在，请刷新后再操作。").custom();
    }

    MModelMetaEntity entity = optEntity.get();

    if (isRunning(modelId)) {
      return MessageResponse.newErrorMessage("有正在匹配运行中的视图使用了该模型，暂时不能进行模型训练。");
    }
    String modelMetaText = entity.getModelMeta();
    ModelMeta modelMeta = JSON.parseObject(modelMetaText, ModelMeta.class);

    boolean result = aiAnalyticsComponent.createDocument(entity.getAiModelId(), modelMeta);
    if (!result) {
      return MessageResponse.newErrorMessage("数据匹配模型训练失败，请确认后再操作。");
    } else {
      return MessageResponse.newInfoMessage("数据匹配模型训练成功。");
    }
  }

  public BaseResponse getModel(String modelId, boolean add) {
    DataResponse<ModelMetaDto> response = new DataResponse<>();
    response.putData("modelTypeOptions", pulldownComponent.modelTypeOptions());
    List<MAiModelEntity> modelEntityList = modelMapper.selectAiModelList(add ? null : modelId);
    response.putData("aiModelOptions", modelEntityList.stream().map(e -> new SelectOption(e.getModelId(), e.getName()))
      .collect(Collectors.toList()));
    List<MAiCategoryEntity> categoryEntityList = mAiCategoryMapper.select(c -> c);
    response.putData("aiCategoryOptions", categoryEntityList.stream().map(MAiCategoryEntity::getCategory).distinct()
      .map(e -> new SelectOption(e, e)).collect(Collectors.toList()));
    if (add) {
      return response;
    }
    Optional<MModelMetaEntity> optEntity = mModelMetaMapper.selectByPrimaryKey(modelId);
    if (!optEntity.isPresent()) {
      return MessageResponse.newErrorMessage("该数据模型已经不存在，请返回一览刷新后再操作。").custom();
    }
    MModelMetaEntity entity = optEntity.get();
    ModelMetaDto dto = new ModelMetaDto();
    dto.setModelId(entity.getModelId());
    dto.setModelName(entity.getModelName());
    dto.setModelType(entity.getModelType());
    dto.setAiModelId(entity.getAiModelId());
    dto.setRemark(entity.getRemark());
    String modelMetaText = entity.getModelMeta();

    dto.setLabelList(Lists.newArrayList());
    dto.setAiLabelList(Lists.newArrayList());
    dto.setAiRuleList(Lists.newArrayList());
    if (StringUtils.isNotEmpty(modelMetaText)) {
      if (ModelType.TAG.is(entity.getModelType())) {
        List<ModelColumnMeta> columnMetaList = SqlUtils.getLabelMetaList(entity);
        List<ModelColumnMetaDto> metaDtoList = columnMetaList.stream().map(e -> {
          ModelColumnMetaDto columnMeta = new ModelColumnMetaDto();
          columnMeta.setIndex(e.getIndex());
          columnMeta.setLabelText(e.getLabelText());
          if (SecuritySupport.isAdmin()) {
            columnMeta.setLabelKeyText(e.getLabelKeyText());
          }
          columnMeta.setBasicWeight(e.getBasicWeight());
          columnMeta.setErrors(Maps.newHashMap());
          return columnMeta;
        }).collect(Collectors.toList());
        dto.setLabelList(metaDtoList);
      } else {

        ModelMeta modelMeta = JSON.parseObject(modelMetaText, ModelMeta.class);
        List<AiModelColumnMetaDto> aiMetaLableDtoList = modelMeta.getAiLabelList().stream().map(e -> {
          AiModelColumnMetaDto columnMeta = new AiModelColumnMetaDto();
          columnMeta.setIndex(e.getIndex());
          columnMeta.setCategory(e.getCategory());
          columnMeta.setLabelText(e.getLabelText());
          columnMeta.setCaseList(e.getCaseList());
          columnMeta.setRemarkText(e.getRemarkText());
          columnMeta.setErrors(Maps.newHashMap());
          return columnMeta;
        }).collect(Collectors.toList());
        dto.setAiLabelList(aiMetaLableDtoList);

        List<AiModelRuleMetaDto> aiMetaRuleDtoList = modelMeta.getAiRuleList().stream().map(e -> {
          AiModelRuleMetaDto columnMeta = new AiModelRuleMetaDto();
          columnMeta.setIndex(e.getIndex());
          columnMeta.setRuleText(e.getRuleText());
          columnMeta.setErrors(Maps.newHashMap());
          return columnMeta;
        }).collect(Collectors.toList());
        dto.setAiRuleList(aiMetaRuleDtoList);
      }
    }

    return response.setValue(dto);
  }

  public BaseResponse deleteModel(String modelId) {
    Optional<MModelMetaEntity> optEntity = mModelMetaMapper.selectByPrimaryKey(modelId);
    if (!optEntity.isPresent()) {
      return MessageResponse.newErrorMessage("该数据模型已经不存在，请刷新后再操作。");
    }
    if (isRunning(modelId)) {
      return MessageResponse.newErrorMessage("该数据模型正在视图中使用，不能删除。");
    }
    mModelMetaMapper.deleteByPrimaryKey(modelId);
    return MessageResponse.newInfoMessage("删除成功。");
  }

  public ResponseEntity<byte[]> exportModel(String modelId) {
    Optional<MModelMetaEntity> optEntity = mModelMetaMapper.selectByPrimaryKey(modelId);
    if (!optEntity.isPresent()) {
      return ResponseSupport.messageResponseEntity(
        MessageResponse.newErrorMessage("该数据模型已经不存在，请刷新后再操作。"));
    }

    MModelMetaEntity entity = optEntity.get();

    ModelMetaData modelMeta = new ModelMetaData();
    modelMeta.setModelName(entity.getModelName());
    modelMeta.setModelType(entity.getModelType());
    modelMeta.setModelMeta(entity.getModelMeta());

    String modelContent = JSON.toJSONString(modelMeta);

    return downloadComponent.downLoad(CipherUtils.encrypt(modelContent).getBytes(), entity.getModelName() + ".MOD",
      null);
  }

  public BaseResponse importModel(ImportModelDto dto) {
    String modeName = dto.getModeName();

    long count = mModelMetaMapper.count(c -> c.where().and(MModelMetaNames.modelName, SqlBuilder.isEqualTo(modeName))
      .and(MModelMetaNames.projectId, SqlBuilder.isEqualTo(SecuritySupport.getCurrentProject())));
    if (count > 0) {
      return MessageResponse.newErrorMessage("输入的模型名称已经存在，请修改后再导入。");
    }

    MultipartFile file = dto.getFile();
    String content = FileUtils.readMultipartFile(file);

    try {
      content = CipherUtils.decrypt(content);
    } catch (Exception e) {
      return MessageResponse.newErrorMessage("模型文件内容非法，请确认后再导入。");
    }
    ModelMetaData modelMeta = JSON.parseObject(content, ModelMetaData.class);

    MModelMetaEntity entity = new MModelMetaEntity();
    String modelId = SqlUtils.generateModelId();
    entity.setModelId(modelId);
    entity.setVersion(0);
    entity.setModelName(modeName);
    entity.setModelType(modelMeta.getModelType());
    entity.setProjectId(SecuritySupport.getCurrentProject());
    entity.setRemark("");
    entity.setDeleted(false);
    entity.setModelMeta(modelMeta.getModelMeta());

    mModelMetaMapper.insert(entity);

    return MessageResponse.newInfoMessage("模型导入成功。");
  }
}
