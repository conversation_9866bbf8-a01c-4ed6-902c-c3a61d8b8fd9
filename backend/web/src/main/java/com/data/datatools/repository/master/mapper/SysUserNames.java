package com.data.datatools.repository.master.mapper;

import java.sql.JDBCType;
import java.time.LocalDateTime;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class SysUserNames {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_user")
    public static final SysUser sysUser = new SysUser();

    /**
     * 用户ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_user.user_id")
    public static final SqlColumn<String> userId = sysUser.userId;

    /**
     * 用户姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_user.user_name")
    public static final SqlColumn<String> userName = sysUser.userName;

    /**
     * 用户邮箱
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_user.user_email")
    public static final SqlColumn<String> userEmail = sysUser.userEmail;

    /**
     * 密码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_user.password")
    public static final SqlColumn<String> password = sysUser.password;

    /**
     * 用户状态
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_user.password_status")
    public static final SqlColumn<String> passwordStatus = sysUser.passwordStatus;

    /**
     * 密码变更时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_user.password_updated_at")
    public static final SqlColumn<LocalDateTime> passwordUpdatedAt = sysUser.passwordUpdatedAt;

    /**
     * 当前项目
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_user.current_project")
    public static final SqlColumn<String> currentProject = sysUser.currentProject;

    /**
     * URL TOKEN
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_user.token")
    public static final SqlColumn<String> token = sysUser.token;

    /**
     * 认证失败回数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_user.login_wrong_count")
    public static final SqlColumn<Integer> loginWrongCount = sysUser.loginWrongCount;

    /**
     * 用户区分
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_user.user_type")
    public static final SqlColumn<String> userType = sysUser.userType;

    /**
     * 省
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_user.prov")
    public static final SqlColumn<String> prov = sysUser.prov;

    /**
     * 市
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_user.city")
    public static final SqlColumn<String> city = sysUser.city;

    /**
     * 区
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_user.district")
    public static final SqlColumn<String> district = sysUser.district;

    /**
     * 镇
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_user.town")
    public static final SqlColumn<String> town = sysUser.town;

    /**
     * 街道
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_user.road")
    public static final SqlColumn<String> road = sysUser.road;

    /**
     * 备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_user.remark")
    public static final SqlColumn<String> remark = sysUser.remark;

    /**
     * 最后登录时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_user.login_time")
    public static final SqlColumn<String> loginTime = sysUser.loginTime;

    /**
     * 最后登录IP
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_user.login_ip")
    public static final SqlColumn<String> loginIp = sysUser.loginIp;

    /**
     * 删除标志:0：未删除，1：删除
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_user.deleted")
    public static final SqlColumn<Boolean> deleted = sysUser.deleted;

    /**
     * 插入时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_user.created_at")
    public static final SqlColumn<LocalDateTime> createdAt = sysUser.createdAt;

    /**
     * 做成者
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_user.created_by")
    public static final SqlColumn<String> createdBy = sysUser.createdBy;

    /**
     * 更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_user.updated_at")
    public static final SqlColumn<LocalDateTime> updatedAt = sysUser.updatedAt;

    /**
     * 更新者
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_user.updated_by")
    public static final SqlColumn<String> updatedBy = sysUser.updatedBy;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_user")
    public static final class SysUser extends SqlTable {
        public final SqlColumn<String> userId = column("user_id", JDBCType.VARCHAR);

        public final SqlColumn<String> userName = column("user_name", JDBCType.VARCHAR);

        public final SqlColumn<String> userEmail = column("user_email", JDBCType.VARCHAR);

        public final SqlColumn<String> password = column("password", JDBCType.VARCHAR);

        public final SqlColumn<String> passwordStatus = column("password_status", JDBCType.CHAR);

        public final SqlColumn<LocalDateTime> passwordUpdatedAt = column("password_updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<String> currentProject = column("current_project", JDBCType.CHAR);

        public final SqlColumn<String> token = column("token", JDBCType.VARCHAR);

        public final SqlColumn<Integer> loginWrongCount = column("login_wrong_count", JDBCType.INTEGER);

        public final SqlColumn<String> userType = column("user_type", JDBCType.CHAR);

        public final SqlColumn<String> prov = column("prov", JDBCType.VARCHAR);

        public final SqlColumn<String> city = column("city", JDBCType.VARCHAR);

        public final SqlColumn<String> district = column("district", JDBCType.VARCHAR);

        public final SqlColumn<String> town = column("town", JDBCType.VARCHAR);

        public final SqlColumn<String> road = column("road", JDBCType.VARCHAR);

        public final SqlColumn<String> remark = column("remark", JDBCType.VARCHAR);

        public final SqlColumn<String> loginTime = column("login_time", JDBCType.VARCHAR);

        public final SqlColumn<String> loginIp = column("login_ip", JDBCType.VARCHAR);

        public final SqlColumn<Boolean> deleted = column("deleted", JDBCType.BIT);

        public final SqlColumn<LocalDateTime> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createdBy = column("created_by", JDBCType.VARCHAR);

        public final SqlColumn<LocalDateTime> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updatedBy = column("updated_by", JDBCType.VARCHAR);

        public SysUser() {
            super("sys_user");
        }
    }
}