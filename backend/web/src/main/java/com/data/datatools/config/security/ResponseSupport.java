package com.data.datatools.config.security;

import com.data.datatools.config.SpringContext;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import com.data.datatools.common.response.BaseResponse;
import net.sf.json.JSONObject;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

public class ResponseSupport {

  private ResponseSupport() {
  }

  public static void jsonResponse(HttpServletResponse response, BaseResponse data)
    throws IOException {
    response.setCharacterEncoding("UTF-8");
    response.setContentType("application/json; charset=utf-8");
    response.getWriter().write(JSONObject.fromObject(data).toString());
  }

  public static ResponseEntity<byte[]> messageResponseEntity(BaseResponse response) {
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    byte[] bytes = toBytes(response);
    return new ResponseEntity<>(bytes, headers, HttpStatus.OK);
  }

  public static byte[] toBytes(Object jsonObject) {
    try {
      ObjectMapper objectMapper = SpringContext.getBean(ObjectMapper.class);
      return objectMapper.writeValueAsBytes(jsonObject);
    } catch (JsonProcessingException e) {
      throw new RuntimeException(e);
    }
  }
}
