package com.data.datatools.web.auth;

import com.data.datatools.common.response.BaseResponse;
import com.data.datatools.common.response.DataResponse;
import com.data.datatools.utils.DateUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

/**
 * Authentication.
 */
@RestController
public class LoginAuthController {

  @Value("${build.version}")
  private String buildVersion;

  @Value("${build.timestamp}")
  private String buildTimestamp;

  @GetMapping(path = {"/"})
  public ModelAndView index() {
    return new ModelAndView("/index.html");
  }

  /**
   * healthCheck
   *
   * @return response ok
   */
  @GetMapping(value = {"/health/check"}, produces = {"application/json"})
  public BaseResponse healthCheck() {
    return new BaseResponse();
  }

  /**
   * Session Check
   *
   * @return response ok
   */
  @GetMapping(value = {"/session/check"}, produces = {"application/json"})
  public BaseResponse sessionCheck() {
    return new BaseResponse();
  }

  @GetMapping("/authority")
  @ResponseBody
  public String authority() {
    Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
    if (principal instanceof UserDetails) {
      String username = ((UserDetails) principal).getUsername();
      return "{ \"user\": \"" + username + "\", " + "\"principal\": \"" + principal.toString()
        + "\" }";
    } else {
      String username = principal.toString();
      return "{ \"user\": \"" + username + "\", " + "\"timestamp\": \"" + buildTimestamp + "\" }";
    }
  }

}
