package com.data.datatools.repository.master.mapper;

import static com.data.datatools.repository.master.mapper.SysAuthNames.*;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

import com.data.datatools.common.mybatis.CommonField;
import com.data.datatools.common.mybatis.Operator;
import com.data.datatools.common.mybatis.VersionAdd;
import com.data.datatools.repository.master.entity.SysAuthEntity;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.insert.render.MultiRowInsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface SysAuthMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_auth")
    BasicColumn[] selectList = BasicColumn.columnList(id, authId, authName, authGroupId, showSequence, deleted, createdAt, createdBy, updatedAt, updatedBy);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_auth")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_auth")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_auth")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    int insert(InsertStatementProvider<SysAuthEntity> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_auth")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultiple")
    int insertMultiple(MultiRowInsertStatementProvider<SysAuthEntity> multipleInsertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_auth")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("SysAuthEntityResult")
    Optional<SysAuthEntity> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_auth")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="SysAuthEntityResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.INTEGER, id=true),
        @Result(column="auth_id", property="authId", jdbcType=JdbcType.VARCHAR),
        @Result(column="auth_name", property="authName", jdbcType=JdbcType.VARCHAR),
        @Result(column="auth_group_id", property="authGroupId", jdbcType=JdbcType.VARCHAR),
        @Result(column="show_sequence", property="showSequence", jdbcType=JdbcType.SMALLINT),
        @Result(column="deleted", property="deleted", jdbcType=JdbcType.BIT),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="created_by", property="createdBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="updated_at", property="updatedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_by", property="updatedBy", jdbcType=JdbcType.VARCHAR)
    })
    List<SysAuthEntity> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_auth")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_auth")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, sysAuth, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_auth")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, sysAuth, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_auth")
    default int deleteByPrimaryKey(Integer id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_auth")
    default int insert(SysAuthEntity record) {
        CommonField.fill(record);
        return MyBatis3Utils.insert(this::insert, record, sysAuth, c ->
            c.map(id).toProperty("id")
            .map(authId).toProperty("authId")
            .map(authName).toProperty("authName")
            .map(authGroupId).toProperty("authGroupId")
            .map(showSequence).toProperty("showSequence")
            .map(deleted).toProperty("deleted")
            .map(createdAt).toProperty("createdAt")
            .map(createdBy).toProperty("createdBy")
            .map(updatedAt).toProperty("updatedAt")
            .map(updatedBy).toProperty("updatedBy")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_auth")
    default int insertMultiple(Collection<SysAuthEntity> records) {
        CommonField.fill(records);
        return MyBatis3Utils.insertMultiple(this::insertMultiple, records, sysAuth, c ->
            c.map(id).toProperty("id")
            .map(authId).toProperty("authId")
            .map(authName).toProperty("authName")
            .map(authGroupId).toProperty("authGroupId")
            .map(showSequence).toProperty("showSequence")
            .map(deleted).toProperty("deleted")
            .map(createdAt).toProperty("createdAt")
            .map(createdBy).toProperty("createdBy")
            .map(updatedAt).toProperty("updatedAt")
            .map(updatedBy).toProperty("updatedBy")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_auth")
    default int insertSelective(SysAuthEntity record) {
        CommonField.fill(record);
        return MyBatis3Utils.insert(this::insert, record, sysAuth, c ->
            c.map(id).toPropertyWhenPresent("id", record::getId)
            .map(authId).toPropertyWhenPresent("authId", record::getAuthId)
            .map(authName).toPropertyWhenPresent("authName", record::getAuthName)
            .map(authGroupId).toPropertyWhenPresent("authGroupId", record::getAuthGroupId)
            .map(showSequence).toPropertyWhenPresent("showSequence", record::getShowSequence)
            .map(deleted).toPropertyWhenPresent("deleted", record::getDeleted)
            .map(createdAt).toPropertyWhenPresent("createdAt", record::getCreatedAt)
            .map(createdBy).toPropertyWhenPresent("createdBy", record::getCreatedBy)
            .map(updatedAt).toPropertyWhenPresent("updatedAt", record::getUpdatedAt)
            .map(updatedBy).toPropertyWhenPresent("updatedBy", record::getUpdatedBy)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_auth")
    default Optional<SysAuthEntity> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, sysAuth, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_auth")
    default List<SysAuthEntity> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, sysAuth, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_auth")
    default List<SysAuthEntity> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, sysAuth, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_auth")
    default Optional<SysAuthEntity> selectByPrimaryKey(Integer id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_auth")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, sysAuth, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_auth")
    static UpdateDSL<UpdateModel> updateAllColumns(SysAuthEntity record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalTo(record::getId)
                .set(authId).equalTo(record::getAuthId)
                .set(authName).equalTo(record::getAuthName)
                .set(authGroupId).equalTo(record::getAuthGroupId)
                .set(showSequence).equalTo(record::getShowSequence)
                .set(deleted).equalTo(record::getDeleted)
                .set(updatedAt).equalTo(LocalDateTime.now())
                .set(updatedBy).equalTo(Operator.of());
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_auth")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(SysAuthEntity record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalToWhenPresent(record::getId)
                .set(authId).equalToWhenPresent(record::getAuthId)
                .set(authName).equalToWhenPresent(record::getAuthName)
                .set(authGroupId).equalToWhenPresent(record::getAuthGroupId)
                .set(showSequence).equalToWhenPresent(record::getShowSequence)
                .set(deleted).equalToWhenPresent(record::getDeleted)
                .set(updatedAt).equalTo(LocalDateTime.now())
                .set(updatedBy).equalTo(Operator.of());
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_auth")
    default int updateByPrimaryKey(SysAuthEntity record) {
        return update(c ->
            c.set(authId).equalTo(record::getAuthId)
            .set(authName).equalTo(record::getAuthName)
            .set(authGroupId).equalTo(record::getAuthGroupId)
            .set(showSequence).equalTo(record::getShowSequence)
            .set(deleted).equalTo(record::getDeleted)
            .set(updatedAt).equalTo(LocalDateTime.now())
            .set(updatedBy).equalTo(Operator.of())
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_auth")
    default int updateByPrimaryKeySelective(SysAuthEntity record) {
        return update(c ->
            c.set(authId).equalToWhenPresent(record::getAuthId)
            .set(authName).equalToWhenPresent(record::getAuthName)
            .set(authGroupId).equalToWhenPresent(record::getAuthGroupId)
            .set(showSequence).equalToWhenPresent(record::getShowSequence)
            .set(deleted).equalToWhenPresent(record::getDeleted)
            .set(updatedAt).equalTo(LocalDateTime.now())
            .set(updatedBy).equalTo(Operator.of())
            .where(id, isEqualTo(record::getId))
        );
    }
}