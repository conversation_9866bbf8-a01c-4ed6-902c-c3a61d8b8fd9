package com.data.datatools.repository.master.mapper;

import static com.data.datatools.repository.master.mapper.MImportExportHistoryNames.*;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

import com.data.datatools.common.mybatis.CommonField;
import com.data.datatools.common.mybatis.Operator;
import com.data.datatools.common.mybatis.VersionAdd;
import com.data.datatools.repository.master.entity.MImportExportHistoryEntity;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.insert.render.MultiRowInsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface MImportExportHistoryMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_import_export_history")
    BasicColumn[] selectList = BasicColumn.columnList(fileId, tableId, tableMode, type, status, exportFile, dataCount, extraSpec, deleted, createdAt, createdBy, updatedAt, updatedBy);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_import_export_history")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_import_export_history")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_import_export_history")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    int insert(InsertStatementProvider<MImportExportHistoryEntity> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_import_export_history")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultiple")
    int insertMultiple(MultiRowInsertStatementProvider<MImportExportHistoryEntity> multipleInsertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_import_export_history")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("MImportExportHistoryEntityResult")
    Optional<MImportExportHistoryEntity> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_import_export_history")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="MImportExportHistoryEntityResult", value = {
        @Result(column="file_id", property="fileId", jdbcType=JdbcType.VARCHAR, id=true),
        @Result(column="table_id", property="tableId", jdbcType=JdbcType.CHAR),
        @Result(column="table_mode", property="tableMode", jdbcType=JdbcType.CHAR),
        @Result(column="type", property="type", jdbcType=JdbcType.CHAR),
        @Result(column="status", property="status", jdbcType=JdbcType.CHAR),
        @Result(column="export_file", property="exportFile", jdbcType=JdbcType.VARCHAR),
        @Result(column="data_count", property="dataCount", jdbcType=JdbcType.INTEGER),
        @Result(column="extra_spec", property="extraSpec", jdbcType=JdbcType.VARCHAR),
        @Result(column="deleted", property="deleted", jdbcType=JdbcType.BIT),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="created_by", property="createdBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="updated_at", property="updatedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_by", property="updatedBy", jdbcType=JdbcType.VARCHAR)
    })
    List<MImportExportHistoryEntity> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_import_export_history")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_import_export_history")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, MImportExportHistory, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_import_export_history")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, MImportExportHistory, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_import_export_history")
    default int deleteByPrimaryKey(String fileId_) {
        return delete(c -> 
            c.where(fileId, isEqualTo(fileId_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_import_export_history")
    default int insert(MImportExportHistoryEntity record) {
        CommonField.fill(record);
        return MyBatis3Utils.insert(this::insert, record, MImportExportHistory, c ->
            c.map(fileId).toProperty("fileId")
            .map(tableId).toProperty("tableId")
            .map(tableMode).toProperty("tableMode")
            .map(type).toProperty("type")
            .map(status).toProperty("status")
            .map(exportFile).toProperty("exportFile")
            .map(dataCount).toProperty("dataCount")
            .map(extraSpec).toProperty("extraSpec")
            .map(deleted).toProperty("deleted")
            .map(createdAt).toProperty("createdAt")
            .map(createdBy).toProperty("createdBy")
            .map(updatedAt).toProperty("updatedAt")
            .map(updatedBy).toProperty("updatedBy")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_import_export_history")
    default int insertMultiple(Collection<MImportExportHistoryEntity> records) {
        CommonField.fill(records);
        return MyBatis3Utils.insertMultiple(this::insertMultiple, records, MImportExportHistory, c ->
            c.map(fileId).toProperty("fileId")
            .map(tableId).toProperty("tableId")
            .map(tableMode).toProperty("tableMode")
            .map(type).toProperty("type")
            .map(status).toProperty("status")
            .map(exportFile).toProperty("exportFile")
            .map(dataCount).toProperty("dataCount")
            .map(extraSpec).toProperty("extraSpec")
            .map(deleted).toProperty("deleted")
            .map(createdAt).toProperty("createdAt")
            .map(createdBy).toProperty("createdBy")
            .map(updatedAt).toProperty("updatedAt")
            .map(updatedBy).toProperty("updatedBy")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_import_export_history")
    default int insertSelective(MImportExportHistoryEntity record) {
        CommonField.fill(record);
        return MyBatis3Utils.insert(this::insert, record, MImportExportHistory, c ->
            c.map(fileId).toPropertyWhenPresent("fileId", record::getFileId)
            .map(tableId).toPropertyWhenPresent("tableId", record::getTableId)
            .map(tableMode).toPropertyWhenPresent("tableMode", record::getTableMode)
            .map(type).toPropertyWhenPresent("type", record::getType)
            .map(status).toPropertyWhenPresent("status", record::getStatus)
            .map(exportFile).toPropertyWhenPresent("exportFile", record::getExportFile)
            .map(dataCount).toPropertyWhenPresent("dataCount", record::getDataCount)
            .map(extraSpec).toPropertyWhenPresent("extraSpec", record::getExtraSpec)
            .map(deleted).toPropertyWhenPresent("deleted", record::getDeleted)
            .map(createdAt).toPropertyWhenPresent("createdAt", record::getCreatedAt)
            .map(createdBy).toPropertyWhenPresent("createdBy", record::getCreatedBy)
            .map(updatedAt).toPropertyWhenPresent("updatedAt", record::getUpdatedAt)
            .map(updatedBy).toPropertyWhenPresent("updatedBy", record::getUpdatedBy)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_import_export_history")
    default Optional<MImportExportHistoryEntity> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, MImportExportHistory, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_import_export_history")
    default List<MImportExportHistoryEntity> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, MImportExportHistory, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_import_export_history")
    default List<MImportExportHistoryEntity> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, MImportExportHistory, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_import_export_history")
    default Optional<MImportExportHistoryEntity> selectByPrimaryKey(String fileId_) {
        return selectOne(c ->
            c.where(fileId, isEqualTo(fileId_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_import_export_history")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, MImportExportHistory, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_import_export_history")
    static UpdateDSL<UpdateModel> updateAllColumns(MImportExportHistoryEntity record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(fileId).equalTo(record::getFileId)
                .set(tableId).equalTo(record::getTableId)
                .set(tableMode).equalTo(record::getTableMode)
                .set(type).equalTo(record::getType)
                .set(status).equalTo(record::getStatus)
                .set(exportFile).equalTo(record::getExportFile)
                .set(dataCount).equalTo(record::getDataCount)
                .set(extraSpec).equalTo(record::getExtraSpec)
                .set(deleted).equalTo(record::getDeleted)
                .set(updatedAt).equalTo(LocalDateTime.now())
                .set(updatedBy).equalTo(Operator.of());
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_import_export_history")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(MImportExportHistoryEntity record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(fileId).equalToWhenPresent(record::getFileId)
                .set(tableId).equalToWhenPresent(record::getTableId)
                .set(tableMode).equalToWhenPresent(record::getTableMode)
                .set(type).equalToWhenPresent(record::getType)
                .set(status).equalToWhenPresent(record::getStatus)
                .set(exportFile).equalToWhenPresent(record::getExportFile)
                .set(dataCount).equalToWhenPresent(record::getDataCount)
                .set(extraSpec).equalToWhenPresent(record::getExtraSpec)
                .set(deleted).equalToWhenPresent(record::getDeleted)
                .set(updatedAt).equalTo(LocalDateTime.now())
                .set(updatedBy).equalTo(Operator.of());
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_import_export_history")
    default int updateByPrimaryKey(MImportExportHistoryEntity record) {
        return update(c ->
            c.set(tableId).equalTo(record::getTableId)
            .set(tableMode).equalTo(record::getTableMode)
            .set(type).equalTo(record::getType)
            .set(status).equalTo(record::getStatus)
            .set(exportFile).equalTo(record::getExportFile)
            .set(dataCount).equalTo(record::getDataCount)
            .set(extraSpec).equalTo(record::getExtraSpec)
            .set(deleted).equalTo(record::getDeleted)
            .set(updatedAt).equalTo(LocalDateTime.now())
            .set(updatedBy).equalTo(Operator.of())
            .where(fileId, isEqualTo(record::getFileId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_import_export_history")
    default int updateByPrimaryKeySelective(MImportExportHistoryEntity record) {
        return update(c ->
            c.set(tableId).equalToWhenPresent(record::getTableId)
            .set(tableMode).equalToWhenPresent(record::getTableMode)
            .set(type).equalToWhenPresent(record::getType)
            .set(status).equalToWhenPresent(record::getStatus)
            .set(exportFile).equalToWhenPresent(record::getExportFile)
            .set(dataCount).equalToWhenPresent(record::getDataCount)
            .set(extraSpec).equalToWhenPresent(record::getExtraSpec)
            .set(deleted).equalToWhenPresent(record::getDeleted)
            .set(updatedAt).equalTo(LocalDateTime.now())
            .set(updatedBy).equalTo(Operator.of())
            .where(fileId, isEqualTo(record::getFileId))
        );
    }
}