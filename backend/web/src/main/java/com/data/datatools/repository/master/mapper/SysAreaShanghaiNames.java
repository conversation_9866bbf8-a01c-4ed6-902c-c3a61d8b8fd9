package com.data.datatools.repository.master.mapper;

import java.sql.JDBCType;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class SysAreaShanghaiNames {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_area_shanghai")
    public static final SysAreaShanghai sysAreaShanghai = new SysAreaShanghai();

    /**
     * 主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_area_shanghai.id")
    public static final SqlColumn<Integer> id = sysAreaShanghai.id;

    /**
     * 省
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_area_shanghai.prov")
    public static final SqlColumn<String> prov = sysAreaShanghai.prov;

    /**
     * 市
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_area_shanghai.city")
    public static final SqlColumn<String> city = sysAreaShanghai.city;

    /**
     * 区
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_area_shanghai.district")
    public static final SqlColumn<String> district = sysAreaShanghai.district;

    /**
     * 镇/街道
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_area_shanghai.town")
    public static final SqlColumn<String> town = sysAreaShanghai.town;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_area_shanghai")
    public static final class SysAreaShanghai extends SqlTable {
        public final SqlColumn<Integer> id = column("id", JDBCType.INTEGER);

        public final SqlColumn<String> prov = column("prov", JDBCType.VARCHAR);

        public final SqlColumn<String> city = column("city", JDBCType.VARCHAR);

        public final SqlColumn<String> district = column("district", JDBCType.VARCHAR);

        public final SqlColumn<String> town = column("town", JDBCType.VARCHAR);

        public SysAreaShanghai() {
            super("sys_area_shanghai");
        }
    }
}