package com.data.datatools.web.workbench.display.dto;

import java.util.ArrayList;
import java.util.List;
import lombok.Data;

@Data
public class DisplayTableDebugColumn {

  private String id;

  private String name;

  private List<DisplayTableColumn> columns = new ArrayList<>();

  public DisplayTableDebugColumn(String id, String name) {
    this.id = id;
    this.name = name;
  }

  public DisplayTableDebugColumn addColumn(DisplayTableColumn column) {
    columns.add(column);
    return this;
  }
}
