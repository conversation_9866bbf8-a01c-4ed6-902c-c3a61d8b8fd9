package com.data.datatools.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

@Configuration
public class RestTemplateConfig {

    @Value("${http.client.connect.timeout:30000}")
    private int connectTimeout;

    @Value("${http.client.read.timeout:120000}")
    private int readTimeout;

    @Bean
    public RestTemplate restTemplate() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(connectTimeout);  // 连接超时（可配置，默认30秒）
        factory.setReadTimeout(readTimeout);        // 读取超时（可配置，默认120秒）

        RestTemplate restTemplate = new RestTemplate(factory);
        return restTemplate;
    }
}
