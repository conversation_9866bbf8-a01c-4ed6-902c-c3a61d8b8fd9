package com.data.datatools.web.workbench.table.dto;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ExportHistoryDto {
  /**
   * 文件ID
   */
  private String fileId;

  /**
   * 源数据表ID
   */
  private String tableId;

  /**
   * 模式 0：原始数据 1：算分数据
   */
  private String tableMode;

  /**
   * 类型 0：导入 1：导出
   */
  private String type;

  /**
   * 状态 0：进行中 1：完成 2：失败
   */
  private String status;

  /**
   * 状态 0：进行中 1：完成 2：失败
   */
  private String statusName;

  /**
   * 文件名
   */
  private String exportFile;

  /**
   * 数据件数
   */
  private Integer dataCount;

  /**
   * 额外信息
   */
  private String extraSpec;

  /**
   * 插入时间
   */
  private LocalDateTime createdAt;

  /**
   * 做成者
   */
  private String createdBy;

  private String userName;
}
