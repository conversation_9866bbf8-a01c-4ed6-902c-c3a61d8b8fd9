package com.data.datatools.common.exception;

import lombok.Getter;

@Getter
public class BusinessException extends RuntimeException {

  private final Throwable exception;

  private final String message;

  public BusinessException(String message) {
    super(message);
    this.exception = null;
    this.message = message;
  }

  public BusinessException(Throwable cause, String message) {
    super(cause);
    this.exception = cause;
    this.message = message;
  }
}
