package com.data.datatools.common.response;

import com.data.datatools.common.base.BaseCondition;
import com.github.pagehelper.Page;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.Getter;
import org.springframework.beans.BeanUtils;

@Data
public class ListResponse<T> extends BaseResponse {

  @Getter
  private Pagination pagination = new Pagination();

  @Getter
  private List<T> data;

  @Getter
  private Map<String, Object> dataMap = new HashMap<>();

  public ListResponse() {
    super();
  }

  public ListResponse(String statusCode) {
    super(statusCode);
  }

  public ListResponse<T> putData(String key, Object data) {
    this.dataMap.put(key, data);
    return this;
  }

  public ListResponse<T> setValue(List<T> data) {
    this.data = data;
    return this;
  }

  public <E> void createPagination(List<T> data, Page<E> dataPage) {
    this.data = data;
    pagination.setTotalCount(dataPage.getTotal());
    pagination.setPageNumber(dataPage.getPageNum());
    pagination.setPageSize(dataPage.getPageSize());
  }

  public BaseResponse copyPagination(BaseCondition condition) {
    BeanUtils.copyProperties(condition.getPagination(), pagination);
    return this;
  }

  @Data
  public static class Pagination {

    private Long totalCount;
    private Integer pageNumber;
    private Integer pageSize;
  }
}
