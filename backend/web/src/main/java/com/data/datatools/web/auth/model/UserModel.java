package com.data.datatools.web.auth.model;

import com.data.datatools.common.model.SelectOption;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import com.data.datatools.config.security.model.LoginUser;
import lombok.Getter;
import org.springframework.security.core.GrantedAuthority;

@Getter
public class UserModel {

  private String sessionId;
  private String userId;
  private String userName;
  private String userType;
  private String city;
  private String district;
  private String street;
  private String currentProject;
  private List<SelectOption> projectOptions;
  private List<String> authorities = new ArrayList<>();

  public UserModel(String sessionId, LoginUser user, List<SelectOption> projectOptions) {
    this.sessionId = sessionId;
    this.userId = user.getUserId();
    this.userName = user.getUserName();
    this.currentProject = user.getCurrentProject();
    this.userType = user.getUserType();
    this.city = user.getCity();
    this.district = user.getDistrict();
    this.street = user.getStreet();
    this.projectOptions = projectOptions;

    this.authorities.addAll(user.getAuthorities().stream().map(GrantedAuthority::getAuthority)
      .collect(Collectors.toList()));
  }
}
