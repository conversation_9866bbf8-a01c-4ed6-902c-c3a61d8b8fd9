package com.data.datatools.web.system.role.dto;

import java.util.List;
import lombok.Data;

@Data
public class RoleInfoModel {
  /**
   * 主键
   */
  private Integer id;

  /**
   * 角色ID
   */
  private String roleId;


  /**
   * 角色名称
   */
  private String roleName;

  /**
   * 角色类型：1:个人用；2：组织用
   */
  private String roleType;

  /**
   * 显示顺序
   */
  private String showSequence;

  /**
   * 权限列表
   */
  private List<RoleAuthInfoModel> authList;
}
