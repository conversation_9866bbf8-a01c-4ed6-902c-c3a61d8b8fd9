package com.data.datatools.component;

import com.data.datatools.common.model.FilterModel;
import com.data.datatools.component.dto.meta.FilterKeyResult;
import com.data.datatools.repository.master.entity.MDataInsertHistoryEntity;
import com.data.datatools.repository.master.mapper.MDataInsertHistoryMapper;
import com.data.datatools.repository.master.mapper.MDataInsertHistoryNames;
import com.data.datatools.utils.DateUtils;
import com.data.datatools.utils.SqlUtils;
import com.data.datatools.utils.StringUtils;
import com.data.datatools.web.workbench.table.TableCtrlMapper;
import com.data.datatools.web.workbench.table.entity.DataImportHistoryParam;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import org.mybatis.dynamic.sql.SqlBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class SqlCommonComponent {

  @Autowired
  private TableCtrlMapper tableCtrlMapper;

  @Autowired
  private MDataInsertHistoryMapper mDataInsertHistoryMapper;

  public FilterKeyResult getFilterKey(String tableId, FilterModel filter, List<String> allKeys) {

    FilterKeyResult result = new FilterKeyResult();

    DataImportHistoryParam param = new DataImportHistoryParam();
    param.setTableId(tableId);

    boolean hasHistoryFilter = false;

    if (filter != null) {
      String[] dateRange = filter.getDateRange();
      if (StringUtils.isNotEmpty(dateRange)) {
        param.setStartTime(DateUtils.parseLocalDate(dateRange[0]).atTime(LocalTime.MIN));
        param.setEndTime(DateUtils.parseLocalDate(dateRange[1]).atTime(LocalTime.MAX));
        hasHistoryFilter = true;
      }

      if (StringUtils.isNotEmpty(filter.getTags())) {
        param.setTags(Arrays.stream(filter.getTags()).map(SqlUtils::jsonParam).collect(Collectors.toList()));
        hasHistoryFilter = true;
      }
    }
    result.setHasHistoryFilter(hasHistoryFilter);

    List<String> keys = new ArrayList<>();
    if (hasHistoryFilter) {
      List<MDataInsertHistoryEntity> historyList = tableCtrlMapper.selectImportHistoryList(param);
      if (!historyList.isEmpty()) {
        keys = historyList.stream().map(MDataInsertHistoryEntity::getImportKey).collect(Collectors.toList());
      }
    } else {
      if (allKeys == null) {
        keys = mDataInsertHistoryMapper.select(c -> c.where().and(MDataInsertHistoryNames.tableId,
          SqlBuilder.isEqualTo(tableId))).stream().map(MDataInsertHistoryEntity::getImportKey).collect(
          Collectors.toList());
      } else {
        keys.addAll(allKeys);
      }
    }
    result.setKeys(keys);
    return result;
  }
}
