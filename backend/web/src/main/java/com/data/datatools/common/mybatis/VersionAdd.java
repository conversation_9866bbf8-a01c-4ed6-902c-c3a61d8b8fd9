package com.data.datatools.common.mybatis;

import org.mybatis.dynamic.sql.BindableColumn;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.render.TableAliasCalculator;
import org.mybatis.dynamic.sql.select.function.AbstractUniTypeFunction;

/**
 * DBテーブル更新バージョン自動アップグレード処理用クラスです。
 *
 * @param <T> T
 * <AUTHOR>
 */
public final class VersionAdd<T> extends AbstractUniTypeFunction<T, VersionAdd<T>> {

  protected VersionAdd(BindableColumn<T> column) {
    super(column);
  }

  @Override
  protected VersionAdd<T> copy() {
    return new VersionAdd<T>(column);
  }

  @Override
  public String renderWithTableAlias(TableAliasCalculator tableAliasCalculator) {
    return column.renderWithTableAlias(tableAliasCalculator) + " + 1";
  }

  /**
   * バージョンフィールドの更新処理を行います。
   *
   * @param updateVersion バージョン更新フィールド
   * @return バージョン更新
   */
  public static VersionAdd<Integer> of(SqlColumn<Integer> updateVersion) {
    return new VersionAdd<>(updateVersion);
  }
}
