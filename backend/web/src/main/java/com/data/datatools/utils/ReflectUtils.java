package com.data.datatools.utils;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.util.List;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.apache.commons.lang3.reflect.MethodUtils;

public final class ReflectUtils {

  private ReflectUtils() {
  }

  public static List<Field> getFieldsListWithAnnotation(Class<?> cls, Class<? extends Annotation> annotationCls) {
    return FieldUtils.getFieldsListWithAnnotation(cls, annotationCls);
  }

  public static Field getField(Class<?> cls, String fieldName) {
    return FieldUtils.getField(cls, fieldName, true);
  }

  public static Object getFieldValue(Object target, Field field) {
    try {
      return FieldUtils.readField(target, field.getName(), true);
    } catch (IllegalAccessException e) {
      throw new RuntimeException(e);
    }
  }

  public static <T> T getFieldValue(Object target, Field field, Class<T> clazz) {
    try {
      Object o = FieldUtils.readField(target, field.getName(), true);
      if (o.getClass().isAssignableFrom(clazz)) {
        return (T) o;
      }
      return null;
    } catch (IllegalAccessException e) {
      throw new RuntimeException(e);
    }
  }

  public static void setFieldValue(Object target, String fieldName, Object value) {
    try {
      FieldUtils.writeField(target, fieldName, value, true);
    } catch (IllegalAccessException e) {
      throw new RuntimeException(e);
    }
  }

  public static void invokeMethod(final Object object, final String methodName,
    Object[] args, Class<?>[] parameterTypes) {
    try {
      MethodUtils.invokeMethod(object, true, methodName, args, parameterTypes);
    } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
      throw new RuntimeException(e);
    }
  }
}
