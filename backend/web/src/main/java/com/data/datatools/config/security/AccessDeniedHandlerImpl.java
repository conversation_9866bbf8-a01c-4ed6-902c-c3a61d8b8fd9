package com.data.datatools.config.security;

import com.data.datatools.common.message.MessageConstants;
import java.io.IOException;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import com.data.datatools.common.response.MessageResponse;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;

public class AccessDeniedHandlerImpl implements AccessDeniedHandler {

  @Override
  public void handle(HttpServletRequest request, HttpServletResponse response,
      AccessDeniedException accessDeniedException) throws IOException, ServletException {
    if (!response.isCommitted()) {
      // Set the 403 status code.
      MessageResponse data = MessageResponse.newErrorMessage(MessageConstants.MESSAGE_E0080);

      ResponseSupport.jsonResponse(response, data);
    }
  }
}
