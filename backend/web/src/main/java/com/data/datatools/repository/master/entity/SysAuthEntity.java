package com.data.datatools.repository.master.entity;

import com.data.datatools.common.base.dao.BaseEntity;
import java.io.Serializable;
import java.time.LocalDateTime;
import javax.annotation.Generated;

/**
 * 权限表
 *
 * database table [data_analytics..sys_auth]
 */
public class SysAuthEntity implements Serializable, BaseEntity {
    /**
     * 主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth.id")
    private Integer id;

    /**
     * 权限ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth.auth_id")
    private String authId;

    /**
     * 权限名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth.auth_name")
    private String authName;

    /**
     * 所属的权限分组ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth.auth_group_id")
    private String authGroupId;

    /**
     * 组内显示顺序
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth.show_sequence")
    private Short showSequence;

    /**
     * 是否删除
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth.deleted")
    private Boolean deleted;

    /**
     * 插入时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth.created_at")
    private LocalDateTime createdAt;

    /**
     * 做成者
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth.created_by")
    private String createdBy;

    /**
     * 更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth.updated_at")
    private LocalDateTime updatedAt;

    /**
     * 更新者
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth.updated_by")
    private String updatedBy;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_auth")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth.id")
    public Integer getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth.id")
    public void setId(Integer id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth.auth_id")
    public String getAuthId() {
        return authId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth.auth_id")
    public void setAuthId(String authId) {
        this.authId = authId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth.auth_name")
    public String getAuthName() {
        return authName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth.auth_name")
    public void setAuthName(String authName) {
        this.authName = authName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth.auth_group_id")
    public String getAuthGroupId() {
        return authGroupId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth.auth_group_id")
    public void setAuthGroupId(String authGroupId) {
        this.authGroupId = authGroupId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth.show_sequence")
    public Short getShowSequence() {
        return showSequence;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth.show_sequence")
    public void setShowSequence(Short showSequence) {
        this.showSequence = showSequence;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth.deleted")
    public Boolean getDeleted() {
        return deleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth.deleted")
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth.created_at")
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth.created_at")
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth.created_by")
    public String getCreatedBy() {
        return createdBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth.created_by")
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth.updated_at")
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth.updated_at")
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth.updated_by")
    public String getUpdatedBy() {
        return updatedBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth.updated_by")
    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }
}