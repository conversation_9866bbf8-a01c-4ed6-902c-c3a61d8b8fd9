package com.data.datatools.common.response;

import com.data.datatools.common.constants.ICommonConstants;
import lombok.Data;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public class ValidErrorResponse extends BaseResponse {

  @Getter
  private final List<MessageInner> messageList = new ArrayList<>();

  @Getter
  private String type = "E";

  @Getter
  private boolean custom = false;

  private ValidErrorResponse() {
    super(ICommonConstants.RESPONSE_STATUS_CODE_FAILED_BIZ);
  }

  public static ValidErrorResponse newInstance() {
    return new ValidErrorResponse();
  }

  public void addMessage(String text, List<String> args) {

    String message = text;
    for (int i = 0; i < args.size(); i++) {
      if (message.contains("{" + i + "}")) {
        message = message.replaceAll("\\{" + i + "}", args.get(i));
      }
    }
    messageList.add(new MessageInner(message));
  }

  @Data
  public static class MessageInner {
    private String type = "E";
    private String message;

    MessageInner(String msg) {
      this.message = msg;
    }
  }
}
