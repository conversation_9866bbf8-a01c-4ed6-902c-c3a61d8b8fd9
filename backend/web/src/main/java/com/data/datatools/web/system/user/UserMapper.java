package com.data.datatools.web.system.user;


import com.data.datatools.common.base.dao.IBasePagingMapper;
import com.data.datatools.common.response.ListResponse;
import com.data.datatools.web.system.user.entity.UserRoleInfoEntity;
import com.data.datatools.web.system.user.model.UserCondition;
import com.data.datatools.web.system.user.model.UserInfoModel;
import java.util.List;
import java.util.function.Function;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface UserMapper extends IBasePagingMapper {

  default List<UserInfoModel> selectDataListWithPage(UserCondition condition, ListResponse<UserInfoModel> response,
    Function<UserRoleInfoEntity, UserInfoModel> convert) {
    return selectWithPage(condition, response, convert, this::selectList);
  }

  List<UserRoleInfoEntity> selectList(@Param(value = "param") UserCondition condition);

  UserRoleInfoEntity findById(String id);
}
