package com.data.datatools.common.base;

import com.data.datatools.common.base.dao.Pagination;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class BaseCondition {

  private Pagination pagination;

  private List<OrderItem> sorts;

  public BaseCondition addOrder(OrderItem item) {
    if (sorts == null) {
      sorts = new ArrayList<>();
    }
    sorts.add(item);
    return this;
  }
}
