package com.data.datatools.web.workbench.display.dto;

import com.data.datatools.component.dto.analytics.ScoreTerm.Item;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Data;

@Data
public class MatchItems {

  private Map<String, Map<String, Map<Integer, List<MatchItem>>>> matchMap = new HashMap<>();

  public MatchItems() {
  }
  public MatchItems addItem(String column, String match, String label, Item item) {

    Map<String, Map<Integer, List<MatchItem>>> scoreMap = matchMap.computeIfAbsent(column, (o) -> new HashMap<>());

    int offset = item.getOffset();

    Map<Integer, List<MatchItem>> matchItems = scoreMap.computeIfAbsent(match, (o) -> new HashMap<>());

    List<MatchItem> itemList = matchItems.computeIfAbsent(offset, o -> new ArrayList<>());
    MatchItem matchItem = new MatchItem(item.getText());
    itemList.add(matchItem);
    matchItem.addItem(label, item.getScore());
    return this;
  }

  @Data
  public static class MatchItem {

    private String text;
    private String label;
    private float score;
    public MatchItem(String text) {
      this.text = text;
    }
    public void addItem(String label, float score) {
      this.label = label;
      this.score = score;
    }

    public String getTitle() {
      return "  " + label + "(" + score + ")\n";
    }
  }
}
