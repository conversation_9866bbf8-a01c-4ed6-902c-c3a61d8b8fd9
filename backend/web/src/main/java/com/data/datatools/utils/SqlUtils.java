package com.data.datatools.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.data.datatools.common.code.ModelType;
import com.data.datatools.common.model.FilterModel;
import com.data.datatools.common.model.ItemFilter;
import com.data.datatools.common.model.ItemFilters;
import com.data.datatools.component.dto.meta.FilterKeyResult;
import com.data.datatools.component.dto.meta.TableColumnMeta;
import com.data.datatools.repository.master.entity.MDataInsertHistoryEntity;
import com.data.datatools.repository.master.entity.MModelMetaEntity;
import com.data.datatools.repository.master.entity.MTableMetaEntity;
import com.data.datatools.web.workbench.model.entity.ModelColumnMeta;
import com.data.datatools.web.workbench.model.entity.ModelMeta;
import com.data.datatools.web.workbench.table.TableCtrlMapper;
import com.data.datatools.web.workbench.table.dto.TableColumnMetaDto;
import com.data.datatools.web.workbench.table.entity.DataImportHistoryParam;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public final class SqlUtils {

  public static String generateTableId() {
    return "t_" + generateId(8);
  }

  public static String generateModelId() {
    return generateId(8);
  }

  public static String generateColumnId() {
    return generateId(4);
  }

  public static String generateProjectId() {
    return generateId(5);
  }

  public static String generateDisplayId() {
    return "v_" + generateId(8);
  }

  public static String generatePivotId() {
    return generateId(8);
  }

  public static String generateScoreTemplateId() {
    return generateId(5);
  }

  public static String generateExportFileId() {
    return generateId(10);
  }

  private static String generateId(int length) {
    String id = StringUtils.generateUUID().substring(4, 4 + length);
    return clean(id);
  }

  public static String getCreateSqlContent(List<TableColumnMetaDto> columnList) {

    List<TableColumnMetaDto> primaryKeyList = columnList.stream().filter(TableColumnMetaDto::isPrimaryKey)
      .collect(Collectors.toList());

    return "";
  }

  public static String generateKey(int length) {
    String key = StringUtils.generateUUID().substring(1, 1 + length);
    return clean(key);
  }

  private static String clean(String value) {
    char firstChar = value.charAt(0);
    if (firstChar >= '0' && firstChar <= '9') {
      char c = (char) (int) (Math.random() * 26 + 97);
      value = c + value.substring(1);
    }
    return value.toLowerCase();
  }

  public static String jsonParam(String tag) {
    return "\"" + tag + "\"";
  }

  public static void append(StringBuilder sb, String text) {
    sb.append(text).append(" ");
  }

  public static void append(StringBuilder sb, String text, boolean last) {
    sb.append(text);
    if (!last) {
      sb.append(",");
    } else {
      sb.append(" ");
    }
  }

  public static void appendJsonColumn(StringBuilder sb, String text) {
    appendJsonColumn(sb, text, false);
  }

  public static void appendJsonColumn(StringBuilder sb, String text, boolean last) {
    sb.append("_t.content->>\"$.").append(text).append("\" as ").append(text);
    if (!last) {
      sb.append(",");
    } else {
      sb.append(" ");
    }
  }

  public static void appendLn(StringBuilder sb, String text) {
    sb.append(text).append("\n");
  }

  public static void appendLn(StringBuilder sb, String text, boolean last) {
    sb.append(text).append(last ? "" : ",").append("\n");
  }

  public static String concat(List<?> columnList, String split) {
    return columnList.stream().map(Objects::toString).collect(Collectors.joining(split));
  }

  public static String getBaseTableSql(MTableMetaEntity tableMetaEntity) {
    return getBaseTableSql(tableMetaEntity, null, null);
  }

  public static String getBaseTableSql(MTableMetaEntity tableMetaEntity, List<TableColumnMeta> columnList, FilterKeyResult filterKey) {
    List<TableColumnMeta> columnMetaList = JSONArray
      .parseArray(tableMetaEntity.getTableMeta(), TableColumnMeta.class);
    StringBuilder sb = new StringBuilder();
    SqlUtils.append(sb, "select");

    for (int i = 0; i < columnMetaList.size(); i++) {
      String columnId = columnMetaList.get(i).getId();
      SqlUtils.appendJsonColumn(sb, columnId, i == columnMetaList.size() - 1);
      if (columnList != null) {
        columnList.add(columnMetaList.get(i));
      }
    }

    SqlUtils.append(sb, "from");
    SqlUtils.append(sb, tableMetaEntity.getTableId());
    SqlUtils.append(sb, "_t");
    SqlUtils.append(sb, "where");
    SqlUtils.append(sb, "_t.deleted = false");
    if (filterKey != null && filterKey.isHasHistoryFilter()) {
      List<String> keys = filterKey.getKeys();
      if (!keys.isEmpty()) {
        SqlUtils.append(sb, "_t.import_key in (" + keys.stream().map(e -> "\"" + e + "\"").collect(
          Collectors.joining(",")) + ")");
      }
    }
    return sb.toString();
  }

//  public static String getBaseDatasetSql(TDatasetMetaEntity datasetMetaEntity) {
//    return getBaseDatasetSql(datasetMetaEntity, null);
//  }

  public static List<String> getFilterKey(TableCtrlMapper tableCtrlMapper, String tableId, FilterModel filter) {

    DataImportHistoryParam param = new DataImportHistoryParam();
    param.setTableId(tableId);

    boolean hasHistoryFilter = false;

    if (filter != null) {
      String[] dateRange = filter.getDateRange();
      if (StringUtils.isNotEmpty(dateRange)) {
        param.setStartTime(DateUtils.parseLocalDate(dateRange[0]).atTime(LocalTime.MIN));
        param.setEndTime(DateUtils.parseLocalDate(dateRange[1]).atTime(LocalTime.MAX));
        hasHistoryFilter = true;
      }

      if (StringUtils.isNotEmpty(filter.getTags())) {
        param.setTags(Arrays.stream(filter.getTags()).map(SqlUtils::jsonParam).collect(Collectors.toList()));
        hasHistoryFilter = true;
      }
    }

    List<String> keys = new ArrayList<>();
    if (hasHistoryFilter) {
      List<MDataInsertHistoryEntity> historyList = tableCtrlMapper.selectImportHistoryList(param);
      if (!historyList.isEmpty()) {
        keys = historyList.stream().map(MDataInsertHistoryEntity::getImportKey).collect(Collectors.toList());
      }
    }
    return keys;
  }

  public static List<ItemFilters> cleanFilter(List<ItemFilters> itemFilters) {
    List<ItemFilters> filtersList = new ArrayList<>();
    for (ItemFilters itemFilter : itemFilters) {
      ItemFilters filters = new ItemFilters();
      filters.setField(itemFilter.getField());
      filters.setRelation(itemFilter.getRelation());
      List<ItemFilter> newItems = new ArrayList<>();
      for (ItemFilter item : itemFilter.getItems()) {
        ItemFilter filter = item.filter();
        if (filter != null) {
          newItems.add(filter);
        }
      }
      filters.setItems(newItems);

      if (ListUtils.isNotEmpty(filters.getItems())) {
        filtersList.add(filters);
      }
    }
    return filtersList;
  }

  public static String cleanSql(String datasetSql) {
    return datasetSql.replace("${key-in}", "");
  }

  public static List<ModelColumnMeta> getLabelMetaList(MModelMetaEntity entity) {
    String modelMetaText = entity.getModelMeta();
    List<ModelColumnMeta> meta;
    if (modelMetaText.startsWith("[")) {
      meta = JSON.parseArray(modelMetaText, ModelColumnMeta.class);
    } else {
      ModelMeta modelMeta = JSON.parseObject(modelMetaText, ModelMeta.class);
      if (ModelType.TAG.is(entity.getModelType())) {
        meta = modelMeta.getLabelList();
      } else {
        meta = modelMeta.getAiLabelList().stream().map(e -> new ModelColumnMeta(e.getIndex(), e.getLabelText())).collect(Collectors.toList());
      }
    }
    return meta;
  }
}
