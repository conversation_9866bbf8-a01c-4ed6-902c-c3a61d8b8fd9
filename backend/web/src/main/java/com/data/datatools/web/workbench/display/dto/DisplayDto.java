package com.data.datatools.web.workbench.display.dto;

import java.util.List;
import lombok.Data;

@Data
public class DisplayDto {

  private String optType;

  private String displayId;

  private String displayName;

  private String tableId;

  private String remark;

  private String modelType;

  private boolean showExisting;

  private String columnMode;

  private List<DisplayColumn> columnList;

  private List<MatchFieldColumn> matchColumnList;

  private boolean test;
}
