package com.data.datatools.web.workbench.model;

import com.data.datatools.common.response.BaseResponse;
import com.data.datatools.web.workbench.model.dto.ImportModelDto;
import com.data.datatools.web.workbench.model.dto.ModelListCondition;
import com.data.datatools.web.workbench.model.dto.ModelMetaDto;
import com.data.datatools.web.workbench.table.dto.TableCtrlCondition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class ModelController {

  @Autowired
  private ModelService modelService;

  @PostMapping("/workbench/select-model")
  @PreAuthorize("hasAnyAuthority('A', 'C', 'D')")
  public BaseResponse searchModelList(@RequestBody ModelListCondition condition) {
    return modelService.searchModelList(condition);
  }

  @PostMapping("/workbench/model-save")
  @PreAuthorize("hasAnyAuthority('A')")
  public BaseResponse saveTable(@RequestBody ModelMetaDto dto) {

    return modelService.saveModel(dto);
  }

  @PostMapping("/workbench/model-refresh/{modelId}")
  @PreAuthorize("hasAnyAuthority('A', 'C', 'D')")
  public BaseResponse refreshModel(@PathVariable String modelId) {
    return modelService.refreshModel(modelId);
  }

  @GetMapping("/workbench/model-get/{modelId}")
  @PreAuthorize("hasAnyAuthority('A')")
  public BaseResponse getModel(@PathVariable String modelId, @RequestParam boolean add) {
    return modelService.getModel(modelId, add);
  }

  @PreAuthorize("hasAnyAuthority('A', 'C', 'D')")
  @DeleteMapping("/workbench/model-delete/{modelId}")
  public BaseResponse deleteModel(@PathVariable("modelId") String modelId) {
    return modelService.deleteModel(modelId);
  }

  @PostMapping("/workbench/import-model")
  @PreAuthorize("hasAnyAuthority('A', 'C', 'D')")
  public BaseResponse importModel(ImportModelDto dto) {
    return modelService.importModel(dto);
  }

  @GetMapping("/workbench/model-export/{modelId}")
  @PreAuthorize("hasAnyAuthority('A')")
  public ResponseEntity<byte[]> exportModel(@PathVariable("modelId") String modelId) {
    return modelService.exportModel(modelId);
  }
}
