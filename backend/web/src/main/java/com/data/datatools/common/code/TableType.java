package com.data.datatools.common.code;

public enum TableType {

  TABLE("1", "数据表"),
  DATASET("2", "数据集");

  private final String type;
  private final String name;

  TableType(String type, String name) {
    this.type = type;
    this.name = name;
  }

  public static TableType value(String type) {
    for (TableType value : TableType.values()) {
      if (value.type.equals(type)) {
        return value;
      }
    }
    return null;
  }

  public static String getLabel(String tableType) {
    for (TableType value : TableType.values()) {
      if (value.getType().equals(tableType)) {
        return value.getName();
      }
    }
    return "";
  }

  public String getType() {
    return type;
  }

  public String getName() {
    return name;
  }

  public boolean is(String tableType) {
    return this.type.equals(tableType);
  }
}
