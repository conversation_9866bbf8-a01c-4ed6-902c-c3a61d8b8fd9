package com.data.datatools.utils;

import java.time.DateTimeException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import org.apache.commons.lang3.StringUtils;

public class DateUtils {

  public static final String DATE_FORMAT_CAL = "yyyy-MM-dd";
  public static final String TIME_FORMAT_CAL = "yyyy-MM-dd HH:mm:ss";

  private DateUtils() {
  }


  public static String formattingElapsedTime(long elapsedTime) {
    StringBuilder builder = new StringBuilder();
    builder.append(elapsedTime);
    if (builder.length() < 10) {
      for (int j = builder.length(); j < 10; j++) {
        if (j == 9) {
          builder.insert(0, '.');
        }
        builder.insert(0, '0');
      }
    } else {
      builder.insert(builder.length() - 9, '.');
    }
    return builder.toString();
  }

  //LocalDate -> Date
  public static Date asDate(LocalDate localDate) {
    return Date.from(localDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
  }

  //LocalDateTime -> Date
  public static Date asDate(LocalDateTime localDateTime) {
    return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
  }

  //Date -> LocalDate
  public static LocalDate asLocalDate(Date date) {
    return Instant.ofEpochMilli(date.getTime()).atZone(ZoneId.systemDefault()).toLocalDate();
  }

  //Date -> LocalDateTime
  public static LocalDateTime asLocalDateTime(Date date) {
    return Instant.ofEpochMilli(date.getTime()).atZone(ZoneId.systemDefault()).toLocalDateTime();
  }

  public static String formatLocalDateTime(LocalDateTime dateTime) {
    return dateTime.format(DateTimeFormatter.ofPattern(TIME_FORMAT_CAL));
  }

  public static LocalDate parseLocalDate(String date) {
    return parseLocalDate(date, DATE_FORMAT_CAL);
  }

  public static LocalDate parseLocalDate(String date, String pattern) {
    if (StringUtils.isEmpty(date)) {
      return null;
    }
    try {
      return LocalDate.parse(date, DateTimeFormatter.ofPattern(pattern));
    } catch (DateTimeException e) {
      throw new RuntimeException(e);
    }
  }
}
