package com.data.datatools.web.workbench.table.entity;

import com.data.datatools.common.base.BaseCondition;
import com.data.datatools.common.model.ItemFilters;
import com.data.datatools.web.workbench.table.dto.BatchEditRecord;
import java.util.List;
import lombok.Data;

@Data
public class TableDataUpdateParam extends BaseCondition {

  private String tableId;
  private List<BatchEditRecord> dataList;

  private List<String> keys;
  private List<ItemFilters> itemFilters;
  private String type = "table";
}
