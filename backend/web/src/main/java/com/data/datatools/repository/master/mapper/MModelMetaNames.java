package com.data.datatools.repository.master.mapper;

import java.sql.JDBCType;
import java.time.LocalDateTime;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class MModelMetaNames {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_model_meta")
    public static final MModelMeta MModelMeta = new MModelMeta();

    /**
     * 主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_model_meta.model_id")
    public static final SqlColumn<String> modelId = MModelMeta.modelId;

    /**
     * 模型名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_model_meta.model_name")
    public static final SqlColumn<String> modelName = MModelMeta.modelName;

    /**
     * 模型类型
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_model_meta.model_type")
    public static final SqlColumn<String> modelType = MModelMeta.modelType;

    /**
     * 所属项目ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_model_meta.project_id")
    public static final SqlColumn<String> projectId = MModelMeta.projectId;

    /**
     * Ai模型ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_model_meta.ai_model_id")
    public static final SqlColumn<String> aiModelId = MModelMeta.aiModelId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_model_meta.status")
    public static final SqlColumn<String> status = MModelMeta.status;

    /**
     * 描述
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_model_meta.remark")
    public static final SqlColumn<String> remark = MModelMeta.remark;

    /**
     * 是否删除
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_model_meta.deleted")
    public static final SqlColumn<Boolean> deleted = MModelMeta.deleted;

    /**
     * 版本
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_model_meta.version")
    public static final SqlColumn<Integer> version = MModelMeta.version;

    /**
     * 插入时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_model_meta.created_at")
    public static final SqlColumn<LocalDateTime> createdAt = MModelMeta.createdAt;

    /**
     * 做成者
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_model_meta.created_by")
    public static final SqlColumn<String> createdBy = MModelMeta.createdBy;

    /**
     * 更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_model_meta.updated_at")
    public static final SqlColumn<LocalDateTime> updatedAt = MModelMeta.updatedAt;

    /**
     * 更新者
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_model_meta.updated_by")
    public static final SqlColumn<String> updatedBy = MModelMeta.updatedBy;

    /**
     * 模型Meta
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_model_meta.model_meta")
    public static final SqlColumn<String> modelMeta = MModelMeta.modelMeta;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_model_meta")
    public static final class MModelMeta extends SqlTable {
        public final SqlColumn<String> modelId = column("model_id", JDBCType.CHAR);

        public final SqlColumn<String> modelName = column("model_name", JDBCType.VARCHAR);

        public final SqlColumn<String> modelType = column("model_type", JDBCType.CHAR);

        public final SqlColumn<String> projectId = column("project_id", JDBCType.CHAR);

        public final SqlColumn<String> aiModelId = column("ai_model_id", JDBCType.VARCHAR);

        public final SqlColumn<String> status = column("status", JDBCType.CHAR);

        public final SqlColumn<String> remark = column("remark", JDBCType.VARCHAR);

        public final SqlColumn<Boolean> deleted = column("deleted", JDBCType.BIT);

        public final SqlColumn<Integer> version = column("version", JDBCType.INTEGER);

        public final SqlColumn<LocalDateTime> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createdBy = column("created_by", JDBCType.VARCHAR);

        public final SqlColumn<LocalDateTime> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updatedBy = column("updated_by", JDBCType.VARCHAR);

        public final SqlColumn<String> modelMeta = column("model_meta", JDBCType.LONGVARCHAR);

        public MModelMeta() {
            super("m_model_meta");
        }
    }
}