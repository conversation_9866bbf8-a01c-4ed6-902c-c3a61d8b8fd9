package com.data.datatools.utils;

import com.data.datatools.common.response.BaseResponse;
import net.sf.json.JSONObject;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public final class ResponseUtils {
  private ResponseUtils() {
  }

  public static void jsonResponse(HttpServletResponse response, BaseResponse data)
      throws IOException {
    response.setCharacterEncoding("UTF-8");
    response.setContentType("application/json; charset=utf-8");
    response.getWriter().write(JSONObject.fromObject(data).toString());
  }
}
