package com.data.datatools.web.workbench.display.dto;

import java.util.List;
import java.util.Objects;
import lombok.Data;

@Data
public class MatchFieldColumn {

  private String columnField;
  private String modelId;
  private List<String> targetColumns;
  private String alias;
  private String width;

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    MatchFieldColumn that = (MatchFieldColumn) o;
    return Objects.equals(columnField, that.columnField) &&
      Objects.equals(targetColumns, that.targetColumns);
  }

  @Override
  public int hashCode() {
    return Objects.hash(columnField, targetColumns);
  }
}
