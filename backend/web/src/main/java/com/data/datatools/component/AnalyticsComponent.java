package com.data.datatools.component;

import com.alibaba.fastjson.JSON;
import com.data.datatools.common.code.DisplayStatus;
import com.data.datatools.common.code.ModelType;
import com.data.datatools.component.dto.analytics.AnalyticsKeys;
import com.data.datatools.component.dto.analytics.AnalyticsKeys.Text;
import com.data.datatools.component.dto.analytics.AnalyticsResult;
import com.data.datatools.component.dto.analytics.ScoreTerm;
import com.data.datatools.repository.master.entity.MAiModelEntity;
import com.data.datatools.repository.master.entity.MDisplayMetaEntity;
import com.data.datatools.repository.master.entity.MModelMetaEntity;
import com.data.datatools.repository.master.mapper.MAiModelMapper;
import com.data.datatools.repository.master.mapper.MAiModelNames;
import com.data.datatools.repository.master.mapper.MDisplayMetaMapper;
import com.data.datatools.repository.master.mapper.MDisplayMetaNames;
import com.data.datatools.repository.master.mapper.MModelMetaMapper;
import com.data.datatools.repository.master.mapper.MModelMetaNames;
import com.data.datatools.repository.slave.entity.AnalyticsSourceEntity;
import com.data.datatools.repository.slave.mapper.DisplayTableMapper;
import com.data.datatools.utils.SqlUtils;
import com.data.datatools.utils.StringUtils;
import com.data.datatools.web.workbench.display.dto.MatchFieldColumn;
import com.data.datatools.web.workbench.display.entity.DisplayMeta;
import com.data.datatools.web.workbench.model.entity.ModelColumnMeta;
import com.hankcs.hanlp.HanLP;
import com.hankcs.hanlp.seg.common.Term;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import org.mybatis.dynamic.sql.SqlBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Component
public class AnalyticsComponent {

  @Autowired
  private MDisplayMetaMapper mDisplayMetaMapper;

  @Autowired
  private MModelMetaMapper mModelMetaMapper;

  @Autowired
  private MAiModelMapper mAiModelMapper;

  @Autowired
  private DisplayTableMapper displayTableMapper;

  @Autowired
  private AiAnalyticsComponent aiAnalyticsComponent;

  private static final Map<String, Integer> ANALYTICS_MAP = new ConcurrentHashMap<>();

  private static final Logger logger = LoggerFactory.getLogger(AnalyticsComponent.class.getName());

  @Transactional(propagation = Propagation.NOT_SUPPORTED)
  public void analytics(String tableId, String displayId) {
    doAnalytics(tableId, displayId);
  }

  @Transactional(propagation = Propagation.NOT_SUPPORTED)
  public void analytics(String tableId) {
    List<MDisplayMetaEntity> entityList = mDisplayMetaMapper.select(
      c -> c.where().and(MDisplayMetaNames.tableId, SqlBuilder.isEqualTo(tableId)));

    for (MDisplayMetaEntity displayMetaEntity : entityList) {
      doAnalytics(tableId, displayMetaEntity.getDisplayId());
    }
  }

  private void doAnalytics(String tableId, String displayId) {
    // 更新状态
    MDisplayMetaEntity displayEntity = mDisplayMetaMapper.selectByPrimaryKey(displayId)
      .orElseThrow(RuntimeException::new);

    List<AnalyticsSourceEntity> dataList = displayTableMapper.selectNeedAnalyticsDataList(tableId, displayId);

    displayEntity.setStatus(DisplayStatus.PROCESS_ING.status());
    displayEntity.setAnalyticsCount((long) dataList.size());
    displayEntity.setAnalyticsTime(LocalDateTime.now());
    mDisplayMetaMapper.updateByPrimaryKey(displayEntity);

    try {
      analyticsInner(dataList, displayId, displayEntity);
      displayEntity.setStatus(DisplayStatus.COMPLETE.status());
      mDisplayMetaMapper.updateByPrimaryKey(displayEntity);
    } catch (Exception e) {
      displayEntity.setStatus(DisplayStatus.FAIL.status());
      mDisplayMetaMapper.updateByPrimaryKey(displayEntity);
      logger.error("智能匹配发生异常：" + displayId, e);
    } finally {
      ANALYTICS_MAP.remove(displayId);
    }
  }

  public int getAnalyticsCount(String displayId) {
    Integer count = ANALYTICS_MAP.get(displayId);
    return count == null ? -1 : count;
  }

  private void analyticsInner(List<AnalyticsSourceEntity> dataList, String displayId,
    MDisplayMetaEntity displayEntity) {
    if (dataList.isEmpty()) {
      return;
    }
    String modelType = displayEntity.getModelType();
    String displayMetaText = displayEntity.getDisplayMeta();
    DisplayMeta displayMeta = JSON.parseObject(displayMetaText, DisplayMeta.class);

    List<MatchFieldColumn> matchColumnList = displayMeta.getMatchColumnList();

    List<String> modelIds = matchColumnList.stream().map(MatchFieldColumn::getModelId).distinct()
      .collect(Collectors.toList());

    Map<String, List<AnalyticsKeys>> modelMap = new HashMap<>();
    Map<String, MAiModelEntity> aiModelMap = new HashMap<>();
    Map<String, List<String>> aiModelLabelsMap = new HashMap<>();
    if (ModelType.TAG.is(modelType)) {
      modelMap = mModelMetaMapper.select(
        c -> c.where().and(MModelMetaNames.modelId, SqlBuilder.isIn(modelIds))).stream().collect(
        Collectors.toMap(MModelMetaEntity::getModelId, e -> {
          List<ModelColumnMeta> meta = SqlUtils.getLabelMetaList(e);
          return meta.stream()
            .map(m -> new AnalyticsKeys(m.getIndex(), m.getLabelText(), m.getLabelKeyText(), m.getBasicWeight()))
            .collect(Collectors.toList());
        }));
    } else {
      Map<String, MModelMetaEntity> aiModelIds = mModelMetaMapper.select(
          c -> c.where().and(MModelMetaNames.modelId, SqlBuilder.isIn(modelIds))).stream().collect(
          Collectors.toMap(MModelMetaEntity::getAiModelId, e -> e));
      aiModelIds.forEach((k, v) -> {
        List<ModelColumnMeta> meta = SqlUtils.getLabelMetaList(v);
        aiModelLabelsMap.put(v.getModelId(), meta.stream().map(ModelColumnMeta::getLabelText).collect(Collectors.toList()));
      });
      aiModelMap = mAiModelMapper.select(
        c -> c.where().and(MAiModelNames.modelId, SqlBuilder.isIn(aiModelIds.keySet()))).stream().collect(
        Collectors.toMap(e -> aiModelIds.get(e.getModelId()).getModelId(), e -> e)
      );
    }
    // 需要匹配 的 源数据
    for (int i = 0; i < dataList.size(); i++) {
      AnalyticsSourceEntity sourceEntity = dataList.get(i);

      String content = sourceEntity.getContent();
      if (StringUtils.isEmpty(content)) {
        continue;
      }
      // 原始内容Map
      Map<String, String> contentMap = JSON.parseObject(content, Map.class);
      Map<String, String> resultMap = new HashMap<>();
      Map<String, Map<String, Map<String, ScoreTerm>>> debugResultMap = new HashMap<>();
      Map<String, String> aiDebugResultMap = new HashMap<>();

      for (MatchFieldColumn matchColumn : matchColumnList) {

        Map<String, String> targetMap = new HashMap<>();
        for (String targetColumn : matchColumn.getTargetColumns()) {
          targetMap.put(targetColumn, contentMap.get(targetColumn));
        }

        if (ModelType.TAG.is(modelType)) {
          analyticsInnerWithTag(modelMap, matchColumn, targetMap, resultMap, debugResultMap);
        } else if (ModelType.AI.is(modelType)) {
          aiAnalyticsComponent.analytics(matchColumn, aiModelMap, aiModelLabelsMap, targetMap, resultMap, aiDebugResultMap);
        } else {
          throw new UnsupportedOperationException(String.format("model type (%s) not supported.", modelType));
        }
      }

      Integer dataId = sourceEntity.getId();

      boolean exist = sourceEntity.isExist();
      Object debugMap = ModelType.TAG.is(modelType) ? debugResultMap : aiDebugResultMap;
      if (!exist) {
        displayTableMapper.insert(displayId, dataId, JSON.toJSONString(resultMap), JSON.toJSONString(debugMap));
      } else {
        displayTableMapper.update(displayId, dataId, JSON.toJSONString(resultMap), JSON.toJSONString(debugMap));
      }
      ANALYTICS_MAP.put(displayId, i);
    }
  }

  private void analyticsInnerWithAi(
    Map<String, List<AnalyticsKeys>> modelMap,
    MatchFieldColumn matchColumn,
    Map<String, String> targetMap,
    Map<String, String> resultMap) {

    System.out.println();
  }

  private void analyticsInnerWithTag(
    Map<String, List<AnalyticsKeys>> modelMap,
    MatchFieldColumn matchColumn,
    Map<String, String> targetMap,
    Map<String, String> resultMap,
    Map<String, Map<String, Map<String, ScoreTerm>>> debugResultMap) {

    String modelId = matchColumn.getModelId();
    String column = matchColumn.getColumnField();

    Map<String, AnalyticsResult> _resultMap = new HashMap<>();
    Map<String, Map<String, ScoreTerm>> _debugResultMap = new HashMap<>();

    targetMap.forEach((field, targetContent) -> {
      if (StringUtils.isNotEmpty(targetContent)) {
        AnalyticsResult result = analyticsContent(targetContent, modelMap.get(modelId));
        _resultMap.put(field, result);
        _debugResultMap.put(field, result.getScoreMap());
      }
    });

    String resultText = "";
    float score = Float.MIN_VALUE;
    for (String field : _resultMap.keySet()) {
      AnalyticsResult ar = _resultMap.get(field);
      if (ar.getScore() > score) {
        resultText = ar.getResult();
        score = ar.getScore();
      }
    }
    resultMap.put(column, resultText);
    debugResultMap.put(column, _debugResultMap);
  }

  private AnalyticsResult analyticsContent(String targetContent, List<AnalyticsKeys> keys) {

    Map<String, ScoreTerm> resultMap = new HashMap<>();
    List<Term> wordList = HanLP.newSegment().enableOffset(true).seg(targetContent);
    for (AnalyticsKeys key : keys) {
      Text labelText = key.getText();
      Map<String, Float> textScoreMap = key.getKeys();

      ScoreTerm scoreTerm = new ScoreTerm(labelText, key.getBasics());

      for (Entry<String, Float> entry : textScoreMap.entrySet()) {
        String t = entry.getKey();
        Float s = entry.getValue();

        Set<String> exitsText = new HashSet<>();
        for (Term term : wordList) {
          if (term.word.equals(t)) {
            if (exitsText.contains(t)) {
              scoreTerm.addTerm(t, 0, s, term.offset);
            } else {
              scoreTerm.addTerm(t, s, s, term.offset);
            }
            exitsText.add(t);
          }
        }
      }
      resultMap.put(labelText.toString(), scoreTerm);
    }

    float score = Float.MIN_VALUE;
    String resultText = "";
    for (Entry<String, ScoreTerm> entry : resultMap.entrySet()) {
      String t = entry.getKey();
      float s = entry.getValue().getScore();
      if (s > score) {
        score = s;
        resultText = t;
      }
    }
    return new AnalyticsResult(resultText, score, resultMap);
  }
}
