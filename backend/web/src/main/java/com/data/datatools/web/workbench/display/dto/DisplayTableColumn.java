package com.data.datatools.web.workbench.display.dto;

import com.data.datatools.common.model.SelectOption;
import java.util.List;
import lombok.Data;

@Data
public class DisplayTableColumn {

  private String id;

  private String name;

  private String width;

  private boolean match;

  private List<SelectOption> matchOptions;

  public DisplayTableColumn(String id, String name, String width, boolean match) {
    this.id = id;
    this.name = name;
    this.width = width;
    this.match = match;
  }
}
