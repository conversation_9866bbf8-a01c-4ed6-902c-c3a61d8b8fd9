package com.data.datatools.common.mybatis;

import java.util.List;
import java.util.function.Function;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.SqlTable;
import org.mybatis.dynamic.sql.select.QueryExpressionDSL;
import org.mybatis.dynamic.sql.select.SelectDSL;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;

public class SpringForUpdateUtility {

  public static QueryExpressionDSL.FromGatherer<SelectForUpdateBatchReaderSelectModel> selectForUpdate(
    BasicColumn... selectList) {
    return SelectDSL.select(SelectForUpdateBatchReaderSelectModel::new, selectList);
  }

  public static <R> List<R> selectList(Function<SelectStatementProvider, List<R>> mapper,
    BasicColumn[] selectList, SqlTable table, SelectForUpdateDSLCompleter completer) {
    return mapper.apply(select(selectList, table, completer));
  }

  public static SelectStatementProvider select(BasicColumn[] selectList, SqlTable table,
    SelectForUpdateDSLCompleter completer) {
    return select(selectForUpdate(selectList).from(table), completer);
  }

  public static SelectStatementProvider select(QueryExpressionDSL<SelectForUpdateBatchReaderSelectModel> start,
    SelectForUpdateDSLCompleter completer) {
    return completer.apply(start)
      .build()
      .render();
  }
}
