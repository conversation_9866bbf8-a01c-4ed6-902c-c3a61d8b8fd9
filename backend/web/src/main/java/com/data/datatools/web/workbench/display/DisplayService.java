package com.data.datatools.web.workbench.display;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.data.datatools.common.code.ColumnMode;
import com.data.datatools.common.code.DisplayStatus;
import com.data.datatools.common.code.ImportExportStatus;
import com.data.datatools.common.code.ImportExportType;
import com.data.datatools.common.code.ModelType;
import com.data.datatools.common.code.OptType;
import com.data.datatools.common.code.TableMode;
import com.data.datatools.common.code.UserType;
import com.data.datatools.common.exception.BusinessException;
import com.data.datatools.common.model.FilterModel;
import com.data.datatools.common.model.ItemFilters;
import com.data.datatools.common.model.SelectOption;
import com.data.datatools.common.response.BaseResponse;
import com.data.datatools.common.response.DataResponse;
import com.data.datatools.common.response.ListResponse;
import com.data.datatools.common.response.MessageResponse;
import com.data.datatools.common.security.SecuritySupport;
import com.data.datatools.common.listener.AnalyticsEvent;
import com.data.datatools.component.AiAnalyticsComponent;
import com.data.datatools.component.AnalyticsComponent;
import com.data.datatools.component.ExportComponent;
import com.data.datatools.component.PulldownComponent;
import com.data.datatools.component.dto.analytics.ScoreTerm;
import com.data.datatools.component.dto.analytics.ScoreTerm.Item;
import com.data.datatools.component.dto.meta.TableColumnMeta;
import com.data.datatools.component.dto.excel.ExcelCreateModel;
import com.data.datatools.repository.master.entity.MDisplayMetaEntity;
import com.data.datatools.repository.master.entity.MImportExportHistoryEntity;
import com.data.datatools.repository.master.entity.MModelMetaEntity;
import com.data.datatools.repository.master.entity.MTableMetaEntity;
import com.data.datatools.repository.master.mapper.MDisplayMetaMapper;
import com.data.datatools.repository.master.mapper.MDisplayMetaNames;
import com.data.datatools.repository.master.mapper.MImportExportHistoryMapper;
import com.data.datatools.repository.master.mapper.MModelMetaMapper;
import com.data.datatools.repository.master.mapper.MModelMetaNames;
import com.data.datatools.repository.master.mapper.MTableMetaMapper;
import com.data.datatools.repository.master.mapper.MTableMetaNames;
import com.data.datatools.repository.slave.mapper.DisplayTableMapper;
import com.data.datatools.utils.DateUtils;
import com.data.datatools.utils.ListUtils;
import com.data.datatools.utils.SqlUtils;
import com.data.datatools.utils.StringUtils;
import com.data.datatools.web.workbench.display.dto.DisplayColumn;
import com.data.datatools.web.workbench.display.dto.DisplayCondition;
import com.data.datatools.web.workbench.display.dto.DisplayDataCondition;
import com.data.datatools.web.workbench.display.dto.DisplayDto;
import com.data.datatools.web.workbench.display.dto.DisplayInitDto;
import com.data.datatools.web.workbench.display.dto.DisplayRecord;
import com.data.datatools.web.workbench.display.dto.DisplayTableColumn;
import com.data.datatools.web.workbench.display.dto.DisplayTableDebugColumn;
import com.data.datatools.web.workbench.display.dto.DisplayTempResult;
import com.data.datatools.web.workbench.display.dto.MatchFieldColumn;
import com.data.datatools.web.workbench.display.dto.MatchItems;
import com.data.datatools.web.workbench.display.dto.MatchItems.MatchItem;
import com.data.datatools.web.workbench.display.dto.SubmitModelDto;
import com.data.datatools.web.workbench.display.dto.UpdateMatchDataDto;
import com.data.datatools.web.workbench.display.entity.DisplayMeta;
import com.data.datatools.web.workbench.display.entity.DisplaySelectParam;
import com.data.datatools.web.workbench.display.entity.RuleText;
import com.data.datatools.web.workbench.model.entity.AiModelColumnMeta;
import com.data.datatools.web.workbench.model.entity.ModelColumnMeta;
import com.data.datatools.web.workbench.model.entity.ModelMeta;
import com.data.datatools.web.workbench.table.dto.ExportTableDataDto;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.TreeMap;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import org.mybatis.dynamic.sql.SqlBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
public class DisplayService {

  @Autowired
  private MTableMetaMapper mTableMetaMapper;

  @Autowired
  private MDisplayMetaMapper mDisplayMetaMapper;

  @Autowired
  private MModelMetaMapper mModelMetaMapper;

  @Autowired
  private DisplayMapper displayMapper;

  @Autowired
  private DisplayTableMapper displayTableMapper;

  @Autowired
  private ApplicationEventPublisher applicationEventPublisher;

  @Autowired
  private PulldownComponent pulldownComponent;

  @Autowired
  private ExportComponent exportComponent;

  @Autowired
  private AnalyticsComponent analyticsComponent;

  @Autowired
  private AiAnalyticsComponent aiAnalyticsComponent;

  @Autowired
  private MImportExportHistoryMapper mImportExportHistoryMapper;

  private static final Logger logger = LoggerFactory.getLogger(DisplayService.class.getName());

  public BaseResponse displayInit(DisplayInitDto dto) {
    DataResponse<Map<String, Object>> response = new DataResponse<>();

    Map<String, Object> responseMap = new HashMap<>();
    response.setValue(responseMap);

    List<SelectOption> tableOptions = getTableSelection();
    List<SelectOption> modelOptions = getModelSelection();

    // modelOptions.add(0, new SelectOption("00", "全标签模型").setData(ModelType.AI.type()));

    responseMap.put("tableOptions", tableOptions);
    responseMap.put("modelOptions", modelOptions);
    responseMap.put("modelTypeOptions", pulldownComponent.modelTypeOptions());

    if (OptType.ADD.is(dto.getOpt())) {
      return response;
    }
    String displayId = dto.getDisplayId();

    MDisplayMetaEntity entity = mDisplayMetaMapper.selectByPrimaryKey(displayId).orElseThrow(RuntimeException::new);

    DisplayDto modal = new DisplayDto();
    modal.setDisplayName(entity.getDisplayName());
    modal.setTableId(entity.getTableId());
    modal.setModelType(entity.getModelType());
    modal.setShowExisting(Boolean.TRUE.equals(entity.getShowExisting()));
    modal.setRemark(entity.getRemark());

    DisplayMeta spec = JSON.parseObject(entity.getDisplayMeta(), DisplayMeta.class);
    modal.setColumnMode(spec.getColumnMode());
    modal.setColumnList(spec.getColumnList());
    modal.setMatchColumnList(spec.getMatchColumnList());

    responseMap.put("display", modal);

    return response;
  }

  private List<SelectOption> getModelSelection() {
    List<MModelMetaEntity> entityList = mModelMetaMapper.select(
      c -> c.where().and(MTableMetaNames.projectId, SqlBuilder.isEqualTo(SecuritySupport.getCurrentProject()))
        .and(MTableMetaNames.deleted, SqlBuilder.isEqualTo(false)));
    return entityList.stream().map(e -> new SelectOption(e.getModelId(), e.getModelName()).setData(e.getModelType()))
      .collect(Collectors.toList());
  }

  private List<SelectOption> getTableSelection() {
    List<MTableMetaEntity> entityList = mTableMetaMapper.select(
      c -> c.where().and(MTableMetaNames.projectId, SqlBuilder.isEqualTo(SecuritySupport.getCurrentProject()))
        .and(MTableMetaNames.deleted, SqlBuilder.isEqualTo(false)));

    return entityList.stream().map(e -> new SelectOption(e.getTableId(), e.getTableName()))
      .collect(Collectors.toList());
  }

  @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Throwable.class)
  public BaseResponse displaySave(DisplayDto dto) {

    MDisplayMetaEntity entity;
    if (OptType.EDIT.is(dto.getOptType())) {
      String displayId = dto.getDisplayId();
      entity = mDisplayMetaMapper.selectOne(c -> {
        c.where().and(MDisplayMetaNames.displayId, SqlBuilder.isEqualTo(displayId))
          .and(MDisplayMetaNames.deleted, SqlBuilder.isEqualTo(false));
        return c;
      }).orElseThrow(() -> new BusinessException("该视图已经不存在，请刷新页面后再操作。"));
      if (DisplayStatus.PROCESS_ING.is(entity.getStatus())) {
        return MessageResponse.newErrorMessage("该视图正在匹配运行中，暂时不能修改。");
      }
    } else {
      entity = new MDisplayMetaEntity();
      entity.setDisplayId(SqlUtils.generateDisplayId());
    }

    long count;
    if (OptType.EDIT.is(dto.getOptType())) {
      count = mDisplayMetaMapper.count(c -> {
        c.where().and(MDisplayMetaNames.deleted, SqlBuilder.isEqualTo(false))
          .and(MDisplayMetaNames.displayId, SqlBuilder.isNotEqualTo(dto.getDisplayId()))
          .and(MDisplayMetaNames.projectId, SqlBuilder.isEqualTo(SecuritySupport.getCurrentProject()))
          .and(MDisplayMetaNames.displayName, SqlBuilder.isEqualTo(dto.getDisplayName()));
        return c;
      });
    } else {
      count = mDisplayMetaMapper.count(c -> {
        c.where().and(MDisplayMetaNames.deleted, SqlBuilder.isEqualTo(false))
          .and(MDisplayMetaNames.projectId, SqlBuilder.isEqualTo(SecuritySupport.getCurrentProject()))
          .and(MDisplayMetaNames.displayName, SqlBuilder.isEqualTo(dto.getDisplayName()));
        return c;
      });
    }
    if (count > 0) {
      return MessageResponse.newErrorMessage("数据集名称已经存在，请重新输入。");
    }

    boolean editMeta = false;
    DisplayMeta displayMeta = new DisplayMeta(dto);
    if (OptType.EDIT.is(dto.getOptType())) {
      String displayMetaText = entity.getDisplayMeta();
      DisplayMeta newDisplayMeta = JSON.parseObject(displayMetaText, DisplayMeta.class);
      editMeta = displayMeta.diff(newDisplayMeta);
    }

    entity.setDisplayName(dto.getDisplayName());
    entity.setProjectId(SecuritySupport.getCurrentProject());
    entity.setTableId(dto.getTableId());
    entity.setRemark(dto.getRemark());
    entity.setModelType(dto.getModelType());
    entity.setShowExisting(dto.isShowExisting());
    entity.setDisplayMeta(JSON.toJSONString(displayMeta));
    entity.setStatus(editMeta ? DisplayStatus.PROCESS_ING.status() : DisplayStatus.COMPLETE.status());
    entity.setAnalyticsTime(LocalDateTime.now());
    entity.setDeleted(false);

    if (OptType.EDIT.is(dto.getOptType())) {
      mDisplayMetaMapper.updateByPrimaryKey(entity);
      // 判断分析列有没有发生变化
      if (editMeta) {
        displayTableMapper.truncateTable(entity.getDisplayId());
        applicationEventPublisher.publishEvent(new AnalyticsEvent(entity.getTableId(), entity.getDisplayId()));
      }
    } else {
      mDisplayMetaMapper.insert(entity);
      displayTableMapper.createDisplayTable(entity.getDisplayId());
      applicationEventPublisher.publishEvent(new AnalyticsEvent(entity.getTableId(), entity.getDisplayId()));
    }

    Map<String, Object> result = new HashMap<>();
    result.put("displayId", entity.getDisplayId());
    result.put("editMeta", editMeta);
    return new DataResponse<>().setValue(result);
  }

  public BaseResponse searchDisplayList(DisplayCondition condition) {
    ListResponse<DisplayRecord> response = new ListResponse<>();
    displayMapper.selectDisplayListWithPage(condition, response, e -> {
      DisplayRecord record = new DisplayRecord();
      record.setDisplayId(e.getDisplayId());
      record.setDisplayName(e.getDisplayName());
      record.setTableId(e.getTableId());
      record.setTableName(e.getTableName());
      record.setModelType(e.getModelType());
      record.setModelTypeName(ModelType.typeName(e.getModelType()));
      record.setCreateTime(e.getCreateTime());
      record.setCreateUser(e.getCreateUser());
      DisplayStatus status = DisplayStatus.getStatus(e.getStatus(), e.getAnalyticsTime());
      if (DisplayStatus.PROCESS_ING.is(status.status())) {
        Long analyticsCount = e.getAnalyticsCount();
        int completeCount = analyticsComponent.getAnalyticsCount(e.getDisplayId());
        if (completeCount == -1 || analyticsCount == null || analyticsCount == 0) {
          record.setProgressRate("未知");
        } else {
          double percent = (double) completeCount / analyticsCount * 100;
          String formatted = String.format("%.1f%%", percent);
          record.setProgressRate(formatted);
        }
      }
      record.setStatus(status.status());
      record.setStatusName(status.statusName());
      record.setRemark(e.getRemark());
      return record;
    });
    return response;
  }

  public BaseResponse selectDisplayData(String displayId, DisplayDataCondition condition) {

    ListResponse<Map<String, Object>> response = new ListResponse<>();

    Optional<MDisplayMetaEntity> optEntity = mDisplayMetaMapper.selectOne(
      c -> c.where().and(MDisplayMetaNames.displayId, SqlBuilder.isEqualTo(displayId))
        .and(MTableMetaNames.projectId, SqlBuilder.isEqualTo(SecuritySupport.getCurrentProject())));
    if (!optEntity.isPresent()) {
      return MessageResponse.newErrorMessage("该展示视图不存在，请返回一览页面刷新后再进行操作。");
    }

    MDisplayMetaEntity entity = optEntity.get();

    DisplaySelectParam param = new DisplaySelectParam();
    param.setSorts(condition.getSorts());
    param.setPagination(condition.getPagination());
    FilterModel filter = condition.getFilter();

    DisplayTempResult result = new DisplayTempResult();

    BaseResponse baseResponse = displayParam(result, entity, param, filter);
    if (baseResponse instanceof MessageResponse) {
      return baseResponse;
    }

    DisplayStatus status = DisplayStatus.getStatus(entity.getStatus(), entity.getAnalyticsTime());

    List<DisplayTableColumn> columnMetas = result.getColumnMetas();
    Map<String, String> showColumnMap = result.getShowColumnMap();
    List<DisplayTableDebugColumn> debugColumnMetas = result.getDebugColumnMetas();

    response.putData("displayName", entity.getDisplayName());
    response.putData("meta", columnMetas);
    response.putData("debugMeta", debugColumnMetas);
    response.putData("modelType", entity.getModelType());
    response.putData("displayStatus", status.status());
    if (condition.isInit()) {
      response.putData("areaOptions", pulldownComponent.townOptions());
    }

    if (DisplayStatus.COMPLETE.is(entity.getStatus())) {
      try {
        displayTableMapper.selectDisplayDataListWithPage(param, response, row -> {
          Object correctContent = row.get("correct_content");
          Map<?, ?> map;
          if (correctContent == null || "".equals(correctContent) || "{}".equals(correctContent)) {
            map = new HashMap<>();
          } else {
            map = JSON.parseObject(correctContent.toString(), Map.class);
          }
          row.put("correct_content", map);
          row.put("tempCorrectContent", map);

          if ((SecuritySupport.isAdmin() || SecuritySupport.isCityManager())) {
            if (ModelType.TAG.is(entity.getModelType())) {
              String debugContent = (String) row.get("debug_content");
              JSONObject debugObjects = JSON.parseObject(debugContent);
              Set<String> matchColumns = debugObjects.keySet();
              MatchItems matchItems = new MatchItems();
              for (String matchField : matchColumns) {
                JSONObject debugColumn = debugObjects.getJSONObject(matchField);
                Set<String> columns = debugColumn.keySet();
                for (String column : columns) {
                  JSONObject resultObject = debugColumn.getJSONObject(column);
                  Set<String> matchLabels = resultObject.keySet();

                  for (String matchLabel : matchLabels) {
                    ScoreTerm scoreTerm = resultObject.getObject(matchLabel, ScoreTerm.class);
                    List<Item> items = scoreTerm.getItems();
                    if (ListUtils.isNotEmpty(items)) { //
                      for (Item item : items) {
                        matchItems.addItem(column, matchField, matchLabel, item);
                      }
                    }
                  }
                }
              }
              matchItems.getMatchMap().forEach((column, matchValues) -> {
                String targetColumn = showColumnMap.get(column);
                String originText = (String) row.get(targetColumn);
                StringBuilder sb = new StringBuilder(originText);
                Map<Integer, Map<String, List<MatchItem>>> offsetMap = new TreeMap<>();
                matchValues.forEach((match, e) -> {
                  e.forEach((offset, matchIem) -> {
                    Map<String, List<MatchItem>> matchMap = offsetMap.computeIfAbsent(offset, o -> new HashMap<>());
                    matchMap.put(match, matchIem);
                  });
                });

                Map<String, String> cNameMap = columnMetas.stream()
                  .collect(Collectors.toMap(DisplayTableColumn::getId, DisplayTableColumn::getName));
                offsetMap.keySet().stream().sorted((e1, e2) -> e2 - e1).forEach(offset -> {
                  Map<String, List<MatchItem>> mMap = offsetMap.get(offset);
                  StringBuilder title = new StringBuilder();
                  AtomicReference<String> text = new AtomicReference<>();
                  mMap.forEach((k, v) -> {
                    v.sort((d1, d2) -> {
                      int dd1 = Integer.parseInt(d1.getLabel().substring(0, d1.getLabel().indexOf(".")));
                      int dd2 = Integer.parseInt(d2.getLabel().substring(0, d2.getLabel().indexOf(".")));
                      return dd1 - dd2;
                    });
                    for (int i = 0; i < v.size(); i++) {
                      MatchItem vv = v.get(i);
                      text.set(vv.getText());
                      if (i == 0) {
                        title.append(cNameMap.get(k)).append("\n");
                      }
                      title.append(vv.getTitle());
                    }
                  });

                  sb.replace(offset, offset + text.get().length(),
                    String.format("<div title=\"%s\" style=\"color: red;display: inline-block\">%s</div>", title, text.get()));
                });
                row.put(targetColumn, sb.toString());
              });
            } else if (ModelType.AI.is(entity.getModelType())) {
              String debugContent = (String) row.get("debug_content");
              if (StringUtils.isNotEmpty(debugContent)) {
                Map<String, String> debugMap = JSON.parseObject(debugContent, Map.class);
                debugMap.forEach((k, v) -> {
                  row.put("debug_" + k, v);
                });
              }
            }
          }
          return row;
        });
      } catch (Exception e) {
        logger.error(e.getMessage(), e);
        return MessageResponse.newErrorMessage("检索失败！（推测原因：视图展示设置有误，或者是过滤条件不正确。）");
      }
    }
    return response;
  }

  private BaseResponse displayParam(DisplayTempResult result, MDisplayMetaEntity entity, DisplaySelectParam param,
    FilterModel filter) {

    String displayId = entity.getDisplayId();
    String tableId = entity.getTableId();
    String modelType = entity.getModelType();
//    if (!SecuritySupport.isAdmin() && !DisplayStatus.COMPLETE.is(status)) {
//      return MessageResponse.newErrorMessage("该视图智能匹配未成功，请返回一览页面刷新后再进行操作。");
//    }

    Optional<MTableMetaEntity> optTableEntity = mTableMetaMapper.selectByPrimaryKey(tableId);
    if (!optTableEntity.isPresent()) {
      return MessageResponse.newErrorMessage("该展示视图不存在，请返回一览页面刷新后再进行操作。");
    }
    // 视图结构
    String displayMetaText = entity.getDisplayMeta();
    DisplayMeta displayMeta = JSON.parseObject(displayMetaText, DisplayMeta.class);
    List<MatchFieldColumn> matchColumnList = displayMeta.getMatchColumnList();

    // 源数据表结构
    MTableMetaEntity tableEntity = optTableEntity.get();
    String tableMeta = tableEntity.getTableMeta();
    List<TableColumnMeta> columnMetaList = JSONArray.parseArray(tableMeta, TableColumnMeta.class);
    Map<String, TableColumnMeta> tableColumnMap = columnMetaList.stream()
      .collect(Collectors.toMap(TableColumnMeta::getId, e -> e));

    List<String> modelIds = matchColumnList.stream().map(MatchFieldColumn::getModelId).collect(Collectors.toList());
    // model
    Map<String, MModelMetaEntity> modelMap = mModelMetaMapper.select(
        c -> c.where().and(MModelMetaNames.modelId, SqlBuilder.isIn(modelIds))).stream()
      .collect(Collectors.toMap(MModelMetaEntity::getModelId, e -> e));

    List<String> sqlColumns = new ArrayList<>();
    List<DisplayTableColumn> columnMetas = new ArrayList<>();
    List<DisplayTableDebugColumn> debugColumnMetas = new ArrayList<>();

    List<DisplayColumn> columnList = displayMeta.getColumnList();

    Map<String, String> showColumnMap = new HashMap<>();

    if (ColumnMode.ALL_COLUMN.is(displayMeta.getColumnMode())) {
      columnList.clear();
      for (TableColumnMeta e : columnMetaList) {
        sqlColumns.add(String.format("t.content ->> \"$.%s\" as %s", e.getId(), e.getId()));
        columnMetas.add(new DisplayTableColumn(e.getId(), e.getName(), e.getWidth(), false));
        showColumnMap.put(e.getId(), e.getId());

        DisplayColumn column = new DisplayColumn();
        column.setColumn(e.getId());
        column.setAlias(e.getName());
        column.setColumnField(e.getId());
        column.setWidth(e.getWidth());

        columnList.add(column);
      }
    } else {
      for (DisplayColumn e : columnList) {
        sqlColumns.add(String.format("t.content ->> \"$.%s\" as %s", e.getColumn(), e.getColumnField()));
        columnMetas.add(new DisplayTableColumn(e.getColumnField(),
          StringUtils.show(e.getAlias(), tableColumnMap.get(e.getColumn()).getName()), e.getWidth(), false));
        showColumnMap.put(e.getColumn(), e.getColumnField());
      }
    }

    sqlColumns.add("v.data_id as dataId");
    for (MatchFieldColumn e : matchColumnList) {
      sqlColumns.add(String.format("v.content ->> \"$.%s\" as %s", e.getColumnField(), e.getColumnField()));

      String modelId = e.getModelId();
      MModelMetaEntity modelEntity = modelMap.get(modelId);
      DisplayTableColumn column = new DisplayTableColumn(e.getColumnField(),
        StringUtils.show(e.getAlias(), modelEntity.getModelName()), e.getWidth(), true);

      List<ModelColumnMeta> meta = SqlUtils.getLabelMetaList(modelEntity);
      column.setMatchOptions(meta.stream().map(m -> {
        String labelText = ModelType.TAG.is(modelType) ? m.getIndex() + "." + m.getLabelText() : m.getLabelText();
        return new SelectOption(labelText, labelText);
      }).collect(Collectors.toList()));
      columnMetas.add(column);
    }
    if (Boolean.TRUE.equals(entity.getShowExisting())) {
      DisplayTableColumn column = new DisplayTableColumn("exiting", "新建/存量", "100", true);
      columnMetas.add(column);
      sqlColumns.add("v.content ->> \"$.exiting\" as exiting");
    }

    sqlColumns.add("ifnull(v.correct_content, '{}') as correct_content");
    if (SecuritySupport.isAdmin() || SecuritySupport.isCityManager()) {
      sqlColumns.add("v.debug_content");

      Map<String, DisplayColumn> columnMap = columnList.stream()
        .collect(Collectors.toMap(DisplayColumn::getColumn, v -> v));

      if (ModelType.TAG.is(modelType)) {
        for (MatchFieldColumn e : matchColumnList) {

          String name = StringUtils.show(e.getAlias(), modelMap.get(e.getModelId()).getModelName());
          DisplayTableDebugColumn debugColumn = new DisplayTableDebugColumn(
            e.getColumnField(), name + "(匹配分值)");
          List<String> targetColumns = e.getTargetColumns();
          for (String targetColumn : targetColumns) {
            DisplayColumn displayColumn = columnMap.get(targetColumn);
            debugColumn.addColumn(
              new DisplayTableColumn(
                displayColumn.getColumn(),
                StringUtils.show(displayColumn.getAlias(), tableColumnMap.get(displayColumn.getColumn()).getName()),
                displayColumn.getWidth(),
                true
              )
            );
          }
          debugColumnMetas.add(debugColumn);
        }
      } else if (ModelType.AI.is(modelType)) {
        for (MatchFieldColumn e : matchColumnList) {

          String name = StringUtils.show(e.getAlias(), modelMap.get(e.getModelId()).getModelName());
          DisplayTableDebugColumn debugColumn = new DisplayTableDebugColumn("debug_" + e.getColumnField(), name + "(推荐标签)");
          debugColumnMetas.add(debugColumn);
        }
      }
    }

    // 所属区域
    {
      sqlColumns.add("ma.district as district");
      sqlColumns.add("ma.town as town");
      sqlColumns.add("ma.road as road");
    }

    // TODO
    String sql = String.format(""
        + "select "
        + "  %s "
        + "from "
        + " %s t "
        + "left join m_area ma on t.area_id = ma.area_id "
        + "left join %s v on t.id = v.data_id ", String.join(",", sqlColumns),
      tableId, displayId);

    if (UserType.DISTRICT.is(SecuritySupport.getUserType())) {
      param.setDistrict(SecuritySupport.getDistrict());
    }

    if (UserType.STREET.is(SecuritySupport.getUserType())) {
      param.setDistrict(SecuritySupport.getDistrict());
      param.setTown(SecuritySupport.getTown());
    }

    if (filter != null) {
      if ((SecuritySupport.isAdmin() || SecuritySupport.isCityManager()) && filter.getArea() != null
        && filter.getArea().length > 0) {
        String district = filter.getArea()[0];
        if ("空白".equals(district)) {
          param.setDistrict("BLANK");
          param.setTown("BLANK");
        } else {
          param.setDistrict(district);
          if (filter.getArea().length > 1) {
            param.setTown(filter.getArea()[1]);
          }
        }
      }
      if ("1".equals(filter.getItemFilterMode())) {
        List<ItemFilters> itemFilters = filter.getItemFilters();
        if (ListUtils.isNotEmpty(itemFilters)) {
          param.setItemFilters(SqlUtils.cleanFilter(itemFilters));
        }
      } else {
        // TODO
      }
    }
    param.setSql(sql);

    result.setColumnMetas(columnMetas);
    result.setDebugColumnMetas(debugColumnMetas);
    result.setShowColumnMap(showColumnMap);

    return new BaseResponse();
  }

  @Transactional
  public BaseResponse displayDelete(String displayId) {
    mDisplayMetaMapper.deleteByPrimaryKey(displayId);
    // 删除透视表
    try {
      displayTableMapper.deleteTable(displayId);
    } catch (Exception e) {
      logger.warn(e.getMessage());
    }
    return MessageResponse.newInfoMessage("删除成功。");
  }

  public BaseResponse reAnalysis(String displayId) {
    MDisplayMetaEntity entity = mDisplayMetaMapper.selectOne(c -> {
      c.where().and(MDisplayMetaNames.displayId, SqlBuilder.isEqualTo(displayId))
        .and(MDisplayMetaNames.deleted, SqlBuilder.isEqualTo(false));
      return c;
    }).orElseThrow(() -> new BusinessException("该视图已经不存在，请刷新页面后再操作。"));

    if (DisplayStatus.PROCESS_ING.is(entity.getStatus())) {
      return MessageResponse.newErrorMessage("视图正在匹配中，不能重新匹配。");
    }

    String tableId = entity.getTableId();
    displayTableMapper.truncateTable(displayId);
    applicationEventPublisher.publishEvent(new AnalyticsEvent(tableId, entity.getDisplayId()));
    return new BaseResponse();
  }

  public BaseResponse refreshStatus(String displayId) {
    MDisplayMetaEntity entity = mDisplayMetaMapper.selectOne(c -> {
      c.where().and(MDisplayMetaNames.displayId, SqlBuilder.isEqualTo(displayId))
        .and(MDisplayMetaNames.deleted, SqlBuilder.isEqualTo(false));
      return c;
    }).orElseThrow(() -> new BusinessException("该视图已经不存在，请刷新页面后再操作。"));

    DisplayStatus status = DisplayStatus.getStatus(entity.getStatus(), entity.getAnalyticsTime());

    Long analyticsCount = entity.getAnalyticsCount();
    int completeCount = analyticsComponent.getAnalyticsCount(displayId);

    String progressRate;
    if (completeCount == -1 || analyticsCount == null || analyticsCount == 0) {
      progressRate = "未知";
    } else {
      double percent = (double) completeCount / analyticsCount * 100;
      progressRate = String.format("%.1f%%", percent);
    }

    return new DataResponse<>()
      .putData("status", status.status())
      .putData("statusName", status.statusName())
      .putData("analyticsCount", analyticsCount)
      .putData("completeCount", completeCount)
      .putData("progressRate", progressRate);
  }

  public BaseResponse exportDisplayData(String displayId, ExportTableDataDto dto) {

    Optional<MDisplayMetaEntity> optEntity = mDisplayMetaMapper.selectOne(
      c -> c.where().and(MDisplayMetaNames.displayId, SqlBuilder.isEqualTo(displayId))
        .and(MTableMetaNames.projectId, SqlBuilder.isEqualTo(SecuritySupport.getCurrentProject())));
    if (!optEntity.isPresent()) {
      return MessageResponse.newErrorMessage("该展示视图不存在，请返回一览页面刷新后再进行操作。");
    }

    MDisplayMetaEntity entity = optEntity.get();

    DisplaySelectParam param = new DisplaySelectParam();
    DisplayTempResult result = new DisplayTempResult();
    BaseResponse baseResponse = displayParam(result, entity, param, null);
    if (baseResponse instanceof MessageResponse) {
      return baseResponse;
    }
    DisplayStatus status = DisplayStatus.getStatus(entity.getStatus(), entity.getAnalyticsTime());
    if (!SecuritySupport.isAdmin() && !DisplayStatus.COMPLETE.is(status.status())) {
      return MessageResponse.newErrorMessage("该视图智能匹配未成功，请返回一览页面刷新后再进行操作。");
    }

    List<DisplayTableColumn> columnMetas = result.getColumnMetas();
    Map<String, String> showColumnMap = result.getShowColumnMap();
    List<DisplayTableDebugColumn> debugColumnMetas = result.getDebugColumnMetas();

    String fileId = SqlUtils.generateExportFileId();

    MImportExportHistoryEntity historyEntity = new MImportExportHistoryEntity();
    historyEntity.setFileId(fileId);
    historyEntity.setTableId(displayId);
    historyEntity.setTableMode(TableMode.DISPLAY.getMode());
    historyEntity.setType(ImportExportType.EXPORT.getType());
    historyEntity.setStatus(ImportExportStatus.ING.getStatus());
    historyEntity.setExportFile(dto.getFileName());
    historyEntity.setDataCount(0);
    historyEntity.setExtraSpec("");
    historyEntity.setDeleted(false);
    mImportExportHistoryMapper.insert(historyEntity);

    Map<String, Object> headerModel = new HashMap<>();
    headerModel.put("tableName", entity.getDisplayName());
    headerModel.put("date", DateUtils.formatLocalDateTime(LocalDateTime.now()));

    exportComponent.exportExcel(fileId, displayId, () -> {
      ExcelCreateModel model = new ExcelCreateModel();

      model.setFileName(dto.getFileName());
      model.setTemplateName("data_export_template");
      model.setModel(headerModel);

      List<String> headers;
      List<String> props;
      {
        headers = new ArrayList<>(Arrays.asList("市/区", "街道", "路"));
        props = new ArrayList<>(Arrays.asList("district", "town", "road"));
      }
      for (DisplayTableColumn columnMeta : columnMetas) {
        headers.add(columnMeta.getName());
        props.add(columnMeta.getId());
      }

      model.setHeaders(headers);
      model.setObjectProps(props);

      List<Map<String, Object>> dataList = displayTableMapper.selectDisplayDataList(param);
      {
        for (Map<String, Object> row : dataList) {
          Object o = row.get("correct_content");
          if (o != null && StringUtils.isNotEmpty(o.toString())) {
            Map map = JSON.parseObject(o.toString(), Map.class);
            for (DisplayTableColumn columnMeta : columnMetas) {
              if (columnMeta.isMatch()) {
                String id = columnMeta.getId();
                String correct = (String)map.get(id);
                if (StringUtils.isNotEmpty(correct)) {
                  row.put(id, correct);
                }
              }
            }
          }
        }
      }
      model.setData(new ArrayList<>(dataList));
      // model.setMergeCells();
      model.setTableMode(TableMode.DISPLAY);

      return model;
    }, SecuritySupport.getLoginUserId());

    return new BaseResponse();
  }

  public BaseResponse updateMatchData(String displayId, UpdateMatchDataDto dto) {

    String correctContent = displayTableMapper.getCorrectContent(displayId, dto.getDataId());
    Map<String, String> map;
    if (StringUtils.isEmpty(correctContent)) {
      map = new HashMap<>();
    } else {
      map = JSON.parseObject(correctContent, Map.class);
    }
    map.put(dto.getColumnId(), dto.getValue());
    map.put("updatedBy", SecuritySupport.getLoginUserId());
    if (StringUtils.isNotEmpty(dto.getCleanTextId())) {
      map.put("ctId", dto.getCleanTextId());
      map.put("ct", dto.getCleanText());
    }
    displayTableMapper.updateCorrectContent(displayId, dto.getDataId(), JSON.toJSONString(map));
    return new BaseResponse();
  }

  @Transactional
  public BaseResponse submitModel(String displayId, SubmitModelDto dto) {

    Optional<MDisplayMetaEntity> optEntity = mDisplayMetaMapper.selectOne(
      c -> c.where().and(MDisplayMetaNames.displayId, SqlBuilder.isEqualTo(displayId))
        .and(MTableMetaNames.projectId, SqlBuilder.isEqualTo(SecuritySupport.getCurrentProject())));
    if (!optEntity.isPresent()) {
      return MessageResponse.newErrorMessage("该展示视图不存在，请返回一览页面刷新后再进行操作。");
    }
    Integer dataId = dto.getDataId();

    MDisplayMetaEntity displayEntity = optEntity.get();
    String tableId = displayEntity.getTableId();
    String displayMetaText = displayEntity.getDisplayMeta();
    DisplayMeta displayMeta = JSON.parseObject(displayMetaText, DisplayMeta.class);

    List<MatchFieldColumn> matchColumnList = displayMeta.getMatchColumnList();

    String contentText = displayTableMapper.getContent(tableId, dataId);

    String correctContent = displayTableMapper.getCorrectContent(displayId, dto.getDataId());
    Map<String, String> correctContentMap;
    if (StringUtils.isEmpty(correctContent)) {
      correctContentMap = new HashMap<>();
    } else {
      correctContentMap = JSON.parseObject(correctContent, Map.class);
    }
    String ctId = correctContentMap.get("ctId");

    for (MatchFieldColumn matchColumn : matchColumnList) {
      String field = matchColumn.getColumnField();
      if (!StringUtils.equals(dto.getColumnId(), field)) {
        continue;
      }
      String modelId = matchColumn.getModelId();
      List<String> targetColumns = matchColumn.getTargetColumns();

      Map<String, String> dataMap = JSON.parseObject(contentText, Map.class);

      Optional<MModelMetaEntity> modelMetaEntity = mModelMetaMapper.selectByPrimaryKey(modelId);
      if (!modelMetaEntity.isPresent()) {
        continue;
      }

      String cleanText;
      if (ctId == null) {
        // clean Text
        String originText = targetColumns.stream().map(e -> dataMap.getOrDefault(e, "")).collect(Collectors.joining("。"));
        cleanText = aiAnalyticsComponent.cleanText(originText);
        ctId = StringUtils.encodeByMD5(originText);
        correctContentMap.put("ctId", ctId);
        correctContentMap.put("ct", cleanText);
      } else {
        cleanText = correctContentMap.get("ct");
        if (StringUtils.isEmpty(cleanText)) {
          String originText = targetColumns.stream().map(e -> dataMap.getOrDefault(e, "")).collect(Collectors.joining("。"));
          cleanText = aiAnalyticsComponent.cleanText(originText);
          correctContentMap.put("ct", cleanText);
        }
      }

      // 同步到model
      MModelMetaEntity mModelMetaEntity = modelMetaEntity.get();
      String modelMetaText = mModelMetaEntity.getModelMeta();
      ModelMeta modelMeta = JSON.parseObject(modelMetaText, ModelMeta.class);
      List<AiModelColumnMeta> aiLabelList = modelMeta.getAiLabelList();

      RuleText ruleText = new RuleText(ctId, cleanText);

      for (AiModelColumnMeta meta : aiLabelList) {
        if (StringUtils.equals(meta.getLabelText(), dto.getCorrectContent())) {
          if (!meta.caseList.contains(ruleText)) {
            meta.caseList.add(ruleText);
          } else {
            return MessageResponse.newWarningMessage("该标签已经提交到了模型库中。");
          }
        } else {
          meta.caseList.remove(ruleText);
        }
      }
      mModelMetaEntity.setModelMeta(JSON.toJSONString(modelMeta));
      mModelMetaMapper.updateByPrimaryKey(mModelMetaEntity);

      UpdateMatchDataDto matchDataDto = new UpdateMatchDataDto();
      matchDataDto.setDataId(dto.getDataId());
      matchDataDto.setColumnId(dto.getColumnId());
      matchDataDto.setValue(dto.getCorrectContent());
      matchDataDto.setCleanTextId(ctId);
      matchDataDto.setCleanText(cleanText);
      updateMatchData(displayId, matchDataDto);
    }
    return MessageResponse.newInfoMessage("提交到模型库操作成功。");
  }
}
