package com.data.datatools.common.listener;

import com.data.datatools.component.AnalyticsComponent;
import com.data.datatools.component.AreaComponent;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
public class AnalyticsEventListener {

  @Autowired
  private AnalyticsComponent analyticsComponent;

  @Autowired
  private AreaComponent areaComponent;

  @Async
  @EventListener(classes = {AnalyticsEvent.class})
  public void handleAsyncEvent(AnalyticsEvent event) {
    // 处理异步事件
    event.getOperator().reset();
    String tableId = event.getTableId();
    String displayId = event.getDisplayId();
    if (displayId == null) {
      analyticsComponent.analytics(tableId);
    } else {
      analyticsComponent.analytics(tableId, displayId);
    }
  }

  @Async
  @EventListener(classes = {AreaMatchEvent.class})
  public void handleAreaMatchEvent(AreaMatchEvent event) {
    // 处理异步事件
    event.getOperator().reset();
    String tableId = event.getTableId();
    Map<String, String> areaMap = event.getAreaMap();
    areaComponent.analytics(tableId, areaMap);
  }
}
