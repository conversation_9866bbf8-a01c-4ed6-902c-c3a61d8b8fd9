package com.data.datatools.repository.master.mapper;

import java.sql.JDBCType;
import java.time.LocalDateTime;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class MDisplayMetaNames {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_display_meta")
    public static final MDisplayMeta MDisplayMeta = new MDisplayMeta();

    /**
     * 主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_display_meta.display_id")
    public static final SqlColumn<String> displayId = MDisplayMeta.displayId;

    /**
     * 展示名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_display_meta.display_name")
    public static final SqlColumn<String> displayName = MDisplayMeta.displayName;

    /**
     * 原始表名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_display_meta.table_id")
    public static final SqlColumn<String> tableId = MDisplayMeta.tableId;

    /**
     * 所属项目ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_display_meta.project_id")
    public static final SqlColumn<String> projectId = MDisplayMeta.projectId;

    /**
     * 模型类型
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_display_meta.model_type")
    public static final SqlColumn<String> modelType = MDisplayMeta.modelType;

    /**
     * 显示新建存量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_display_meta.show_existing")
    public static final SqlColumn<Boolean> showExisting = MDisplayMeta.showExisting;

    /**
     * 状态
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_display_meta.status")
    public static final SqlColumn<String> status = MDisplayMeta.status;

    /**
     * 智能分析日时
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_display_meta.analytics_time")
    public static final SqlColumn<LocalDateTime> analyticsTime = MDisplayMeta.analyticsTime;

    /**
     * 智能分析件数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_display_meta.analytics_count")
    public static final SqlColumn<Long> analyticsCount = MDisplayMeta.analyticsCount;

    /**
     * 描述
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_display_meta.remark")
    public static final SqlColumn<String> remark = MDisplayMeta.remark;

    /**
     * 是否删除
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_display_meta.deleted")
    public static final SqlColumn<Boolean> deleted = MDisplayMeta.deleted;

    /**
     * 插入时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_display_meta.created_at")
    public static final SqlColumn<LocalDateTime> createdAt = MDisplayMeta.createdAt;

    /**
     * 做成者
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_display_meta.created_by")
    public static final SqlColumn<String> createdBy = MDisplayMeta.createdBy;

    /**
     * 更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_display_meta.updated_at")
    public static final SqlColumn<LocalDateTime> updatedAt = MDisplayMeta.updatedAt;

    /**
     * 更新者
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_display_meta.updated_by")
    public static final SqlColumn<String> updatedBy = MDisplayMeta.updatedBy;

    /**
     * 展示Meta
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_display_meta.display_meta")
    public static final SqlColumn<String> displayMeta = MDisplayMeta.displayMeta;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_display_meta")
    public static final class MDisplayMeta extends SqlTable {
        public final SqlColumn<String> displayId = column("display_id", JDBCType.CHAR);

        public final SqlColumn<String> displayName = column("display_name", JDBCType.VARCHAR);

        public final SqlColumn<String> tableId = column("table_id", JDBCType.CHAR);

        public final SqlColumn<String> projectId = column("project_id", JDBCType.CHAR);

        public final SqlColumn<String> modelType = column("model_type", JDBCType.CHAR);

        public final SqlColumn<Boolean> showExisting = column("show_existing", JDBCType.BIT);

        public final SqlColumn<String> status = column("status", JDBCType.VARCHAR);

        public final SqlColumn<LocalDateTime> analyticsTime = column("analytics_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> analyticsCount = column("analytics_count", JDBCType.BIGINT);

        public final SqlColumn<String> remark = column("remark", JDBCType.VARCHAR);

        public final SqlColumn<Boolean> deleted = column("deleted", JDBCType.BIT);

        public final SqlColumn<LocalDateTime> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createdBy = column("created_by", JDBCType.VARCHAR);

        public final SqlColumn<LocalDateTime> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updatedBy = column("updated_by", JDBCType.VARCHAR);

        public final SqlColumn<String> displayMeta = column("display_meta", JDBCType.LONGVARCHAR);

        public MDisplayMeta() {
            super("m_display_meta");
        }
    }
}