package com.data.datatools.config.security.model;

import java.util.Collection;
import java.util.Date;
import lombok.Data;
import lombok.Getter;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.User;

public class LoginUser extends User {

  private static final long serialVersionUID = 5366890930439174556L;

  @Getter
  private final String userId;

  @Getter
  private final String userName;

  @Getter
  private final String userPwd;

  @Getter
  private String currentProject;

  @Getter
  private final long loginTime;

  @Getter
  private final boolean solidUser;

  @Getter
  private final String userType;
  @Getter
  private final String city;
  @Getter
  private final String district;
  @Getter
  private final String street;

  public LoginUser(UserParam param) {
    super(param.getUserId(), param.getUserPwd(), param.isAccountEnabled(), true, true,
      !param.isAccountLocked(), param.getAuthorities());
    this.userId = param.getUserId();
    this.userName = param.getUserName();
    this.loginTime = new Date().getTime();
    this.userPwd = param.getUserPwd();
    this.currentProject = param.getCurrentProject();
    this.solidUser = param.isSolidUser();
    this.userType = param.getUserType();
    this.city = param.getCity();
    this.district = param.getDistrict();
    this.street = param.getStreet();
  }

  @Override
  public boolean equals(Object rhs) {
    return super.equals(rhs);
  }

  @Override
  public int hashCode() {
    return super.hashCode();
  }

  public void switchProject(String currentProject) {
    this.currentProject = currentProject;
  }

  @Data
  public static class UserParam {

    String userId;
    String userName;
    String userPwd;
    String currentProject;
    boolean accountLocked;
    boolean accountEnabled;
    boolean solidUser;
    String userType;
    String city;
    String district;
    String street;
    Collection<? extends GrantedAuthority> authorities;
  }
}
