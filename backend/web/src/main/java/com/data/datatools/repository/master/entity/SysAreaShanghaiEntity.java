package com.data.datatools.repository.master.entity;

import com.data.datatools.common.base.dao.BaseEntity;
import java.io.Serializable;
import javax.annotation.Generated;

/**
 * 上海区划表
 *
 * database table [data_analytics..sys_area_shanghai]
 */
public class SysAreaShanghaiEntity implements Serializable, BaseEntity {
    /**
     * 主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_area_shanghai.id")
    private Integer id;

    /**
     * 省
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_area_shanghai.prov")
    private String prov;

    /**
     * 市
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_area_shanghai.city")
    private String city;

    /**
     * 区
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_area_shanghai.district")
    private String district;

    /**
     * 镇/街道
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_area_shanghai.town")
    private String town;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_area_shanghai")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_area_shanghai.id")
    public Integer getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_area_shanghai.id")
    public void setId(Integer id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_area_shanghai.prov")
    public String getProv() {
        return prov;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_area_shanghai.prov")
    public void setProv(String prov) {
        this.prov = prov;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_area_shanghai.city")
    public String getCity() {
        return city;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_area_shanghai.city")
    public void setCity(String city) {
        this.city = city;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_area_shanghai.district")
    public String getDistrict() {
        return district;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_area_shanghai.district")
    public void setDistrict(String district) {
        this.district = district;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_area_shanghai.town")
    public String getTown() {
        return town;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_area_shanghai.town")
    public void setTown(String town) {
        this.town = town;
    }
}