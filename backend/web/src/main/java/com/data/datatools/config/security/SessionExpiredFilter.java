package com.data.datatools.config.security;

import java.io.IOException;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import com.data.datatools.common.constants.ICommonConstants;
import com.data.datatools.common.response.BaseResponse;
import com.data.datatools.common.response.MessageResponse;
import org.springframework.web.filter.GenericFilterBean;

public class SessionExpiredFilter extends GenericFilterBean {

  @Override
  public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse,
      Fi<PERSON><PERSON>hain filterChain) throws IOException, ServletException {
    HttpServletRequest request = (HttpServletRequest) servletRequest;
    HttpServletResponse response = (HttpServletResponse) servletResponse;

    if (isTarget(request) && isSessionTimeout(request)) {
      // isAjaxRequest
      logger.info("session timeout!");
      String uri = request.getServletPath();
      if (uri.startsWith("/session/check") && "true".equals(request.getParameter("toLogin"))) {
        MessageResponse messageResponse = MessageResponse.newInstance();
        messageResponse.setCustom(true);
        ResponseSupport.jsonResponse(response, messageResponse);
      } else {
        BaseResponse data = new BaseResponse(ICommonConstants.RESPONSE_STATUS_CODE_FAILED_TOKEN_EXPIRED);
        ResponseSupport.jsonResponse(response, data);
      }
      return;
    }

    filterChain.doFilter(request, response);
  }

  private boolean isTarget(HttpServletRequest request) {
    String uri = request.getServletPath();
    return !uri.equals("") && !uri.equals("/") //
        && !uri.startsWith("/auth/login") //
        && !uri.startsWith("/error") //
        && !uri.startsWith("/health/check") // ヘルスチェックAPI
        && !uri.startsWith("/password/change") // パスワード変更
        && !uri.startsWith("/version");
  }

  private boolean isSessionTimeout(HttpServletRequest request) {
    HttpSession currentSession = request.getSession(false);
    if (currentSession == null) {
      return true;
    }
    String requestSessionId = request.getRequestedSessionId();
    boolean isValid = request.isRequestedSessionIdValid();
    return requestSessionId == null || !isValid || !requestSessionId.equals(currentSession.getId());
  }
}
