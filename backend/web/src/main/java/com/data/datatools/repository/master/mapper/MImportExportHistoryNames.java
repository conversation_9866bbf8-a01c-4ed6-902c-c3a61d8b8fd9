package com.data.datatools.repository.master.mapper;

import java.sql.JDBCType;
import java.time.LocalDateTime;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class MImportExportHistoryNames {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_import_export_history")
    public static final MImportExportHistory MImportExportHistory = new MImportExportHistory();

    /**
     * 文件ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_import_export_history.file_id")
    public static final SqlColumn<String> fileId = MImportExportHistory.fileId;

    /**
     * 源数据表ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_import_export_history.table_id")
    public static final SqlColumn<String> tableId = MImportExportHistory.tableId;

    /**
     * 模式 0：原始数据 1：算分数据
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_import_export_history.table_mode")
    public static final SqlColumn<String> tableMode = MImportExportHistory.tableMode;

    /**
     * 类型 0：导入 1：导出
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_import_export_history.type")
    public static final SqlColumn<String> type = MImportExportHistory.type;

    /**
     * 状态 0：进行中 1：完成 2：失败
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_import_export_history.status")
    public static final SqlColumn<String> status = MImportExportHistory.status;

    /**
     * 文件名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_import_export_history.export_file")
    public static final SqlColumn<String> exportFile = MImportExportHistory.exportFile;

    /**
     * 数据件数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_import_export_history.data_count")
    public static final SqlColumn<Integer> dataCount = MImportExportHistory.dataCount;

    /**
     * 额外信息
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_import_export_history.extra_spec")
    public static final SqlColumn<String> extraSpec = MImportExportHistory.extraSpec;

    /**
     * 是否删除
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_import_export_history.deleted")
    public static final SqlColumn<Boolean> deleted = MImportExportHistory.deleted;

    /**
     * 插入时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_import_export_history.created_at")
    public static final SqlColumn<LocalDateTime> createdAt = MImportExportHistory.createdAt;

    /**
     * 做成者
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_import_export_history.created_by")
    public static final SqlColumn<String> createdBy = MImportExportHistory.createdBy;

    /**
     * 更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_import_export_history.updated_at")
    public static final SqlColumn<LocalDateTime> updatedAt = MImportExportHistory.updatedAt;

    /**
     * 更新者
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_import_export_history.updated_by")
    public static final SqlColumn<String> updatedBy = MImportExportHistory.updatedBy;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_import_export_history")
    public static final class MImportExportHistory extends SqlTable {
        public final SqlColumn<String> fileId = column("file_id", JDBCType.VARCHAR);

        public final SqlColumn<String> tableId = column("table_id", JDBCType.CHAR);

        public final SqlColumn<String> tableMode = column("table_mode", JDBCType.CHAR);

        public final SqlColumn<String> type = column("type", JDBCType.CHAR);

        public final SqlColumn<String> status = column("status", JDBCType.CHAR);

        public final SqlColumn<String> exportFile = column("export_file", JDBCType.VARCHAR);

        public final SqlColumn<Integer> dataCount = column("data_count", JDBCType.INTEGER);

        public final SqlColumn<String> extraSpec = column("extra_spec", JDBCType.VARCHAR);

        public final SqlColumn<Boolean> deleted = column("deleted", JDBCType.BIT);

        public final SqlColumn<LocalDateTime> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createdBy = column("created_by", JDBCType.VARCHAR);

        public final SqlColumn<LocalDateTime> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updatedBy = column("updated_by", JDBCType.VARCHAR);

        public MImportExportHistory() {
            super("m_import_export_history");
        }
    }
}