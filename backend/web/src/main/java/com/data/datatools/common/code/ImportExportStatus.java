package com.data.datatools.common.code;

public enum ImportExportStatus {
  ING("0", "进行中"), DONE("1", "完成"), ERR("2", "失败"), ERR_TO("3", "失败 (超时)");

  private final String status;
  private final String statusName;

  ImportExportStatus(String status, String statusName) {
    this.status = status;
    this.statusName = statusName;
  }

  public static String getStatusName(String status) {
    for (ImportExportStatus value : ImportExportStatus.values()) {
      if (value.status.equals(status)) {
        return value.statusName;
      }
    }
    return "";
  }

  public String getStatus() {
    return this.status;
  }

  public boolean is(String status) {
    return this.status.equalsIgnoreCase(status);
  }
}
