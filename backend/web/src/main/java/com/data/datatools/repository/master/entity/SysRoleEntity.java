package com.data.datatools.repository.master.entity;

import com.data.datatools.common.base.dao.BaseEntity;
import java.io.Serializable;
import java.time.LocalDateTime;
import javax.annotation.Generated;

/**
 * 角色表
 *
 * database table [data_analytics..sys_role]
 */
public class SysRoleEntity implements Serializable, BaseEntity {
    /**
     * 主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_role.id")
    private Integer id;

    /**
     * 角色ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_role.role_id")
    private String roleId;

    /**
     * 角色名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_role.role_name")
    private String roleName;

    /**
     * 角色类型：1:个人用；2：组织用
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_role.role_type")
    private String roleType;

    /**
     * 显示顺序
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_role.show_sequence")
    private Short showSequence;

    /**
     * 是否删除
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_role.deleted")
    private Boolean deleted;

    /**
     * 插入时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_role.created_at")
    private LocalDateTime createdAt;

    /**
     * 做成者
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_role.created_by")
    private String createdBy;

    /**
     * 更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_role.updated_at")
    private LocalDateTime updatedAt;

    /**
     * 更新者
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_role.updated_by")
    private String updatedBy;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_role")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_role.id")
    public Integer getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_role.id")
    public void setId(Integer id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_role.role_id")
    public String getRoleId() {
        return roleId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_role.role_id")
    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_role.role_name")
    public String getRoleName() {
        return roleName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_role.role_name")
    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_role.role_type")
    public String getRoleType() {
        return roleType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_role.role_type")
    public void setRoleType(String roleType) {
        this.roleType = roleType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_role.show_sequence")
    public Short getShowSequence() {
        return showSequence;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_role.show_sequence")
    public void setShowSequence(Short showSequence) {
        this.showSequence = showSequence;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_role.deleted")
    public Boolean getDeleted() {
        return deleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_role.deleted")
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_role.created_at")
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_role.created_at")
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_role.created_by")
    public String getCreatedBy() {
        return createdBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_role.created_by")
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_role.updated_at")
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_role.updated_at")
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_role.updated_by")
    public String getUpdatedBy() {
        return updatedBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_role.updated_by")
    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }
}