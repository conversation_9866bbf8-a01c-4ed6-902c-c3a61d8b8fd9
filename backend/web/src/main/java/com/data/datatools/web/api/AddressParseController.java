package com.data.datatools.web.api;

import org.springframework.web.bind.annotation.*;
import java.util.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import javax.servlet.http.HttpServletRequest;

/**
 * 地址解析接口（示例实现）
 * 实际使用时应该替换为真实的地址解析服务
 */
@RestController
@RequestMapping("/api/address")
public class AddressParseController {

    /**
     * 地址解析接口（新格式）
     * @param request 包含query、response_mode、user、inputs的请求
     * @param httpRequest HTTP请求对象，用于获取请求头
     * @return 符合新格式的响应
     */
    @PostMapping("/parse")
    public Map<String, Object> parseAddress(@RequestBody Map<String, Object> request,
                                           HttpServletRequest httpRequest) {
        // 验证Authorization头（示例实现）
        String authHeader = httpRequest.getHeader("Authorization");
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            return createErrorResponse("缺少Authorization头");
        }

        String token = authHeader.substring(7); // 移除"Bearer "前缀
        if (!"app-HA2DDqGPLXxYxUpSilonkItC".equals(token)) {
            return createErrorResponse("无效的Authorization token");
        }

        String query = (String) request.get("query");
        String user = (String) request.get("user");

        if (query == null || query.trim().isEmpty()) {
            return createErrorResponse("查询参数为空");
        }

        // 这里是示例实现，实际应该调用真实的地址解析服务
        List<String> addresses = mockAddressParse(query.trim());

        return createSuccessResponse(addresses, user);
    }
    
    /**
     * 模拟地址解析（示例实现）
     * 实际使用时应该替换为真实的解析逻辑
     */
    private List<String> mockAddressParse(String address) {
        List<String> results = new ArrayList<>();
        
        // 示例1: 川沙路4625弄 -> 返回多个地址
        if (address.contains("川沙路4625弄")) {
            results.add("川沙路4625弄1号");
            results.add("川沙路4625弄2号");
            results.add("川沙路4625弄3号");
            return results;
        }
        
        // 示例2: 进贤路250弄 -> 返回多个地址
        if (address.contains("进贤路250弄")) {
            results.add("进贤路250弄1号");
            results.add("进贤路250弄2号");
            return results;
        }
        
        // 示例3: 华夏东路2139弄 -> 返回多个地址
        if (address.contains("华夏东路2139弄")) {
            results.add("华夏东路2139弄1号");
            results.add("华夏东路2139弄2号");
            results.add("华夏东路2139弄3号");
            results.add("华夏东路2139弄4号");
            return results;
        }
        
        // 默认情况：如果没有匹配的规则，返回原地址
        results.add(address);
        return results;
    }

    /**
     * 创建成功响应
     */
    private Map<String, Object> createSuccessResponse(List<String> addresses, String user) {
        Map<String, Object> response = new HashMap<>();
        response.put("event", "message");
        response.put("task_id", UUID.randomUUID().toString());
        response.put("id", UUID.randomUUID().toString());
        response.put("message_id", UUID.randomUUID().toString());
        response.put("conversation_id", UUID.randomUUID().toString());
        response.put("mode", "advanced-chat");

        // 将地址列表转换为JSON字符串
        try {
            com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
            String answerJson = mapper.writeValueAsString(addresses);
            response.put("answer", answerJson);
        } catch (Exception e) {
            response.put("answer", "[]");
        }

        // 添加metadata
        Map<String, Object> metadata = new HashMap<>();
        Map<String, Object> usage = new HashMap<>();
        usage.put("prompt_tokens", 3023);
        usage.put("completion_tokens", 40);
        usage.put("total_tokens", 3063);
        usage.put("total_price", "0");
        usage.put("currency", "USD");
        usage.put("latency", 1.5);
        metadata.put("usage", usage);
        metadata.put("annotation_reply", null);
        metadata.put("retriever_resources", new ArrayList<>());
        response.put("metadata", metadata);

        response.put("created_at", System.currentTimeMillis() / 1000);

        return response;
    }

    /**
     * 创建错误响应
     */
    private Map<String, Object> createErrorResponse(String errorMessage) {
        Map<String, Object> response = new HashMap<>();
        response.put("event", "error");
        response.put("task_id", UUID.randomUUID().toString());
        response.put("answer", "[]");
        response.put("error", errorMessage);
        response.put("created_at", System.currentTimeMillis() / 1000);
        return response;
    }
}
