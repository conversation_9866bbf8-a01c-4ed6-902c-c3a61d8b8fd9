package com.data.datatools.component.dto.analytics;

import com.data.datatools.utils.StringUtils;
import com.hankcs.hanlp.dictionary.CustomDictionary;
import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

@Getter
public class AnalyticsKeys {

  private final Text text;

  private final Map<String, Float> keys = new HashMap<>();

  private final Float basics;

  public AnalyticsKeys(int index, String text, String keyTexts, Float basics) {
    this.text = new Text(index, text);
    if (StringUtils.isNotEmpty(keyTexts)) {
      for (String keyText : keyTexts.split(";")) {
        if (keyText.contains("#")) {
          String[] kv = keyText.split("#");
          keys.put(kv[0], Float.parseFloat(kv[1]));
          // 自定义词库
          CustomDictionary.add(kv[0]);
        }
      }
    }
    this.basics = basics;
  }

  @Data
  @AllArgsConstructor
  public static class Text {
    private int index;
    private String text;

    @Override
    public String toString() {
      return index + "." + text;
    }
  }
}
