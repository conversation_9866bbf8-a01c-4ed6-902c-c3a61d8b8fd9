package com.data.datatools.web.system.user;

import com.data.datatools.common.response.BaseResponse;
import com.data.datatools.common.response.ListResponse;
import com.data.datatools.common.response.MessageResponse;
import com.data.datatools.web.system.user.model.IdList;
import com.data.datatools.web.system.user.model.PasswordForm;
import com.data.datatools.web.system.user.model.UserCondition;
import com.data.datatools.web.system.user.model.UserInfoModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class UserController {

  @Autowired
  private UserService userService;

  @PostMapping(value = "/user/search")
  public BaseResponse searchUser(@RequestBody UserCondition userCondition) {

    ListResponse<UserInfoModel> listResponse = new ListResponse<>();
    userService.searchByPagingWithCond(userCondition, listResponse);
    return listResponse;
  }

  @PostMapping(value = "/user/get")
  public BaseResponse getUser(@RequestBody UserCondition user) {
    return userService.getUser(user);
  }

  @PostMapping(value = "/user/add")
  public BaseResponse addUser(@RequestBody UserInfoModel user) {

    return userService.addUser(user);
  }

  @PostMapping(value = "/user/update")
  public BaseResponse updateUser(@RequestBody UserInfoModel user) {
    MessageResponse messageResponse = MessageResponse.newInstance();

    return userService.updateUser(user, messageResponse);
  }

  @PostMapping(value = "/user/delete")
  public BaseResponse deleteUser(@RequestBody UserInfoModel user) {
    MessageResponse messageResponse = MessageResponse.newInstance();
    userService.deleteUser(user.getUserId(), messageResponse);
    if (messageResponse.hasError()) {
      return messageResponse;
    }
    return new BaseResponse();
  }

  @PostMapping(value = "/user/multipleDelete")
  public BaseResponse deleteUsers(@RequestBody IdList userLst) {
    MessageResponse messageResponse = MessageResponse.newInstance();
    userService.deleteUsers(userLst.getUserList(), messageResponse);

    if (messageResponse.hasError()) {
      return messageResponse;
    }
    return new BaseResponse();
  }

  @PostMapping("/user/password")
  public BaseResponse updatePassword(@RequestBody PasswordForm form) {
    return userService.updatePassword(form);
  }
}
