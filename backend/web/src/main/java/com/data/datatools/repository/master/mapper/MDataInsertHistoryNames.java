package com.data.datatools.repository.master.mapper;

import java.sql.JDBCType;
import java.time.LocalDateTime;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class MDataInsertHistoryNames {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_data_insert_history")
    public static final MDataInsertHistory MDataInsertHistory = new MDataInsertHistory();

    /**
     * 主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_data_insert_history.id")
    public static final SqlColumn<Integer> id = MDataInsertHistory.id;

    /**
     * 数据表ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_data_insert_history.table_id")
    public static final SqlColumn<String> tableId = MDataInsertHistory.tableId;

    /**
     * Key
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_data_insert_history.import_key")
    public static final SqlColumn<String> importKey = MDataInsertHistory.importKey;

    /**
     * 导入类型 1：文件导入 2：手动录入
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_data_insert_history.import_type")
    public static final SqlColumn<Integer> importType = MDataInsertHistory.importType;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_data_insert_history.import_time")
    public static final SqlColumn<LocalDateTime> importTime = MDataInsertHistory.importTime;

    /**
     * 插入时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_data_insert_history.created_at")
    public static final SqlColumn<LocalDateTime> createdAt = MDataInsertHistory.createdAt;

    /**
     * 做成者
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_data_insert_history.created_by")
    public static final SqlColumn<String> createdBy = MDataInsertHistory.createdBy;

    /**
     * Tags, etc.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_data_insert_history.import_meta")
    public static final SqlColumn<String> importMeta = MDataInsertHistory.importMeta;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_data_insert_history")
    public static final class MDataInsertHistory extends SqlTable {
        public final SqlColumn<Integer> id = column("id", JDBCType.INTEGER);

        public final SqlColumn<String> tableId = column("table_id", JDBCType.CHAR);

        public final SqlColumn<String> importKey = column("import_key", JDBCType.CHAR);

        public final SqlColumn<Integer> importType = column("import_type", JDBCType.INTEGER);

        public final SqlColumn<LocalDateTime> importTime = column("import_time", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createdBy = column("created_by", JDBCType.VARCHAR);

        public final SqlColumn<String> importMeta = column("import_meta", JDBCType.LONGVARCHAR);

        public MDataInsertHistory() {
            super("m_data_insert_history");
        }
    }
}