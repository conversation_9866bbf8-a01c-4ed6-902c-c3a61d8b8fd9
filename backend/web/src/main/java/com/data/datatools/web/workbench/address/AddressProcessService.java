package com.data.datatools.web.workbench.address;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.data.datatools.common.response.BaseResponse;
import com.data.datatools.common.response.DataResponse;
import com.data.datatools.common.response.MessageResponse;
import com.data.datatools.component.ExcelComponent;
import com.data.datatools.utils.StringUtils;
import com.data.datatools.web.workbench.address.dto.AddressProcessDto;
import com.data.datatools.web.workbench.address.parser.AddressParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.poi.ss.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Service
public class AddressProcessService {

    private static final Logger logger = LoggerFactory.getLogger(AddressProcessService.class);

    @Autowired
    private ExcelComponent excelComponent;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    @Value("${export.excel.base.path}")
    private String basePath;

    @Value("${address.parse.mode:local}")
    private String addressParseMode; // local: 本地解析, api: 接口调用

    @Value("${address.parse.api.url:http://*************:60012/v1/chat-messages}")
    private String addressParseApiUrl;

    @Value("${address.parse.api.user:王韬}")
    private String addressParseApiUser; // API调用时的用户名

    @Value("${address.parse.api.token:app-HA2DDqGPLXxYxUpSilonkItC}")
    private String addressParseApiToken; // API调用时的Authorization token

    @Value("${address.parse.api.retry.count:2}")
    private int apiRetryCount; // API调用重试次数

    // 任务状态管理
    private final Map<String, TaskStatus> taskStatusMap = new ConcurrentHashMap<>();
    private final Map<String, TaskProgress> taskProgressMap = new ConcurrentHashMap<>();

    /**
     * 任务状态枚举
     */
    public enum TaskStatus {
        PROCESSING("处理中"),
        COMPLETED("已完成"),
        FAILED("处理失败");

        private final String description;

        TaskStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 任务进度信息
     */
    public static class TaskProgress {
        private int totalRows;
        private int processedRows;
        private String currentStep;

        public TaskProgress(int totalRows, int processedRows, String currentStep) {
            this.totalRows = totalRows;
            this.processedRows = processedRows;
            this.currentStep = currentStep;
        }

        // Getters and setters
        public int getTotalRows() { return totalRows; }
        public void setTotalRows(int totalRows) { this.totalRows = totalRows; }
        public int getProcessedRows() { return processedRows; }
        public void setProcessedRows(int processedRows) { this.processedRows = processedRows; }
        public String getCurrentStep() { return currentStep; }
        public void setCurrentStep(String currentStep) { this.currentStep = currentStep; }

        public double getProgressPercentage() {
            return totalRows > 0 ? (double) processedRows / totalRows * 100 : 0;
        }
    }

    /**
     * 启动地址处理任务（立即返回任务ID，异步处理）
     */
    public BaseResponse processAddressExcel(AddressProcessDto dto) {
        try {
            MultipartFile file = dto.getFile();
            if (file == null || file.isEmpty()) {
                return MessageResponse.newErrorMessage("请选择要处理的Excel文件");
            }

            // 生成任务ID
            String taskId = generateFileId();

            // 设置任务状态为处理中
            taskStatusMap.put(taskId, TaskStatus.PROCESSING);

            // 立即启动异步处理，不等待结果
            processAddressExcelAsync(file, taskId);

            // 立即返回任务ID，不等待处理完成
            Map<String, Object> result = new HashMap<>();
            result.put("taskId", taskId);
            result.put("status", TaskStatus.PROCESSING.getDescription());

            logger.info("地址处理任务已启动，任务ID: {}", taskId);
            return DataResponse.of(result);

        } catch (Exception e) {
            logger.error("启动地址处理任务时发生错误", e);
            return MessageResponse.newErrorMessage("启动处理失败：" + e.getMessage());
        }
    }

    /**
     * 异步处理Excel中的地址数据
     */
    @Async("taskExecutor")
    public void processAddressExcelAsync(MultipartFile file, String taskId) {
        logger.info("异步任务开始执行，任务ID: {}, 线程: {}", taskId, Thread.currentThread().getName());
        try {

            // 存储原始数据和处理后的结果
            List<Map<String, String>> originalRows = new ArrayList<>();
            List<Map<String, String>> processedRows = new ArrayList<>();
            Map<Integer, String> headerMap = new HashMap<>();

            // 读取Excel文件
            ExcelComponent.ExcelData excelData = excelComponent.readExcelWithHeaderCheck(file, 100,
                headerMapCheck -> {
                    // 检查是否包含"地址"列
                    headerMap.putAll(headerMapCheck);
                    return !headerMapCheck.containsValue("地址");
                },
                rowList -> {
                    for (ExcelComponent.ExcelRowData<Map<String, String>> row : rowList) {
                        Map<String, String> dataMap = row.getData();

                        // 保存原始行数据（保持原始顺序）
                        Map<String, String> originalRowData = new HashMap<>(dataMap);
                        originalRowData.put("_rowIndex", String.valueOf(originalRows.size())); // 添加行索引
                        originalRows.add(originalRowData);

                        // 暂时不处理地址，先保存原始数据
                        // 地址处理将在后续异步进行
                    }
                });

            // 检查Excel读取是否有错误
            if (excelData.isHeaderError()) {
                taskStatusMap.put(taskId, TaskStatus.FAILED);
                logger.error("Excel文件格式错误：未找到地址列，任务ID: {}", taskId);
                return;
            }

            if (excelData.isNodataError()) {
                taskStatusMap.put(taskId, TaskStatus.FAILED);
                logger.error("Excel文件中没有数据，任务ID: {}", taskId);
                return;
            }

            logger.info("开始异步处理地址，任务ID: {}, 原始行数: {}", taskId, originalRows.size());

            // 初始化进度信息
            taskProgressMap.put(taskId, new TaskProgress(originalRows.size(), 0, "开始处理地址"));

            // 异步处理每行的地址
            for (int i = 0; i < originalRows.size(); i++) {
                Map<String, String> originalRow = originalRows.get(i);
                String addressValue = originalRow.get("地址");

                if (addressValue != null && !addressValue.trim().isEmpty()) {
                    try {
                        // 调用接口解析地址
                        List<String> addresses = processAddress(addressValue.trim());

                        // 为每个处理后的地址创建新行
                        for (String processedAddress : addresses) {
                            Map<String, String> processedRow = new HashMap<>(originalRow);
                            processedRow.put("地址", processedAddress);
                            processedRow.put("_originalRowIndex", String.valueOf(i)); // 关联原始行
                            processedRows.add(processedRow);
                        }

                        // 更新进度信息
                        TaskProgress progress = taskProgressMap.get(taskId);
                        if (progress != null) {
                            progress.setProcessedRows(i + 1);
                            progress.setCurrentStep("正在处理第 " + (i + 1) + " 行地址");
                        }

                        // 每处理10行记录一次进度
                        if ((i + 1) % 10 == 0) {
                            logger.info("地址处理进度，任务ID: {}, 已处理: {}/{}", taskId, i + 1, originalRows.size());
                        }

                    } catch (Exception e) {
                        logger.error("处理地址时发生错误，任务ID: {}, 行号: {}, 地址: {}", taskId, i, addressValue, e);
                        // 如果处理失败，保留原始地址
                        Map<String, String> processedRow = new HashMap<>(originalRow);
                        processedRow.put("_originalRowIndex", String.valueOf(i));
                        processedRows.add(processedRow);
                    }
                }
            }

            // 更新进度：开始生成Excel
            TaskProgress progress = taskProgressMap.get(taskId);
            if (progress != null) {
                progress.setCurrentStep("正在生成Excel文件");
            }

            // 生成结果Excel文件
            String fileName = "processed_addresses_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".xlsx";

            createResultExcel(originalRows, processedRows, headerMap, taskId, fileName);

            // 设置任务状态为完成
            taskStatusMap.put(taskId, TaskStatus.COMPLETED);

            // 更新进度：任务完成
            if (progress != null) {
                progress.setCurrentStep("任务完成");
            }

            logger.info("地址处理任务完成，任务ID: {}, 原始行数: {}, 处理后行数: {}",
                       taskId, originalRows.size(), processedRows.size());

            // 定时清理任务状态（24小时后）
            scheduleTaskCleanup(taskId);

        } catch (Exception e) {
            logger.error("处理地址Excel文件时发生错误，任务ID: " + taskId, e);
            taskStatusMap.put(taskId, TaskStatus.FAILED);

            // 失败任务也需要清理
            scheduleTaskCleanup(taskId);
        }
    }

    /**
     * 查询任务状态
     */
    public BaseResponse getTaskStatus(String taskId) {
        TaskStatus status = taskStatusMap.get(taskId);
        if (status == null) {
            return MessageResponse.newErrorMessage("任务不存在");
        }

        Map<String, Object> result = new HashMap<>();
        result.put("taskId", taskId);
        result.put("status", status.getDescription());
        result.put("canDownload", status == TaskStatus.COMPLETED);

        // 添加进度信息
        TaskProgress progress = taskProgressMap.get(taskId);
        if (progress != null) {
            result.put("totalRows", progress.getTotalRows());
            result.put("processedRows", progress.getProcessedRows());
            result.put("currentStep", progress.getCurrentStep());
            result.put("progressPercentage", Math.round(progress.getProgressPercentage() * 100) / 100.0);
        }

        return DataResponse.of(result);
    }

    /**
     * 解析地址（根据配置选择本地解析或接口调用）
     */
    private List<String> processAddress(String addressText) {
        if ("api".equalsIgnoreCase(addressParseMode)) {
            return processAddressByApi(addressText);
        } else {
            return processAddressByLocal(addressText);
        }
    }

    /**
     * 调用接口解析地址（带重试机制）
     */
    private List<String> processAddressByApi(String addressText) {
        for (int attempt = 1; attempt <= apiRetryCount; attempt++) {
            try {
                return callAddressParseApiOnce(addressText, attempt);
            } catch (Exception e) {
                logger.warn("API调用第{}次失败，地址: {}，错误: {}", attempt, addressText, e.getMessage());
                if (attempt == apiRetryCount) {
                    logger.error("API调用重试{}次后仍然失败，地址: {}", apiRetryCount, addressText, e);
                    return Arrays.asList(addressText);
                }
                // 等待一段时间后重试
                try {
                    Thread.sleep(1000 * attempt); // 递增等待时间
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    return Arrays.asList(addressText);
                }
            }
        }
        return Arrays.asList(addressText);
    }

    /**
     * 单次API调用
     */
    private List<String> callAddressParseApiOnce(String addressText, int attempt) throws Exception {
        long startTime = System.currentTimeMillis();
        logger.debug("开始第{}次调用地址解析API，地址: {}", attempt, addressText);

        try {
            // 创建请求体，按照新的API格式
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("query", addressText);
            requestBody.put("response_mode", "blocking");
            requestBody.put("user", addressParseApiUser);
            requestBody.put("inputs", new HashMap<>());

            // 创建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + addressParseApiToken);
            headers.set("Content-Type", "application/json");

            // 创建HTTP实体
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

            // 调用地址解析接口
            ResponseEntity<String> response = restTemplate.exchange(
                addressParseApiUrl,
                HttpMethod.POST,
                requestEntity,
                String.class
            );

            long endTime = System.currentTimeMillis();
            logger.debug("API调用完成，耗时: {}ms，状态码: {}", endTime - startTime, response.getStatusCode());

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                // 解析返回的JSON，提取answer字段
                Map<String, Object> responseMap = objectMapper.readValue(
                    response.getBody(),
                    new TypeReference<Map<String, Object>>() {}
                );

                String answerStr = (String) responseMap.get("answer");
                if (answerStr != null && !answerStr.trim().isEmpty()) {
                    // 解析answer字段中的JSON数组字符串
                    List<String> addresses = objectMapper.readValue(
                        answerStr,
                        new TypeReference<List<String>>() {}
                    );
                    logger.debug("API解析成功，地址: {}，结果数量: {}", addressText, addresses.size());
                    return addresses != null ? addresses : new ArrayList<>();
                } else {
                    logger.warn("接口返回的answer字段为空，地址: {}", addressText);
                    return Arrays.asList(addressText);
                }
            } else {
                logger.error("地址解析接口调用失败，状态码: {}，响应体: {}", response.getStatusCode(), response.getBody());
                return Arrays.asList(addressText);
            }

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            logger.debug("第{}次API调用失败，地址: {}，耗时: {}ms，错误类型: {}",
                        attempt, addressText, endTime - startTime, e.getClass().getSimpleName());
            throw e; // 重新抛出异常，由上层处理重试
        }
    }

    /**
     * 本地解析地址（使用高精度地址解析器）
     */
    private List<String> processAddressByLocal(String addressText) {
        try {
            AddressParser parser = new AddressParser();
            List<String> results = parser.parse(addressText);

            // 如果解析结果为空，返回原始地址
            if (results.isEmpty()) {
                logger.warn("地址解析结果为空，返回原始地址: {}", addressText);
                return Arrays.asList(addressText);
            }

            logger.debug("地址解析成功，原始: {}, 结果数量: {}", addressText, results.size());
            return results;

        } catch (Exception e) {
            logger.error("本地地址解析失败，地址: {}", addressText, e);
            // 解析失败时返回原始地址
            return Arrays.asList(addressText);
        }
    }

    // 旧的解析方法已被高精度地址解析器替换

    /**
     * 去重但保持第一次出现的顺序
     */
    private List<String> removeDuplicatesKeepOrder(List<String> list) {
        Set<String> seen = new LinkedHashSet<>();
        for (String item : list) {
            seen.add(item);
        }
        return new ArrayList<>(seen);
    }



    /**
     * 创建结果Excel文件
     */
    private void createResultExcel(List<Map<String, String>> originalRows,
                                 List<Map<String, String>> processedRows,
                                 Map<Integer, String> headerMap,
                                 String fileId,
                                 String fileName) throws IOException {
        // 创建文件
        File file = new File(StringUtils.concatPath(basePath, "excel", fileId));
        file.getParentFile().mkdirs();

        // 创建最终的数据列表
        List<List<String>> allData = new ArrayList<>();

        // 创建表头（添加No列）
        List<String> headers = new ArrayList<>();
        headers.add("No");

        // 获取原始表头顺序
        List<String> originalHeaders = new ArrayList<>();
        headerMap.entrySet().stream()
            .sorted(Map.Entry.comparingByKey())
            .forEach(entry -> originalHeaders.add(entry.getValue()));
        headers.addAll(originalHeaders);
        allData.add(headers);

        // 按原始行的顺序处理数据
        for (int i = 0; i < originalRows.size(); i++) {
            Map<String, String> originalRow = originalRows.get(i);

            // 添加原始行（No列为空）
            List<String> originalRowData = new ArrayList<>();
            originalRowData.add(""); // No列为空
            for (String header : originalHeaders) {
                originalRowData.add(originalRow.getOrDefault(header, ""));
            }
            allData.add(originalRowData);

            // 添加该行对应的处理后的行
            int counter = 1;
            String currentRowIndex = String.valueOf(i);

            for (Map<String, String> processedRow : processedRows) {
                // 通过行索引匹配
                if (currentRowIndex.equals(processedRow.get("_originalRowIndex"))) {
                    List<String> processedRowData = new ArrayList<>();
                    processedRowData.add(String.valueOf(counter++)); // No列
                    for (String header : originalHeaders) {
                        processedRowData.add(processedRow.getOrDefault(header, ""));
                    }
                    allData.add(processedRowData);
                }
            }
        }

        // 使用EasyExcel写入文件，为原始数据添加背景色
        EasyExcel.write(file.getAbsolutePath())
            .head(createHead(headers))
            .registerWriteHandler(new OriginalRowStyleHandler(allData))
            .sheet("地址处理结果")
            .doWrite(createDataWithoutHeader(allData));
    }

    /**
     * 创建表头
     */
    private List<List<String>> createHead(List<String> headers) {
        List<List<String>> head = new ArrayList<>();
        for (String header : headers) {
            List<String> headColumn = new ArrayList<>();
            headColumn.add(header);
            head.add(headColumn);
        }
        return head;
    }

    /**
     * 创建数据（不包含表头）
     */
    private List<List<String>> createDataWithoutHeader(List<List<String>> allData) {
        if (allData.size() > 1) {
            return allData.subList(1, allData.size());
        }
        return new ArrayList<>();
    }



    /**
     * 原始行样式处理器（优化版，只创建一个样式对象）
     */
    private static class OriginalRowStyleHandler implements CellWriteHandler {
        private final List<List<String>> allData;
        private CellStyle originalRowStyle; // 复用的样式对象

        public OriginalRowStyleHandler(List<List<String>> allData) {
            this.allData = allData;
        }

        @Override
        public void beforeCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder,
                                   Row row, Head head, Integer columnIndex, Integer relativeRowIndex, Boolean isHead) {
            // 不需要实现
        }

        @Override
        public void afterCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder,
                                  Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
            // 不需要实现
        }

        @Override
        public void afterCellDataConverted(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder,
                                         CellData cellData, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
            // 不需要实现
        }

        @Override
        public void afterCellDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder,
                                   List<CellData> cellDataList, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
            // 跳过表头
            if (isHead) {
                return;
            }

            // 获取实际数据行索引
            int dataRowIndex = relativeRowIndex;
            if (dataRowIndex < allData.size() - 1) {
                List<String> rowData = allData.get(dataRowIndex + 1);

                // 如果No列为空，说明是原始行，设置背景色
                if (rowData.size() > 0 && (rowData.get(0) == null || rowData.get(0).trim().isEmpty())) {
                    // 复用样式对象，避免创建过多样式
                    if (originalRowStyle == null) {
                        Workbook workbook = writeSheetHolder.getSheet().getWorkbook();
                        originalRowStyle = workbook.createCellStyle();
                        originalRowStyle.setFillForegroundColor(IndexedColors.LIGHT_YELLOW.getIndex());
                        originalRowStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                    }
                    cell.setCellStyle(originalRowStyle);
                }
            }
        }
    }

    /**
     * 定时清理任务状态
     */
    private void scheduleTaskCleanup(String taskId) {
        // 使用异步执行器延迟清理任务状态（24小时后）
        new Thread(() -> {
            try {
                Thread.sleep(24 * 60 * 60 * 1000); // 24小时
                taskStatusMap.remove(taskId);
                taskProgressMap.remove(taskId);
                logger.info("已清理任务状态，任务ID: {}", taskId);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.warn("任务清理被中断，任务ID: {}", taskId);
            }
        }).start();
    }

    /**
     * 生成文件ID
     */
    private String generateFileId() {
        return UUID.randomUUID().toString().replace("-", "");
    }
}
