package com.data.datatools.component;

import com.alibaba.fastjson.JSON;
import com.data.datatools.component.dto.ai.AiAnswerDto;
import com.data.datatools.component.dto.ai.CreateDocumentOutDto;
import com.data.datatools.component.dto.analytics.AiAnalyticsResult;
import com.data.datatools.repository.master.entity.MAiModelEntity;
import com.data.datatools.repository.master.mapper.MAiModelMapper;
import com.data.datatools.utils.ListUtils;
import com.data.datatools.utils.StringUtils;
import com.data.datatools.web.workbench.display.dto.MatchFieldColumn;
import com.data.datatools.web.workbench.display.entity.RuleText;
import com.data.datatools.web.workbench.model.entity.AiModelColumnMeta;
import com.data.datatools.web.workbench.model.entity.ModelMeta;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

@Component
public class AiAnalyticsComponent {

  @Autowired
  private RestTemplate restTemplate;

  @Value("${data-analytics.ai.url}")
  private String baseUrl;

  @Value("${data-analytics.ai.token-ds}")
  private String aiDsToken;

  @Value("${data-analytics.ai.token-app}")
  private String aiAppToken;

  @Value("${data-analytics.ai.token-app_clean-text}")
  private String aiAppCleanTextToken;

  @Autowired
  private MAiModelMapper mAiModelMapper;

  private static final String NO_RESULT_MESSAGE = "无法识别，请确保输入市政服务相关信息，或对问题进行详细描述。";

  private static final Logger logger = LoggerFactory.getLogger(AiAnalyticsComponent.class);

  @Transactional(propagation = Propagation.REQUIRES_NEW)
  public boolean createDocument(String aiModelId, ModelMeta modelMeta) {
    try {

      Optional<MAiModelEntity> optEntity = mAiModelMapper.selectByPrimaryKey(aiModelId);
      if (!optEntity.isPresent()) {
        logger.error("[model] ai model not found. ModelID:{}", aiModelId);
        return false;
      }
      MAiModelEntity entity = optEntity.get();

      String kId = entity.getkId();
      String docIdsText = entity.getdId();
      // delete document.
      if (StringUtils.isNotEmpty(docIdsText)) {
        for (String docId : docIdsText.split(",")) {
          ResponseEntity<String> response = restTemplate.exchange(
            StringUtils.concatPath(baseUrl, String.format("/datasets/%s/documents/%s", kId, docId)),
            HttpMethod.DELETE,
            new HttpEntity<>(getHeader(0)),
            String.class
          );
          if (!response.getStatusCode().is2xxSuccessful()) {
            logger.warn("[model] delete document fail. kId: {}, dId: {}. response: {}", kId, docId, response);
          }
        }
      }
      // create document.
      List<AiModelColumnMeta> aiLabelList = modelMeta.getAiLabelList();
      String labelTextArray = aiLabelList.stream().map(e -> {
        if (StringUtils.isEmpty(e.getRemarkText())) {
          return String.format("#%s##%s",
            StringUtils.clean(e.getCategory(), "#"), StringUtils.clean(e.getLabelText(), "#"));
        } else {
          return String.format("#%s##%s//%s",
            StringUtils.clean(e.getCategory(), "#"), StringUtils.clean(e.getLabelText(), "#"),
            StringUtils.clean(e.getRemarkText(), "//"));
        }
      }).collect(Collectors.joining("\n\n"));

      List<String> documentIdList = new ArrayList<>();
      String bodyTemplate = "{\"name\": \"%s\",\"text\": \"%s\",\"indexing_technique\": \"high_quality\",\"process_rule\": {\"mode\": \"automatic\"}}";
      {
        HttpEntity<String> httpEntity = new HttpEntity<>(String.format(bodyTemplate, "分类标签", labelTextArray),
          getHeader(0));
        ResponseEntity<String> response = restTemplate.exchange(
          StringUtils.concatPath(baseUrl, String.format("/datasets/%s/document/create-by-text", kId)),
          HttpMethod.POST,
          httpEntity,
          String.class
        );
        if (!response.getStatusCode().is2xxSuccessful()) {
          logger.warn("[model] create document fail 11. kId: {}. response: {}", kId, response);
          return false;
        }
        String body = response.getBody();
        CreateDocumentOutDto outDto = JSON.parseObject(body, CreateDocumentOutDto.class);
        if (outDto == null || outDto.getDocument() == null) {
          logger.warn("[model] create document fail 12. kId: {}. response: {}", kId, response);
          return false;
        }
        String id = outDto.getDocument().getId();
        documentIdList.add(id);
      }
      {
        StringBuilder sb = new StringBuilder();
        for (AiModelColumnMeta aiLabelMeta : aiLabelList) {
          List<RuleText> caseList = aiLabelMeta.getCaseList();
          String category = aiLabelMeta.getCategory();
          String labelText = aiLabelMeta.getLabelText();
          if (ListUtils.isNotEmpty(caseList)) {
            for (RuleText ruleText : caseList) {
              if (StringUtils.isEmpty(ruleText.getCt())) {
                continue;
              }
              if (sb.length() > 0) {
                sb.append("\\n\\n");
              }
              sb.append(String.format("标签：#%s##%s\\n", category, labelText));
              sb.append(String.format("案例：%s", ruleText.getCt().replaceAll("\n", "\\n")));
            }
          }
        }
        if (sb.length() > 0) {
          HttpEntity<String> httpEntity = new HttpEntity<>(String.format(bodyTemplate, "案例说明", sb.toString().trim()), getHeader(0));
          ResponseEntity<String> response = restTemplate.exchange(
            StringUtils.concatPath(baseUrl, String.format("/datasets/%s/document/create-by-text", kId)),
            HttpMethod.POST,
            httpEntity,
            String.class
          );
          if (!response.getStatusCode().is2xxSuccessful()) {
            logger.warn("[model] create document fail 21. kId: {}. response: {}", kId, response);
            return false;
          }
          String body = response.getBody();
          CreateDocumentOutDto outDto = JSON.parseObject(body, CreateDocumentOutDto.class);
          if (outDto == null || outDto.getDocument() == null) {
            logger.warn("[model] create document fail 22. kId: {}. response: {}", kId, response);
            return false;
          }
          String id = outDto.getDocument().getId();
          documentIdList.add(id);
        }
      }

      entity.setdId(String.join(",", documentIdList));
      mAiModelMapper.updateByPrimaryKey(entity);
    } catch (Exception e) {
      logger.error("[model] sync ai model fail.", e);
      return false;
    }
    return true;
  }

  private HttpHeaders getHeader(int app) {
    HttpHeaders headers = new HttpHeaders();
    headers.set("Authorization", "Bearer " + (app == 0 ? aiDsToken : app == 1 ? aiAppToken : aiAppCleanTextToken));
    headers.setContentType(MediaType.APPLICATION_JSON);
    return headers;
  }

  public void analytics(MatchFieldColumn matchColumn, Map<String, MAiModelEntity> aiModelMap,
    Map<String, List<String>> aiModelLabelsMap,
    Map<String, String> targetMap, Map<String, String> resultMap, Map<String, String> aiDebugResultMap) {
    String field = matchColumn.getColumnField();
    String modelId = matchColumn.getModelId();
    MAiModelEntity entity = aiModelMap.get(modelId);
    List<String> aiModelLabels = aiModelLabelsMap.get(modelId);

    StringBuilder sb = new StringBuilder();
    for (String targetColumn : matchColumn.getTargetColumns()) {
      String text = targetMap.get(targetColumn);
      text = text.trim();
      if (sb.length() == 0) {
        sb.append(text);
      } else if (sb.charAt(sb.length() - 1) == '。') {
        sb.append(text);
      } else {
        sb.append("。");
        sb.append(text);
      }
    }

    String bodyTemplate = "{\"query\": \"%s\", \"response_mode\": \"blocking\", \"user\": \"Test\", \"inputs\": {\"PID\":\"%s\"}}";
    HttpEntity<String> httpEntity = new HttpEntity<>(String.format(bodyTemplate, sb, entity.getpId()), getHeader(1));
    try {
      ResponseEntity<String> response = restTemplate.exchange(
        StringUtils.concatPath(baseUrl, "/chat-messages"),
        HttpMethod.POST,
        httpEntity,
        String.class
      );
      if (!response.getStatusCode().is2xxSuccessful()) {
        logger.warn("[analytics] analytics fail 1. pId: {}. response: {}", entity.getpId(), response);
        resultMap.put(field, "匹配异常");
        return;
      }
      String body = response.getBody();
      if (StringUtils.isNotEmpty(body)) {
        AiAnalyticsResult result = JSON.parseObject(body, AiAnalyticsResult.class);

        if (result != null && StringUtils.isNotEmpty(result.getAnswer())) {
          String answer = result.getAnswer();
          if (StringUtils.isNotEmpty(answer)) {
            AiAnswerDto answerDto = JSON.parseObject(answer, AiAnswerDto.class);
            String tag01 = answerDto.getTag01();
            String tag02 = answerDto.getTag02();
            resultMap.put(field, aiModelLabels.contains(tag01) ? tag01 : "其他");
            resultMap.put("exiting", tag02);
            aiDebugResultMap.put(field, tag01);
          } else {
            resultMap.put(field, "其他");
          }
        } else {
          resultMap.put(field, "匹配异常");
          logger.warn("[analytics] analytics fail 3. pId: {}. response: {}", entity.getpId(), response);
        }
      } else {
        logger.warn("[analytics] analytics fail 2. pId: {}. response: {}", entity.getpId(), response);
        resultMap.put(field, "匹配异常");
      }
    } catch (Exception e) {
      logger.error("", e);
      resultMap.put(field, "匹配异常");
    }
  }

  public String analytics(String pId, String message) {

    String bodyTemplate = "{\"query\": \"%s\", \"response_mode\": \"blocking\", \"user\": \"Test\", \"inputs\": {\"PID\":\"%s\"}}";
    HttpEntity<String> httpEntity = new HttpEntity<>(String.format(bodyTemplate, message, pId), getHeader(1));
    try {
      ResponseEntity<String> response = restTemplate.exchange(
        StringUtils.concatPath(baseUrl, "/chat-messages"),
        HttpMethod.POST,
        httpEntity,
        String.class
      );
      if (!response.getStatusCode().is2xxSuccessful()) {
        logger.warn("[chat] analytics message fail 1. pId: {}. response: {}", pId, response);
        return "匹配异常。";
      }
      String body = response.getBody();
      if (StringUtils.isNotEmpty(body) && !"{}".equals(body)) {
        AiAnalyticsResult result = JSON.parseObject(body, AiAnalyticsResult.class);
        if (result != null) {
          String answer = result.getAnswer();
          if (StringUtils.isNotEmpty(answer) && !"{}".equals(answer)) {
            AiAnswerDto answerDto = JSON.parseObject(answer, AiAnswerDto.class);
            String tag01 = answerDto.getTag01();
            String tag02 = answerDto.getTag02();
            if ("未知".equals(tag01)) {
              return NO_RESULT_MESSAGE;
            }
            return String.format("标签：%s。", tag01);
          } else {
            logger.warn("[chat] analytics message fail 3. pId: {}. response: {}", pId, response);
            return NO_RESULT_MESSAGE;
          }
        } else {
          logger.warn("[chat] analytics message fail 4. pId: {}. response: {}", pId, response);
          return NO_RESULT_MESSAGE;
        }
      } else {
        logger.warn("[chat] analytics message fail 2. pId: {}. response: {}", pId, response);
        return NO_RESULT_MESSAGE;
      }
    } catch (Exception e) {
      logger.error("", e);
      return "匹配异常。";
    }
  }

  public String cleanText(String originText) {

    String bodyTemplate = "{\"query\": \"%s\", \"response_mode\": \"blocking\", \"user\": \"Test\", \"inputs\": {}}";
    HttpEntity<String> httpEntity = new HttpEntity<>(String.format(bodyTemplate, originText), getHeader(2));
    try {
      ResponseEntity<String> response = restTemplate.exchange(
        StringUtils.concatPath(baseUrl, "/chat-messages"),
        HttpMethod.POST,
        httpEntity,
        String.class
      );
      if (!response.getStatusCode().is2xxSuccessful()) {
        logger.warn("[cleanText] analytics message fail 1. response: {}", response);
        return null;
      }
      String body = response.getBody();
      if (StringUtils.isNotEmpty(body) && !"{}".equals(body)) {
        AiAnalyticsResult result = JSON.parseObject(body, AiAnalyticsResult.class);
        if (result != null) {
          String answer = result.getAnswer();
          if (StringUtils.isNotEmpty(answer) && !"{}".equals(answer)) {
            return answer;
          } else {
            logger.warn("[cleanText] analytics message fail 3. response: {}", response);
            return null;
          }
        } else {
          logger.warn("[cleanText] analytics message fail 4. response: {}", response);
          return null;
        }
      } else {
        logger.warn("[cleanText] analytics message fail 2. response: {}", response);
        return null;
      }
    } catch (Exception e) {
      logger.error("", e);
      return null;
    }
  }
}
