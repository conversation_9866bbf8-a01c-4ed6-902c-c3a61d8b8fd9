package com.data.datatools.repository.master.mapper;

import static com.data.datatools.repository.master.mapper.MDataEditHistoryNames.TDataEditHistory;
import static com.data.datatools.repository.master.mapper.MDataEditHistoryNames.createdAt;
import static com.data.datatools.repository.master.mapper.MDataEditHistoryNames.createdBy;
import static com.data.datatools.repository.master.mapper.MDataEditHistoryNames.editInfo;
import static com.data.datatools.repository.master.mapper.MDataEditHistoryNames.id;
import static com.data.datatools.repository.master.mapper.MDataEditHistoryNames.recordId;
import static com.data.datatools.repository.master.mapper.MDataEditHistoryNames.tableId;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.data.datatools.common.mybatis.CommonField;
import com.data.datatools.repository.master.entity.MDataEditHistoryEntity;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.insert.render.MultiRowInsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface MDataEditHistoryMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dp_master..t_data_edit_history")
    BasicColumn[] selectList = BasicColumn.columnList(id, tableId, recordId, createdAt, createdBy, editInfo);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dp_master..t_data_edit_history")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dp_master..t_data_edit_history")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dp_master..t_data_edit_history")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    int insert(InsertStatementProvider<MDataEditHistoryEntity> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dp_master..t_data_edit_history")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultiple")
    int insertMultiple(MultiRowInsertStatementProvider<MDataEditHistoryEntity> multipleInsertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dp_master..t_data_edit_history")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("TDataEditHistoryEntityResult")
    Optional<MDataEditHistoryEntity> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dp_master..t_data_edit_history")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="TDataEditHistoryEntityResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.INTEGER, id=true),
        @Result(column="table_id", property="tableId", jdbcType=JdbcType.CHAR),
        @Result(column="record_id", property="recordId", jdbcType=JdbcType.INTEGER),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="created_by", property="createdBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="edit_info", property="editInfo", jdbcType=JdbcType.LONGVARCHAR)
    })
    List<MDataEditHistoryEntity> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dp_master..t_data_edit_history")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dp_master..t_data_edit_history")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, TDataEditHistory, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dp_master..t_data_edit_history")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, TDataEditHistory, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dp_master..t_data_edit_history")
    default int deleteByPrimaryKey(Integer id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dp_master..t_data_edit_history")
    default int insert(MDataEditHistoryEntity record) {
        CommonField.fill(record);
        return MyBatis3Utils.insert(this::insert, record, TDataEditHistory, c ->
            c.map(id).toProperty("id")
            .map(tableId).toProperty("tableId")
            .map(recordId).toProperty("recordId")
            .map(createdAt).toProperty("createdAt")
            .map(createdBy).toProperty("createdBy")
            .map(editInfo).toProperty("editInfo")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dp_master..t_data_edit_history")
    default int insertMultiple(Collection<MDataEditHistoryEntity> records) {
        CommonField.fill(records);
        return MyBatis3Utils.insertMultiple(this::insertMultiple, records, TDataEditHistory, c ->
            c.map(id).toProperty("id")
            .map(tableId).toProperty("tableId")
            .map(recordId).toProperty("recordId")
            .map(createdAt).toProperty("createdAt")
            .map(createdBy).toProperty("createdBy")
            .map(editInfo).toProperty("editInfo")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dp_master..t_data_edit_history")
    default int insertSelective(MDataEditHistoryEntity record) {
        CommonField.fill(record);
        return MyBatis3Utils.insert(this::insert, record, TDataEditHistory, c ->
            c.map(id).toPropertyWhenPresent("id", record::getId)
            .map(tableId).toPropertyWhenPresent("tableId", record::getTableId)
            .map(recordId).toPropertyWhenPresent("recordId", record::getRecordId)
            .map(createdAt).toPropertyWhenPresent("createdAt", record::getCreatedAt)
            .map(createdBy).toPropertyWhenPresent("createdBy", record::getCreatedBy)
            .map(editInfo).toPropertyWhenPresent("editInfo", record::getEditInfo)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dp_master..t_data_edit_history")
    default Optional<MDataEditHistoryEntity> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, TDataEditHistory, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dp_master..t_data_edit_history")
    default List<MDataEditHistoryEntity> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, TDataEditHistory, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dp_master..t_data_edit_history")
    default List<MDataEditHistoryEntity> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, TDataEditHistory, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dp_master..t_data_edit_history")
    default Optional<MDataEditHistoryEntity> selectByPrimaryKey(Integer id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dp_master..t_data_edit_history")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, TDataEditHistory, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dp_master..t_data_edit_history")
    static UpdateDSL<UpdateModel> updateAllColumns(MDataEditHistoryEntity record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalTo(record::getId)
                .set(tableId).equalTo(record::getTableId)
                .set(recordId).equalTo(record::getRecordId)
                .set(editInfo).equalTo(record::getEditInfo);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dp_master..t_data_edit_history")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(MDataEditHistoryEntity record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalToWhenPresent(record::getId)
                .set(tableId).equalToWhenPresent(record::getTableId)
                .set(recordId).equalToWhenPresent(record::getRecordId)
                .set(editInfo).equalToWhenPresent(record::getEditInfo);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dp_master..t_data_edit_history")
    default int updateByPrimaryKey(MDataEditHistoryEntity record) {
        return update(c ->
            c.set(tableId).equalTo(record::getTableId)
            .set(recordId).equalTo(record::getRecordId)
            .set(editInfo).equalTo(record::getEditInfo)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dp_master..t_data_edit_history")
    default int updateByPrimaryKeySelective(MDataEditHistoryEntity record) {
        return update(c ->
            c.set(tableId).equalToWhenPresent(record::getTableId)
            .set(recordId).equalToWhenPresent(record::getRecordId)
            .set(editInfo).equalToWhenPresent(record::getEditInfo)
            .where(id, isEqualTo(record::getId))
        );
    }
}
