package com.data.datatools.common.listener;

import com.data.datatools.common.mybatis.Operator;
import java.util.Map;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class AreaMatchEvent extends ApplicationEvent {

  private final String tableId;
  private final Map<String, String> areaMap;
  private final Operator operator;

  public AreaMatchEvent(String tableId, Map<String, String> areaMap) {
    super("AreaMatchEvent");

    this.tableId = tableId;
    this.areaMap = areaMap;
    this.operator = Operator.copy();
  }
}
