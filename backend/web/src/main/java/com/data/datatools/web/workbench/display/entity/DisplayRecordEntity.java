package com.data.datatools.web.workbench.display.entity;

import java.time.LocalDateTime;
import lombok.Data;

@Data
public class DisplayRecordEntity {

  private String displayId;

  private String displayName;

  private String tableId;

  private String tableName;

  private String projectId;

  private String modelType;

  private String createUser;

  private String createTime;

  private String status;

  private Long analyticsCount;

  private LocalDateTime analyticsTime;

  private String remark;
}
