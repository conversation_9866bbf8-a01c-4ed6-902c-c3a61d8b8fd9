package com.data.datatools.config;

import com.data.datatools.common.exception.BusinessException;
import com.data.datatools.common.logger.EventTraceLogger;
import com.data.datatools.common.logger.OperateLog;
import com.data.datatools.common.message.MessageConstants;
import com.data.datatools.common.response.BaseResponse;
import com.data.datatools.common.response.MessageResponse;
import com.data.datatools.config.security.ResponseSupport;
import java.lang.reflect.Method;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.http.ResponseEntity;

@Configuration
@EnableAspectJAutoProxy
public class ControllerAspectConfig {

  private static final Logger LOGGER = LoggerFactory.getLogger(ControllerAspectConfig.class);

  @Bean
  ComponentAspect componentAspect() {
    return new ComponentAspect();
  }

  @Aspect
  public class ComponentAspect {

    @Around("execution(public * com.data.datatools.web..*Controller.*(..))")
    public Object invokeWeb(ProceedingJoinPoint pjp) {

      Signature signature = pjp.getSignature();
      Class<?> methodReturnType = null;
      if (signature instanceof MethodSignature) {
        MethodSignature methodSignature = (MethodSignature) signature;
        Method method = methodSignature.getMethod();
        methodReturnType = method.getReturnType();
      }
      try {
        return pjp.proceed();
      } catch (BusinessException e) {
        MessageResponse response = MessageResponse.newInstance();
        String message = e.getMessage();
        if (message == null) {
          message = MessageConstants.MESSAGE_E0099;
        }
        LOGGER.error(message, e.getException());
        response.addMessage("E", message);
        return response(methodReturnType, response);
      } catch (Throwable e) {
        MessageResponse response = MessageResponse.newInstance();
        LOGGER.error(MessageConstants.MESSAGE_E0099, e);
        response.addMessage("E", MessageConstants.MESSAGE_E0099);
        return response(methodReturnType, response);
      }
    }

    private Object response(Class<?> clazz, BaseResponse response) {
      if (clazz == ResponseEntity.class) {
        return ResponseSupport.messageResponseEntity(response);
      } else {
        return response;
      }
    }

    @Around("@annotation(com.data.datatools.common.logger.OperateLog)")
    public Object operateLog(ProceedingJoinPoint pjp) throws Throwable {
      Method method = ((MethodSignature) pjp.getSignature()).getMethod();
      OperateLog operateLog = method.getAnnotation(OperateLog.class);
      String type = operateLog.type();
      try {
        EventTraceLogger.initContext();
        Object proceed = pjp.proceed();
        if (proceed instanceof MessageResponse) {
          MessageResponse messageResponse = (MessageResponse) proceed;
          if (messageResponse.hasError()) {
            EventTraceLogger.error(type, formatMessage(operateLog.failure(), operateLog.authentication()));
          } else {
            EventTraceLogger.info(type, formatMessage(operateLog.success(), operateLog.authentication()));
          }
        } else {
          EventTraceLogger.info(type, formatMessage(operateLog.success(), operateLog.authentication()));
        }
        return proceed;
      } catch (Throwable e) {
        EventTraceLogger.error(type, e, formatMessage(operateLog.failure(), operateLog.authentication()));
        throw e;
      } finally {
        EventTraceLogger.clearContext();
      }
    }

    private String formatMessage(String message, boolean authentication) {

      return message;
    }
  }
}
