package com.data.datatools.repository.master.mapper;

import static com.data.datatools.repository.master.mapper.MAreaNames.*;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

import com.data.datatools.common.mybatis.CommonField;
import com.data.datatools.common.mybatis.Operator;
import com.data.datatools.common.mybatis.VersionAdd;
import com.data.datatools.repository.master.entity.MAreaEntity;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.insert.render.MultiRowInsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface MAreaMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_area")
    BasicColumn[] selectList = BasicColumn.columnList(areaId, address, prov, city, district, town, road, roads, subroad, createdAt, createdBy, updatedAt, updatedBy);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_area")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_area")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_area")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    int insert(InsertStatementProvider<MAreaEntity> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_area")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultiple")
    int insertMultiple(MultiRowInsertStatementProvider<MAreaEntity> multipleInsertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_area")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("MAreaEntityResult")
    Optional<MAreaEntity> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_area")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="MAreaEntityResult", value = {
        @Result(column="area_id", property="areaId", jdbcType=JdbcType.VARCHAR, id=true),
        @Result(column="address", property="address", jdbcType=JdbcType.VARCHAR),
        @Result(column="prov", property="prov", jdbcType=JdbcType.VARCHAR),
        @Result(column="city", property="city", jdbcType=JdbcType.VARCHAR),
        @Result(column="district", property="district", jdbcType=JdbcType.VARCHAR),
        @Result(column="town", property="town", jdbcType=JdbcType.VARCHAR),
        @Result(column="road", property="road", jdbcType=JdbcType.VARCHAR),
        @Result(column="roads", property="roads", jdbcType=JdbcType.VARCHAR),
        @Result(column="subroad", property="subroad", jdbcType=JdbcType.VARCHAR),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="created_by", property="createdBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="updated_at", property="updatedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_by", property="updatedBy", jdbcType=JdbcType.VARCHAR)
    })
    List<MAreaEntity> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_area")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_area")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, MArea, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_area")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, MArea, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_area")
    default int deleteByPrimaryKey(String areaId_) {
        return delete(c -> 
            c.where(areaId, isEqualTo(areaId_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_area")
    default int insert(MAreaEntity record) {
        CommonField.fill(record);
        return MyBatis3Utils.insert(this::insert, record, MArea, c ->
            c.map(areaId).toProperty("areaId")
            .map(address).toProperty("address")
            .map(prov).toProperty("prov")
            .map(city).toProperty("city")
            .map(district).toProperty("district")
            .map(town).toProperty("town")
            .map(road).toProperty("road")
            .map(roads).toProperty("roads")
            .map(subroad).toProperty("subroad")
            .map(createdAt).toProperty("createdAt")
            .map(createdBy).toProperty("createdBy")
            .map(updatedAt).toProperty("updatedAt")
            .map(updatedBy).toProperty("updatedBy")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_area")
    default int insertMultiple(Collection<MAreaEntity> records) {
        CommonField.fill(records);
        return MyBatis3Utils.insertMultiple(this::insertMultiple, records, MArea, c ->
            c.map(areaId).toProperty("areaId")
            .map(address).toProperty("address")
            .map(prov).toProperty("prov")
            .map(city).toProperty("city")
            .map(district).toProperty("district")
            .map(town).toProperty("town")
            .map(road).toProperty("road")
            .map(roads).toProperty("roads")
            .map(subroad).toProperty("subroad")
            .map(createdAt).toProperty("createdAt")
            .map(createdBy).toProperty("createdBy")
            .map(updatedAt).toProperty("updatedAt")
            .map(updatedBy).toProperty("updatedBy")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_area")
    default int insertSelective(MAreaEntity record) {
        CommonField.fill(record);
        return MyBatis3Utils.insert(this::insert, record, MArea, c ->
            c.map(areaId).toPropertyWhenPresent("areaId", record::getAreaId)
            .map(address).toPropertyWhenPresent("address", record::getAddress)
            .map(prov).toPropertyWhenPresent("prov", record::getProv)
            .map(city).toPropertyWhenPresent("city", record::getCity)
            .map(district).toPropertyWhenPresent("district", record::getDistrict)
            .map(town).toPropertyWhenPresent("town", record::getTown)
            .map(road).toPropertyWhenPresent("road", record::getRoad)
            .map(roads).toPropertyWhenPresent("roads", record::getRoads)
            .map(subroad).toPropertyWhenPresent("subroad", record::getSubroad)
            .map(createdAt).toPropertyWhenPresent("createdAt", record::getCreatedAt)
            .map(createdBy).toPropertyWhenPresent("createdBy", record::getCreatedBy)
            .map(updatedAt).toPropertyWhenPresent("updatedAt", record::getUpdatedAt)
            .map(updatedBy).toPropertyWhenPresent("updatedBy", record::getUpdatedBy)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_area")
    default Optional<MAreaEntity> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, MArea, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_area")
    default List<MAreaEntity> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, MArea, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_area")
    default List<MAreaEntity> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, MArea, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_area")
    default Optional<MAreaEntity> selectByPrimaryKey(String areaId_) {
        return selectOne(c ->
            c.where(areaId, isEqualTo(areaId_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_area")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, MArea, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_area")
    static UpdateDSL<UpdateModel> updateAllColumns(MAreaEntity record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(areaId).equalTo(record::getAreaId)
                .set(address).equalTo(record::getAddress)
                .set(prov).equalTo(record::getProv)
                .set(city).equalTo(record::getCity)
                .set(district).equalTo(record::getDistrict)
                .set(town).equalTo(record::getTown)
                .set(road).equalTo(record::getRoad)
                .set(roads).equalTo(record::getRoads)
                .set(subroad).equalTo(record::getSubroad)
                .set(updatedAt).equalTo(LocalDateTime.now())
                .set(updatedBy).equalTo(Operator.of());
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_area")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(MAreaEntity record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(areaId).equalToWhenPresent(record::getAreaId)
                .set(address).equalToWhenPresent(record::getAddress)
                .set(prov).equalToWhenPresent(record::getProv)
                .set(city).equalToWhenPresent(record::getCity)
                .set(district).equalToWhenPresent(record::getDistrict)
                .set(town).equalToWhenPresent(record::getTown)
                .set(road).equalToWhenPresent(record::getRoad)
                .set(roads).equalToWhenPresent(record::getRoads)
                .set(subroad).equalToWhenPresent(record::getSubroad)
                .set(updatedAt).equalTo(LocalDateTime.now())
                .set(updatedBy).equalTo(Operator.of());
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_area")
    default int updateByPrimaryKey(MAreaEntity record) {
        return update(c ->
            c.set(address).equalTo(record::getAddress)
            .set(prov).equalTo(record::getProv)
            .set(city).equalTo(record::getCity)
            .set(district).equalTo(record::getDistrict)
            .set(town).equalTo(record::getTown)
            .set(road).equalTo(record::getRoad)
            .set(roads).equalTo(record::getRoads)
            .set(subroad).equalTo(record::getSubroad)
            .set(updatedAt).equalTo(LocalDateTime.now())
            .set(updatedBy).equalTo(Operator.of())
            .where(areaId, isEqualTo(record::getAreaId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_area")
    default int updateByPrimaryKeySelective(MAreaEntity record) {
        return update(c ->
            c.set(address).equalToWhenPresent(record::getAddress)
            .set(prov).equalToWhenPresent(record::getProv)
            .set(city).equalToWhenPresent(record::getCity)
            .set(district).equalToWhenPresent(record::getDistrict)
            .set(town).equalToWhenPresent(record::getTown)
            .set(road).equalToWhenPresent(record::getRoad)
            .set(roads).equalToWhenPresent(record::getRoads)
            .set(subroad).equalToWhenPresent(record::getSubroad)
            .set(updatedAt).equalTo(LocalDateTime.now())
            .set(updatedBy).equalTo(Operator.of())
            .where(areaId, isEqualTo(record::getAreaId))
        );
    }
}