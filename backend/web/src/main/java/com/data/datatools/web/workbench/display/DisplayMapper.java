package com.data.datatools.web.workbench.display;

import com.data.datatools.common.base.dao.IBasePagingMapper;
import com.data.datatools.common.response.ListResponse;
import com.data.datatools.web.workbench.display.dto.DisplayCondition;
import com.data.datatools.web.workbench.display.dto.DisplayRecord;
import com.data.datatools.web.workbench.display.entity.DisplayRecordEntity;
import java.util.List;
import java.util.function.Function;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface DisplayMapper extends IBasePagingMapper {

  default List<DisplayRecord> selectDisplayListWithPage(DisplayCondition condition,
    ListResponse<DisplayRecord> response,
    Function<DisplayRecordEntity, DisplayRecord> convert) {

    return selectWithPage(condition, response, convert, this::selectList);
  }

  List<DisplayRecordEntity> selectList(@Param("param") DisplayCondition condition);
}
