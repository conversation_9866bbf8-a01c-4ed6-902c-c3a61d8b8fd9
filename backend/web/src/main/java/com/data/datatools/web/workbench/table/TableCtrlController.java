package com.data.datatools.web.workbench.table;

import com.data.datatools.common.response.BaseResponse;
import com.data.datatools.web.workbench.table.dto.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class TableCtrlController {

  @Autowired
  private TableCtrlService tableService;

  @PostMapping("/workbench/table-save")
  public BaseResponse saveTable(@RequestBody TableMetaDto dto) {

    return tableService.saveTable(dto);
  }

  @GetMapping("/workbench/table-get/{tableId}")
  public BaseResponse getTable(@PathVariable String tableId) {
    return  tableService.getTable(tableId);
  }

  @PostMapping("/workbench/select-table")
  public BaseResponse searchTableList(@RequestBody TableCtrlCondition condition) {

    return tableService.selectTableList(condition);
  }

  @DeleteMapping("/workbench/delete-table/{tableId}")
  public BaseResponse deleteTable(@PathVariable String tableId) {
    return tableService.deleteTable(tableId);
  }

  @DeleteMapping("/workbench/clear-table-data/{tableId}")
  public BaseResponse clearTableData(@PathVariable String tableId) {
    return tableService.clearTableData(tableId);
  }

  @PostMapping("/workbench/select-table-data/{tableId}")
  public BaseResponse searchTableDataList(@PathVariable String tableId, @RequestBody TableDataCondition condition) {

    return tableService.selectTableDataList(tableId, condition);
  }

  @PostMapping("/workbench/import-table-data/{tableId}")
  public BaseResponse importTableData(@PathVariable String tableId, ImportTableDataDto dto) {

    return tableService.importTableData(tableId, dto);
  }

  @PostMapping("/workbench/match-area/{tableId}")
  public BaseResponse matchArea(@PathVariable String tableId) {
    return tableService.matchArea(tableId);
  }

  // 数据导出
  @PostMapping("/workbench/export-table-data/{tableId}")
  public BaseResponse exportTableData(@PathVariable String tableId, @RequestBody ExportTableDataDto dto) {
    return tableService.exportTableData(tableId, dto);
  }

  @PostMapping("/workbench/edit-table-data/{tableId}")
  public BaseResponse editTableData(@PathVariable String tableId, @RequestBody EditTableDataDto dto) {
    return tableService.editTableData(tableId, dto);
  }

  @PostMapping("/workbench/delete-table-data/{tableId}")
  public BaseResponse deleteTableData(@PathVariable String tableId, @RequestBody TableDataParam param) {
    return tableService.deleteTableData(tableId, param);
  }

  @GetMapping("/workbench/table-columns/{tableId}")
  public BaseResponse getTableColumnMeta(@PathVariable String tableId) {
    return tableService.getTableColumnMeta(tableId);
  }

  // 查重
  @PostMapping("/workbench/data-duplication-check")
  public BaseResponse dataDuplicationCheck(@RequestBody DuplicationCheckCondition condition) {

    return tableService.dataDuplicationCheck(condition);
  }

  // 查重后，删除重复数据
  @PostMapping("/workbench/data-duplication-delete")
  public BaseResponse deleteDuplicationData(@RequestBody DuplicationCheckCondition condition) {
    return tableService.deleteDuplicationData(condition);
  }

  // 查询修改历史
  @PostMapping("/workbench/table-edit-history-data/{tableId}/{recordId}")
  public BaseResponse searchEditHistoryData(@PathVariable("tableId") String tableId, @PathVariable("recordId") Integer recordId) {
    return tableService.searchEditHistoryData(tableId, recordId);
  }

  // 模板下载
  @PostMapping("/workbench/template-download/{template}")
  public ResponseEntity<byte[]> templateDownload(@PathVariable String template) {
    return tableService.templateDownload(template);
  }

  // 导出历史
  @PostMapping("/workbench/export-history-list/{tableId}")
  public BaseResponse getExportHistoryList(@PathVariable String tableId, @RequestBody ExportHistoryCondition condition) {
    return tableService.getExportHistoryList(tableId, condition);
  }

  @PostMapping("/workbench/download-file/{fileId}")
  public ResponseEntity<byte[]> downloadFile(@PathVariable String fileId) {
    return tableService.downloadExportedFile(fileId);
  }

  @PostMapping("/workbench/update-area")
  public BaseResponse updateArea(@RequestBody UpdateAreaDto dto) {
    return tableService.updateArea(dto);
  }
}
