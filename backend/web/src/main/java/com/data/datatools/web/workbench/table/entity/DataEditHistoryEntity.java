package com.data.datatools.web.workbench.table.entity;

import java.time.LocalDateTime;
import lombok.Data;

@Data
public class DataEditHistoryEntity {
  private Integer id;

  /**
   * 表名
   */
  private String tableId;

  /**
   * 数据ID
   */
  private Integer recordId;

  /**
   * 插入时间
   */
  private LocalDateTime createdTime;

  /**
   * 做成者ID
   */
  private String createdUser;

  /**
   * 做成者
   */
  private String createdUserName;

  /**
   * Diff
   */
  private String editInfo;
}
