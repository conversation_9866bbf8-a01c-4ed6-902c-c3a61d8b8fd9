package com.data.datatools.repository.master.entity;

import com.data.datatools.common.base.dao.BaseEntity;
import java.io.Serializable;
import java.time.LocalDateTime;
import javax.annotation.Generated;

/**
 * 数据表Meta信息
 *
 * database table [data_analytics..m_table_meta]
 */
public class MTableMetaEntity implements Serializable, BaseEntity {
    /**
     * 主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.table_id")
    private String tableId;

    /**
     * 表名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.table_name")
    private String tableName;

    /**
     * 所属项目ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.project_id")
    private String projectId;

    /**
     * 地区匹配状态 0:完成 1:进行中 2:失败
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.match_status")
    private String matchStatus;

    /**
     * 描述
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.remark")
    private String remark;

    /**
     * 是否删除
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.deleted")
    private Boolean deleted;

    /**
     * 插入时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.created_at")
    private LocalDateTime createdAt;

    /**
     * 做成者
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.created_by")
    private String createdBy;

    /**
     * 更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.updated_at")
    private LocalDateTime updatedAt;

    /**
     * 更新者
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.updated_by")
    private String updatedBy;

    /**
     * 数据表Meta
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.table_meta")
    private String tableMeta;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_table_meta")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.table_id")
    public String getTableId() {
        return tableId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.table_id")
    public void setTableId(String tableId) {
        this.tableId = tableId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.table_name")
    public String getTableName() {
        return tableName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.table_name")
    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.project_id")
    public String getProjectId() {
        return projectId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.project_id")
    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.match_status")
    public String getMatchStatus() {
        return matchStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.match_status")
    public void setMatchStatus(String matchStatus) {
        this.matchStatus = matchStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.remark")
    public String getRemark() {
        return remark;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.remark")
    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.deleted")
    public Boolean getDeleted() {
        return deleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.deleted")
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.created_at")
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.created_at")
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.created_by")
    public String getCreatedBy() {
        return createdBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.created_by")
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.updated_at")
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.updated_at")
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.updated_by")
    public String getUpdatedBy() {
        return updatedBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.updated_by")
    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.table_meta")
    public String getTableMeta() {
        return tableMeta;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.table_meta")
    public void setTableMeta(String tableMeta) {
        this.tableMeta = tableMeta;
    }
}