package com.data.datatools.utils;

import java.util.List;
import java.util.stream.Collectors;

public final class ListUtils {

  private ListUtils() {}

  public static boolean isEmpty(List<?> list) {
    return list == null || list.isEmpty();
  }

  public static boolean isNotEmpty(List<?> list) {
    return !isEmpty(list);
  }

  public static String join(List<String> keys, String delimiter) {
    return String.join(delimiter, keys);
  }
}
