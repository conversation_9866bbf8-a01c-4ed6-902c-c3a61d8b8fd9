package com.data.datatools.web.workbench.table.entity;

import com.data.datatools.common.base.BaseCondition;
import com.data.datatools.common.model.ItemFilters;
import java.util.List;
import lombok.Data;

@Data
public class TableDataSelectParam extends BaseCondition {

  private String tableId;
  private boolean deleted;
  private List<String> keys;
  private List<ItemFilters> itemFilters;
  private String type = "table";
  private String district;
  private String town;
}
