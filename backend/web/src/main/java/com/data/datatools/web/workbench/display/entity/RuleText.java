package com.data.datatools.web.workbench.display.entity;

import com.data.datatools.utils.StringUtils;
import lombok.Data;

@Data
public class RuleText {

  private String ctId;
  private String ct;

  public RuleText() {
  }

  public RuleText(String ctId, String ct) {
    this.ctId = ctId;
    this.ct = ct;
  }

  @Override
  public boolean equals(Object other) {
    if (other instanceof RuleText) {
      return StringUtils.equals(((RuleText)other).ctId, ctId);
    }
    return false;
  }
}
