package com.data.datatools.component;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.read.metadata.holder.ReadRowHolder;
import com.alibaba.excel.read.metadata.holder.ReadSheetHolder;
import com.alibaba.excel.util.ConverterUtils;
import com.data.datatools.component.dto.excel.DataExcelRecord;
import com.data.datatools.component.dto.excel.ExcelReader;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

@Component
public class ExcelComponent {

  private static final Logger logger = LoggerFactory.getLogger(ExcelComponent.class);

  public void readExcel(MultipartFile file, int pieceCount, ExcelReader<DataExcelRecord> reader) {
    try (InputStream is = file.getInputStream()){
      readExcel(is, file.getOriginalFilename(), pieceCount, reader);
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
  }
  public void readExcel(InputStream inputStream, String originalFilename, int pieceCount, ExcelReader<DataExcelRecord> reader) {
    List<DataExcelRecord> dataList = new ArrayList<>();
    try {
      EasyExcel.read(inputStream, DataExcelRecord.class, new AnalysisEventListener<DataExcelRecord>() {
        @Override
        public void invoke(DataExcelRecord data, AnalysisContext analysisContext) {
          dataList.add(data);
          ReadRowHolder rowHolder = analysisContext.readRowHolder();
          if (rowHolder.getRowIndex() % pieceCount == 0) {
            reader.read(dataList);
            dataList.clear();
          }
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
          reader.read(dataList);
          dataList.clear();
          ReadSheetHolder readSheetHolder = analysisContext.readSheetHolder();
          logger.info("Excel({}) read success. total count: {}", originalFilename, readSheetHolder.getApproximateTotalRowNumber());
        }
      }).sheet().doRead();
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  public ExcelData readExcelWithHeaderCheck(MultipartFile file, int pieceCount,
    Function<Map<Integer, String>, Boolean> headerCheck,
    ExcelReader<ExcelRowData<Map<String, String>>> reader) {

    try (InputStream is = file.getInputStream()){
      return readExcelWithHeaderCheck(is, file.getOriginalFilename(), pieceCount, headerCheck, reader);
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  public ExcelData readExcelWithHeaderCheck(InputStream inputStream, String originalFilename, int pieceCount,
    Function<Map<Integer, String>, Boolean> headerCheck,
    ExcelReader<ExcelRowData<Map<String, String>>> reader) {

    ExcelData excelData = new ExcelData();
    try {

      List<ExcelRowData<Map<String, String>>> dataList = new ArrayList<>();

      EasyExcel.read(inputStream,
        new AnalysisEventListener<Map<Integer, String>>() {

          @Override
          public void invoke(Map<Integer, String> data, AnalysisContext context) {
            ReadRowHolder rowHolder = context.readRowHolder();
            Integer rowIndex = rowHolder.getRowIndex();
            Map<Integer, String> headerMap = excelData.getHeaderMap();


            Map<String, String> dataMap = new HashMap<>();

            data.forEach((k, v) -> dataMap.put(headerMap.get(k), v));

            dataList.add(new ExcelRowData<>(rowIndex, dataMap));
            if (rowIndex % pieceCount == 0) {
              reader.read(dataList);
              dataList.clear();
            }
          }

          @Override
          public void doAfterAllAnalysed(AnalysisContext context) {

            if (!dataList.isEmpty()) {
              reader.read(dataList);
              dataList.clear();
            }
            ReadSheetHolder readSheetHolder = context.readSheetHolder();
            Integer totalRowNumber = readSheetHolder.getApproximateTotalRowNumber();
            logger.info("Excel({}) read success. total count: {}", originalFilename, totalRowNumber);
            if (totalRowNumber == 0) {
              throw new HeaderErrorException();
            }
            if (totalRowNumber == 1) {
              throw new NoDataException();
            }
          }

          @Override
          public void invokeHead(Map<Integer, CellData> headMap, AnalysisContext context) {
            Map<Integer, String> header = ConverterUtils.convertToStringMap(headMap, context);
            Boolean error = headerCheck.apply(header);
            if (error) {
              throw new HeaderErrorException();
            }
            excelData.getHeaderMap().putAll(header);
          }
        }).sheet().headRowNumber(1).doRead();

      return excelData;
    } catch (Exception e) {
      Throwable cause = e.getCause();
      if (e instanceof HeaderErrorException || cause instanceof HeaderErrorException) {
        excelData.setHeaderError(true);
        return excelData;
      }
      if (e instanceof NoDataException || cause instanceof NoDataException) {
        excelData.setNodataError(true);
        return excelData;
      }
      throw new RuntimeException(e);
    }
  }

  static class HeaderErrorException extends RuntimeException {

  }
  static class NoDataException extends RuntimeException {

  }

  @Data
  public static class ExcelData {

    private boolean headerError;
    private boolean nodataError;
    private final Map<Integer, String> headerMap = new HashMap<>();
    private final List<Map<Integer, CellData<Object>>> dataList = new ArrayList<>();

  }

  @Getter
  @AllArgsConstructor
  public static class ExcelRowData<T> {

    private final int rowIndex;
    private final T data;
  }
}
