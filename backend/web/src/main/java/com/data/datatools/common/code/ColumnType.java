package com.data.datatools.common.code;

public enum ColumnType {
  STRING(1, "文本"),
  NUMBER(2, "数字"),
  BOOL(3, "布尔型"),
  DATE(4, "日期");

  private final int type;
  private final String name;
  ColumnType(int type, String name) {
    this.type = type;
    this.name = name;
  }

  public int getType() {
    return type;
  }

  public String getName() {
    return name;
  }

  public static String getName(int type) {
    for (ColumnType value : ColumnType.values()) {
      if (value.type == type) {
        return value.name;
      }
    }
    return "-";
  }

  public boolean is(int type) {
    return this.type == type;
  }
}
