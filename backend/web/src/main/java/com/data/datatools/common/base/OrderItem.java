package com.data.datatools.common.base;


import java.io.Serializable;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class OrderItem implements Serializable {
  private static final long serialVersionUID = 1L;

  private String column;

  private boolean asc = true;

  public static OrderItem asc(String column) {
    return build(column, true);
  }

  public static OrderItem desc(String column) {
    return build(column, false);
  }

  public static List<OrderItem> asc(String... columns) {
    return Arrays.stream(columns).map(OrderItem::asc).collect(Collectors.toList());
  }

  public static List<OrderItem> desc(String... columns) {
    return Arrays.stream(columns).map(OrderItem::desc).collect(Collectors.toList());
  }

  private static OrderItem build(String column, boolean asc) {
    OrderItem item = new OrderItem();
    item.setColumn(column);
    item.setAsc(asc);
    return item;
  }

  @Override
  public String toString() {
    return column + " " + (asc ? "ASC" : "DESC");
  }
}
