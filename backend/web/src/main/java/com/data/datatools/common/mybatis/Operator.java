package com.data.datatools.common.mybatis;

import com.data.datatools.common.security.SecuritySupport;
import java.util.function.Supplier;

/**
 * DBオペレーターをサプライヤークラスです。
 *
 * <AUTHOR>
 */
public final class Operator implements Supplier<String> {

  private static final ThreadLocal<String> USER_ID = new ThreadLocal<>();

  private String currentUser;

  private Operator() {
  }

  private Operator(String currentUser) {
    this.currentUser = currentUser;
  }

  /**
   * DBオペレーターを取得します。
   *
   * @return DBオペレーター
   */
  @Override
  public String get() {
    String userId = Operator.USER_ID.get();
    return userId == null ? SecuritySupport.getLoginUserId() : userId;
  }

  /**
   * Operator実例を作成します。
   *
   * @return Operator実例
   */
  public static Operator of() {
    return new Operator();
  }

  public static Operator copy() {
    String loginUserId = SecuritySupport.getLoginUserId();
    return new Operator(loginUserId != null ? loginUserId : USER_ID.get());
  }

  public static void setCurrentUserId(String userId) {
    USER_ID.set(userId);
  }

  public void reset() {
    USER_ID.set(this.currentUser);
  }
}
