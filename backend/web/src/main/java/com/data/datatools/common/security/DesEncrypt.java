package com.data.datatools.common.security;

import com.data.datatools.common.exception.BusinessException;
import com.data.datatools.common.message.MessageConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import java.security.SecureRandom;
import java.util.Base64;

public final class DesEncrypt {

  private static final Logger logger = LoggerFactory.getLogger(DesEncrypt.class);
  private static final String DES = "DES";
  private static final String SECRETKEY = "LJOjdffjds*9jodfw3792jTTTNldsafasdfnsfeQEJOFdfcxdfewEOJRJJ=%&*25Bl";
  private static final int ENCRYPT = 1;
  private static final int DECRYPT = 2;

  private DesEncrypt() {
  }

  public static String encrypt(String text) {
    if (text == null) {
      return null;
    } else {
      try {
        byte[] input = encrypt(text.getBytes(), SECRETKEY.getBytes());
        return Base64.getEncoder().encodeToString(input);
      } catch (Exception e) {
        logger.error("Cannot encrypt", e);
        return text;
      }
    }
  }

  public static String decrypt(String text) {
    if (text == null) {
      return null;
    } else {
      try {
        byte[] input = Base64.getDecoder().decode(text.getBytes());
        return new String(decrypt(input, SECRETKEY.getBytes()));
      } catch (Exception e) {
        logger.error("Cannot decrypt", e);
        return text;
      }
    }
  }

  private static byte[] encrypt(byte[] string, byte[] key) {
    return parseData(string, key, ENCRYPT);
  }

  private static byte[] decrypt(byte[] string, byte[] key) {
    return parseData(string, key, DECRYPT);
  }

  private static byte[] parseData(byte[] string, byte[] key, int operation) {

    try {
      SecureRandom random = new SecureRandom();
      DESKeySpec spec = new DESKeySpec(key);
      SecretKeyFactory secretKeyFactory = SecretKeyFactory.getInstance(DES);
      SecretKey secretKey = secretKeyFactory.generateSecret(spec);
      Cipher cipher = Cipher.getInstance(DES);
      cipher.init(operation, secretKey, random);
      return cipher.doFinal(string);
    } catch (Exception e) {
      throw new BusinessException(e, MessageConstants.MESSAGE_E0099);
    }
  }
}
