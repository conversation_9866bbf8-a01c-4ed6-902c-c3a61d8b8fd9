package com.data.datatools.web.system.user.entity;

import com.data.datatools.common.base.dao.BaseEntity;
import lombok.Data;

@Data
public class UserRoleInfoEntity implements BaseEntity {

  /**
   * id
   */
  private String userId;

  /**
   * user
   */
  private String userName;

  /**
   * email
   */
  private String userEmail;

  /**
   * role_name
   */
  private String userRoleName;

  /**
   * role_id
   */
  private String userType;

  private String district;

  private String town;

  /**
   * deleted
   */
  private String userState;

}
