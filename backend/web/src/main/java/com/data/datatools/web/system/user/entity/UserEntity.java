package com.data.datatools.web.system.user.entity;

import lombok.Data;

@Data
public class UserEntity {

  /**
   * アカウントID
   */
  private String accountId;

  /**
   * アカウント名前
   */
  private String accountName;

  /**
   * アカウントメール
   */
  private String accountEmail;

  /**
   * アカウントパスワード
   */
  private String password;

  /**
   * ロールid
   */
  private String roleId;

  /**
   * ロールid
   */
  private String roleName;
}
