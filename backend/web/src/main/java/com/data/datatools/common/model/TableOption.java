package com.data.datatools.common.model;

import com.data.datatools.common.code.TableType;
import com.data.datatools.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class TableOption {

  private String tableId;
  private TableType tableType;

  public TableOption(String value) {
    if (StringUtils.isNotEmpty(value)) {
      String[] textArray = value.split(":");
      this.tableId = textArray[0];
      this.tableType = TableType.value(textArray[1]);
    }
  }

  @Override
  public String toString() {
    return String.format("%s:%s", tableId, tableType.getType());
  }
}
