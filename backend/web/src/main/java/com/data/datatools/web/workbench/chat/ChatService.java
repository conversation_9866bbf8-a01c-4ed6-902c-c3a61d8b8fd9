package com.data.datatools.web.workbench.chat;

import com.data.datatools.common.response.BaseResponse;
import com.data.datatools.common.response.DataResponse;
import com.data.datatools.component.AiAnalyticsComponent;
import com.data.datatools.utils.StringUtils;
import com.data.datatools.web.workbench.chat.dto.MessageDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

@Service
public class ChatService {

  @Autowired
  private AiAnalyticsComponent aiAnalyticsComponent;

  public BaseResponse chatMessage(MessageDto dto) {
    DataResponse<String> response = new DataResponse<>();
    String message = dto.getMessage();
    if (StringUtils.isEmpty(message)) {
      return response;
    }

    String result = aiAnalyticsComponent.analytics("00", message.trim().replaceAll("\r\n", "\n").replaceAll("\n", ""));
    return response.setValue(result);
  }
}
