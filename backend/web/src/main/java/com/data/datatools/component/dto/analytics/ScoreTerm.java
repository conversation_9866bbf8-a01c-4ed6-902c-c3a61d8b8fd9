package com.data.datatools.component.dto.analytics;

import com.alibaba.fastjson.annotation.JSONField;
import com.data.datatools.component.dto.analytics.AnalyticsKeys.Text;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
public class ScoreTerm {

  private float score;
  @JSONField(serialize = false, deserialize = false)
  private Text text;
  private List<Item> items = new ArrayList<>();

  public ScoreTerm() {
  }

  public ScoreTerm(Text text, float basic) {
    this.text = text;
    this.score = basic;
  }

  public ScoreTerm addTerm(String text, float score, float originScore, int offset) {
    items.add(new Item(text, originScore, offset));
    this.score += score;

    return this;
  }

  @Data
  @AllArgsConstructor
  public static class Item {
    private String text;
    private float score;
    private int offset;
    public Item() {
    }
  }
}
