package com.data.datatools.common.response;

import java.util.HashMap;
import java.util.Map;
import lombok.Getter;

public class DataResponse<T> extends BaseResponse {
  private T data;

  @Getter
  private final Map<String, Object> dataMap = new HashMap<>();

  public DataResponse() {
    super();
  }

  public DataResponse(String statusCode) {
    super(statusCode);
  }

  public T getData() {
    return data;
  }

  public DataResponse<T> setValue(T data) {
    this.data = data;
    return this;
  }

  public static DataResponse<Map<?, ?>> of(Map<?, ?> data) {
    DataResponse<Map<?, ?>> dataResponse = new DataResponse<>();
    dataResponse.setValue(data);
    return dataResponse;
  }

  public static BaseResponse of(Object model) {
    DataResponse<Object> dataResponse = new DataResponse<>();
    dataResponse.setValue(model);
    return dataResponse;
  }

  public static DataResponse<Map<Object, Object>> of(Object... kvs) {
    DataResponse<Map<Object, Object>> dataResponse = new DataResponse<>();

    Map<Object, Object> data = new HashMap<>();
    for (int i = 0; i < kvs.length; i += 2) {
      data.put(kvs[i], kvs[i + 1]);
    }
    dataResponse.setValue(data);
    return dataResponse;
  }

  public DataResponse<T> putData(String key, Object data) {
    this.dataMap.put(key, data);
    return this;
  }
}
