package com.data.datatools.component.dto.meta;

import com.data.datatools.common.code.ColumnAggregate;
import com.data.datatools.common.code.ColumnType;
import com.data.datatools.utils.StringUtils;
import java.util.List;
import lombok.Data;

@Data
public class PivotColumnMeta {

  private String id; // 字段

  private String name; // 字段名

  private String aliasName; // 别名

  private int index;

  private int type; // 字段类型

  private int secType; // Date -> 季度，月

  private String aggregate; // 集合类型

  private List<PivotColumnMeta> children;

  private List<String> filterValue;

  public static final String _SUM_ = "__sum__";

  public String getColumnId(String t) {
    if (ColumnType.DATE.is(type)) {
      if (secType == 1) {
        return "extract(quarter from " + t + "." + id + ")";
      } else if (secType == 2) {
        return "extract(year_month from " + t + "." + id + ")";
      }
    }
    return t + "." + id;
  }

  public String getColumnName(boolean hasAggregate) {

    if (StringUtils.isNotEmpty(aliasName)) {
      return aliasName;
    }
    String _name = "";
    if (ColumnType.DATE.is(type)) {
      if (secType == 1) {
        _name = "季度(" + name + ")";
      } else if (secType == 2) {
        _name = "月(" + name + ")";
      }
    }
    if (StringUtils.isEmpty(_name)) {
      _name = name;
    }
    if (hasAggregate && StringUtils.isNotEmpty(aggregate)) {
      return ColumnAggregate.label(aggregate) + ":" + _name;
    }
    return _name;
  }
  public String getColumnName() {
    return getColumnName(true);
  }

  public boolean isSumColumn() {
    return _SUM_.equals(getId());
  }
}
