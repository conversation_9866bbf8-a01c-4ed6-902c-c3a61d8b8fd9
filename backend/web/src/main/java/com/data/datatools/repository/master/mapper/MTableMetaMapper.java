package com.data.datatools.repository.master.mapper;

import static com.data.datatools.repository.master.mapper.MTableMetaNames.*;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

import com.data.datatools.common.mybatis.CommonField;
import com.data.datatools.common.mybatis.Operator;
import com.data.datatools.common.mybatis.VersionAdd;
import com.data.datatools.repository.master.entity.MTableMetaEntity;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.insert.render.MultiRowInsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface MTableMetaMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_table_meta")
    BasicColumn[] selectList = BasicColumn.columnList(tableId, tableName, projectId, matchStatus, remark, deleted, createdAt, createdBy, updatedAt, updatedBy, tableMeta);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_table_meta")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_table_meta")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_table_meta")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    int insert(InsertStatementProvider<MTableMetaEntity> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_table_meta")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultiple")
    int insertMultiple(MultiRowInsertStatementProvider<MTableMetaEntity> multipleInsertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_table_meta")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("MTableMetaEntityResult")
    Optional<MTableMetaEntity> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_table_meta")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="MTableMetaEntityResult", value = {
        @Result(column="table_id", property="tableId", jdbcType=JdbcType.CHAR, id=true),
        @Result(column="table_name", property="tableName", jdbcType=JdbcType.VARCHAR),
        @Result(column="project_id", property="projectId", jdbcType=JdbcType.CHAR),
        @Result(column="match_status", property="matchStatus", jdbcType=JdbcType.CHAR),
        @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
        @Result(column="deleted", property="deleted", jdbcType=JdbcType.BIT),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="created_by", property="createdBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="updated_at", property="updatedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_by", property="updatedBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="table_meta", property="tableMeta", jdbcType=JdbcType.LONGVARCHAR)
    })
    List<MTableMetaEntity> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_table_meta")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_table_meta")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, MTableMeta, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_table_meta")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, MTableMeta, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_table_meta")
    default int deleteByPrimaryKey(String tableId_) {
        return delete(c -> 
            c.where(tableId, isEqualTo(tableId_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_table_meta")
    default int insert(MTableMetaEntity record) {
        CommonField.fill(record);
        return MyBatis3Utils.insert(this::insert, record, MTableMeta, c ->
            c.map(tableId).toProperty("tableId")
            .map(tableName).toProperty("tableName")
            .map(projectId).toProperty("projectId")
            .map(matchStatus).toProperty("matchStatus")
            .map(remark).toProperty("remark")
            .map(deleted).toProperty("deleted")
            .map(createdAt).toProperty("createdAt")
            .map(createdBy).toProperty("createdBy")
            .map(updatedAt).toProperty("updatedAt")
            .map(updatedBy).toProperty("updatedBy")
            .map(tableMeta).toProperty("tableMeta")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_table_meta")
    default int insertMultiple(Collection<MTableMetaEntity> records) {
        CommonField.fill(records);
        return MyBatis3Utils.insertMultiple(this::insertMultiple, records, MTableMeta, c ->
            c.map(tableId).toProperty("tableId")
            .map(tableName).toProperty("tableName")
            .map(projectId).toProperty("projectId")
            .map(matchStatus).toProperty("matchStatus")
            .map(remark).toProperty("remark")
            .map(deleted).toProperty("deleted")
            .map(createdAt).toProperty("createdAt")
            .map(createdBy).toProperty("createdBy")
            .map(updatedAt).toProperty("updatedAt")
            .map(updatedBy).toProperty("updatedBy")
            .map(tableMeta).toProperty("tableMeta")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_table_meta")
    default int insertSelective(MTableMetaEntity record) {
        CommonField.fill(record);
        return MyBatis3Utils.insert(this::insert, record, MTableMeta, c ->
            c.map(tableId).toPropertyWhenPresent("tableId", record::getTableId)
            .map(tableName).toPropertyWhenPresent("tableName", record::getTableName)
            .map(projectId).toPropertyWhenPresent("projectId", record::getProjectId)
            .map(matchStatus).toPropertyWhenPresent("matchStatus", record::getMatchStatus)
            .map(remark).toPropertyWhenPresent("remark", record::getRemark)
            .map(deleted).toPropertyWhenPresent("deleted", record::getDeleted)
            .map(createdAt).toPropertyWhenPresent("createdAt", record::getCreatedAt)
            .map(createdBy).toPropertyWhenPresent("createdBy", record::getCreatedBy)
            .map(updatedAt).toPropertyWhenPresent("updatedAt", record::getUpdatedAt)
            .map(updatedBy).toPropertyWhenPresent("updatedBy", record::getUpdatedBy)
            .map(tableMeta).toPropertyWhenPresent("tableMeta", record::getTableMeta)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_table_meta")
    default Optional<MTableMetaEntity> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, MTableMeta, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_table_meta")
    default List<MTableMetaEntity> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, MTableMeta, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_table_meta")
    default List<MTableMetaEntity> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, MTableMeta, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_table_meta")
    default Optional<MTableMetaEntity> selectByPrimaryKey(String tableId_) {
        return selectOne(c ->
            c.where(tableId, isEqualTo(tableId_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_table_meta")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, MTableMeta, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_table_meta")
    static UpdateDSL<UpdateModel> updateAllColumns(MTableMetaEntity record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(tableId).equalTo(record::getTableId)
                .set(tableName).equalTo(record::getTableName)
                .set(projectId).equalTo(record::getProjectId)
                .set(matchStatus).equalTo(record::getMatchStatus)
                .set(remark).equalTo(record::getRemark)
                .set(deleted).equalTo(record::getDeleted)
                .set(updatedAt).equalTo(LocalDateTime.now())
                .set(updatedBy).equalTo(Operator.of())
                .set(tableMeta).equalTo(record::getTableMeta);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_table_meta")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(MTableMetaEntity record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(tableId).equalToWhenPresent(record::getTableId)
                .set(tableName).equalToWhenPresent(record::getTableName)
                .set(projectId).equalToWhenPresent(record::getProjectId)
                .set(matchStatus).equalToWhenPresent(record::getMatchStatus)
                .set(remark).equalToWhenPresent(record::getRemark)
                .set(deleted).equalToWhenPresent(record::getDeleted)
                .set(updatedAt).equalTo(LocalDateTime.now())
                .set(updatedBy).equalTo(Operator.of())
                .set(tableMeta).equalToWhenPresent(record::getTableMeta);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_table_meta")
    default int updateByPrimaryKey(MTableMetaEntity record) {
        return update(c ->
            c.set(tableName).equalTo(record::getTableName)
            .set(projectId).equalTo(record::getProjectId)
            .set(matchStatus).equalTo(record::getMatchStatus)
            .set(remark).equalTo(record::getRemark)
            .set(deleted).equalTo(record::getDeleted)
            .set(updatedAt).equalTo(LocalDateTime.now())
            .set(updatedBy).equalTo(Operator.of())
            .set(tableMeta).equalTo(record::getTableMeta)
            .where(tableId, isEqualTo(record::getTableId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_table_meta")
    default int updateByPrimaryKeySelective(MTableMetaEntity record) {
        return update(c ->
            c.set(tableName).equalToWhenPresent(record::getTableName)
            .set(projectId).equalToWhenPresent(record::getProjectId)
            .set(matchStatus).equalToWhenPresent(record::getMatchStatus)
            .set(remark).equalToWhenPresent(record::getRemark)
            .set(deleted).equalToWhenPresent(record::getDeleted)
            .set(updatedAt).equalTo(LocalDateTime.now())
            .set(updatedBy).equalTo(Operator.of())
            .set(tableMeta).equalToWhenPresent(record::getTableMeta)
            .where(tableId, isEqualTo(record::getTableId))
        );
    }
}