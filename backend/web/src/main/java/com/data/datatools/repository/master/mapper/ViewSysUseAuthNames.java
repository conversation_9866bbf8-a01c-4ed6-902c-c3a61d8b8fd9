package com.data.datatools.repository.master.mapper;

import java.sql.JDBCType;
import java.time.LocalDateTime;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class ViewSysUseAuthNames {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..view_sys_use_auth")
    public static final ViewSysUseAuth viewSysUseAuth = new ViewSysUseAuth();

    /**
     * 用户ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.user_id")
    public static final SqlColumn<String> userId = viewSysUseAuth.userId;

    /**
     * 用户姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.user_name")
    public static final SqlColumn<String> userName = viewSysUseAuth.userName;

    /**
     * 密码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.password")
    public static final SqlColumn<String> password = viewSysUseAuth.password;

    /**
     * 密码状态
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.password_status")
    public static final SqlColumn<String> passwordStatus = viewSysUseAuth.passwordStatus;

    /**
     * 密码变更时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.password_updated_at")
    public static final SqlColumn<LocalDateTime> passwordUpdatedAt = viewSysUseAuth.passwordUpdatedAt;

    /**
     * 当前项目
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.current_project")
    public static final SqlColumn<String> currentProject = viewSysUseAuth.currentProject;

    /**
     * 认证失败回数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.login_wrong_count")
    public static final SqlColumn<Integer> loginWrongCount = viewSysUseAuth.loginWrongCount;

    /**
     * 用户邮箱
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.user_email")
    public static final SqlColumn<String> userEmail = viewSysUseAuth.userEmail;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.user_type")
    public static final SqlColumn<String> userType = viewSysUseAuth.userType;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.city")
    public static final SqlColumn<String> city = viewSysUseAuth.city;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.district")
    public static final SqlColumn<String> district = viewSysUseAuth.district;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.town")
    public static final SqlColumn<String> town = viewSysUseAuth.town;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.solid_user")
    public static final SqlColumn<Integer> solidUser = viewSysUseAuth.solidUser;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..view_sys_use_auth")
    public static final class ViewSysUseAuth extends SqlTable {
        public final SqlColumn<String> userId = column("user_id", JDBCType.VARCHAR);

        public final SqlColumn<String> userName = column("user_name", JDBCType.VARCHAR);

        public final SqlColumn<String> password = column("password", JDBCType.VARCHAR);

        public final SqlColumn<String> passwordStatus = column("password_status", JDBCType.CHAR);

        public final SqlColumn<LocalDateTime> passwordUpdatedAt = column("password_updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<String> currentProject = column("current_project", JDBCType.CHAR);

        public final SqlColumn<Integer> loginWrongCount = column("login_wrong_count", JDBCType.INTEGER);

        public final SqlColumn<String> userEmail = column("user_email", JDBCType.VARCHAR);

        public final SqlColumn<String> userType = column("user_type", JDBCType.VARCHAR);

        public final SqlColumn<String> city = column("city", JDBCType.VARCHAR);

        public final SqlColumn<String> district = column("district", JDBCType.CHAR);

        public final SqlColumn<String> town = column("town", JDBCType.CHAR);

        public final SqlColumn<Integer> solidUser = column("solid_user", JDBCType.INTEGER);

        public ViewSysUseAuth() {
            super("view_sys_use_auth");
        }
    }
}