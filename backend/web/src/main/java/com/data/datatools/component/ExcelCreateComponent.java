package com.data.datatools.component;

import com.data.datatools.component.dto.excel.MergeCells;
import com.data.datatools.utils.StringUtils;
import com.google.common.base.Joiner;

import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.List;
import java.util.Map;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.jxls.area.Area;
import org.jxls.area.CommandData;
import org.jxls.builder.AreaBuilder;
import org.jxls.builder.xls.XlsCommentAreaBuilder;
import org.jxls.command.Command;
import org.jxls.command.GridCommand;
import org.jxls.common.CellRef;
import org.jxls.common.Context;
import org.jxls.transform.Transformer;
import org.jxls.transform.poi.PoiTransformer;
import org.jxls.util.JxlsHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.ResourceUtils;

@Component
public class ExcelCreateComponent {

  @Value("${excel.template.path}")
  private String templatePath;

  private static final Logger logger = LoggerFactory.getLogger(ExcelCreateComponent.class);

  public void exportExcel(String templateName, OutputStream os, Map<String, Object> model)
    throws IOException {
    try (InputStream fileInputStream = getTemplate(templateName)) {
      exportExcel(fileInputStream, os, model);
    }
  }

  public void createExcelDynamicGrid(OutputStream os, String templateName, Map<String, Object> model, List<String> headers,
    List<String> objectProps, List<Object> data, List<MergeCells> mergeCells) {
    exportExcelDynamicGrid(templateName, os, model, headers, objectProps, data, mergeCells);
  }

  private void exportExcelDynamicGrid(String templateName, OutputStream os, Map<String, Object> model,
    List<String> headers, List<String> objectProps, List<Object> data, List<MergeCells> mergeCells) {
    try (InputStream is = getTemplate(templateName)) {
      Context context = new Context();
      context.putVar("headers", headers);
      context.putVar("data", data);
      if (model != null) {
        for (Map.Entry<String, Object> entry : model.entrySet()) {
          context.putVar(entry.getKey(), entry.getValue());
        }
      }
      JxlsHelper jxlsHelper = JxlsHelper.getInstance();
      jxlsHelper.setProcessFormulas(false);
      processGridTemplate(jxlsHelper, is, os, context, Joiner.on(",").join(objectProps), mergeCells);
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  private void processGridTemplate(JxlsHelper jxlsHelper, InputStream is, OutputStream os, Context context,
    String objectProps, List<MergeCells> mergeCells) throws IOException {
    //2.创建workbook 注意这里的workbook实现还是XSSFWorkbook
    Workbook workbook = WorkbookFactory.create(is);
    //3.通过XSSFWorkbook创建一个SXSSFWorkbook
    PoiTransformer transformer = PoiTransformer.createSxssfTransformer(workbook, 50, false);
    //4.重点：这里是通过上面的transformer创建了一个AreaBuilder 然后拿到一个List<Area> 这个就是需要渲染的区域 每一个通过jx:area(lastCell="") 定义的都会是List里面的一个元素
    AreaBuilder areaBuilder = new XlsCommentAreaBuilder(transformer);
//    List<Area> xlsAreaList = areaBuilder.build();
//    Transformer transformer = jxlsHelper.createTransformer(is, os);
//    AreaBuilder areaBuilder = jxlsHelper.getAreaBuilder();
//    areaBuilder.setTransformer(transformer);
    List<Area> xlsAreaList = areaBuilder.build();

    for (Area xlsArea : xlsAreaList) {
      for (CommandData commandData : xlsArea.getCommandDataList()) {
        Command command = commandData.getCommand();
        if (command instanceof GridCommand) {
          GridCommand gridCommand = (GridCommand) command;
          gridCommand.setProps(objectProps);
        }
      }
      xlsArea.applyAt(new CellRef("Result!A1"), context); // 渲染新的sheet
    }
    context.getConfig().setIsFormulaProcessingRequired(false);
    // 7.如果需要将之前的模板sheet给删除掉可以采用如下代码
    workbook.removeSheetAt(0);
    workbook.setForceFormulaRecalculation(true);
    // if (ListUtils.isNotEmpty(mergeCells)) {
    //   mergeCells
    //     .forEach(c -> transformer.mergeCells(new CellRef("Result", c.getX(), c.getY()), c.getRows(), c.getCols()));
    // }
    //8.将Excel导出
    transformer.getWorkbook().write(os);
  }

  public ResponseEntity<byte[]> reportDownload(String pdfFileName, ByteArrayOutputStream os,
    Map<String, String> extraData) {

    try {
      MultiValueMap<String, String> headerMap = new LinkedMultiValueMap<>();
      headerMap.set("Content-Type",
        String.format("application/x-msdownload;filename=%s", URLEncoder.encode(pdfFileName, StandardCharsets.UTF_8.name())));
      headerMap.set("Content-Length", String.valueOf(os.size()));
      if (extraData != null && !extraData.isEmpty()) {
        headerMap.set("Extra-Data", StringUtils.encodeExtraData(extraData));
      }
      HttpHeaders headers = new HttpHeaders(headerMap);
      return new ResponseEntity<>(os.toByteArray(), headers, HttpStatus.OK);
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  private void exportExcel(InputStream is, OutputStream os, Map<String, Object> model)
    throws IOException {
    Context context = new Context();
    if (model != null) {
      for (Map.Entry<String, Object> entry : model.entrySet()) {
        context.putVar(entry.getKey(), entry.getValue());
      }
    }
    JxlsHelper jxlsHelper = JxlsHelper.getInstance();
    Transformer transformer = jxlsHelper.createTransformer(is, os);
    // JexlExpressionEvaluator evaluator = (JexlExpressionEvaluator)
    // transformer.getTransformationConfig().getExpressionEvaluator();
    // Map<String, Object> funcs = new HashMap<>();
    // funcs.put("utils", new JxlsUtils());
    // evaluator.getJexlEngine().setFunctions(funcs);
    jxlsHelper.processTemplate(context, transformer);
  }

  public ResponseEntity<byte[]> excelDownload(String excelFileName, String fileName) {

    ByteArrayOutputStream os = new ByteArrayOutputStream();
    try (InputStream inputStream = getTemplate(excelFileName)) {
      int len;
      byte[] bytes = new byte[1024];
      while ((len = inputStream.read(bytes)) != -1) {
        os.write(bytes, 0, len);
      }
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
    MultiValueMap<String, String> headerMap = new LinkedMultiValueMap<>();
    try {
      headerMap.set("Content-Type",
        String.format("application/x-msdownload;filename=%s", URLEncoder.encode(fileName, StandardCharsets.UTF_8.name())));
    } catch (UnsupportedEncodingException e) {
      throw new RuntimeException(e);
    }
    headerMap.set("Content-Length", String.valueOf(os.size()));
    HttpHeaders headers = new HttpHeaders(headerMap);
    return new ResponseEntity<>(os.toByteArray(), headers, HttpStatus.OK);
  }

  public ResponseEntity<byte[]> downloadFile(File file, String fileName) {

    ByteArrayOutputStream os = new ByteArrayOutputStream();
    try (InputStream inputStream = Files.newInputStream(file.toPath())) {
      int len;
      byte[] bytes = new byte[1024];
      while ((len = inputStream.read(bytes)) != -1) {
        os.write(bytes, 0, len);
      }
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
    MultiValueMap<String, String> headerMap = new LinkedMultiValueMap<>();
    try {
      headerMap.set("Content-Type",
        String.format("application/x-msdownload;filename=%s", URLEncoder.encode(fileName, StandardCharsets.UTF_8.name())));
    } catch (UnsupportedEncodingException e) {
      throw new RuntimeException(e);
    }
    headerMap.set("Content-Length", String.valueOf(os.size()));
    HttpHeaders headers = new HttpHeaders(headerMap);
    return new ResponseEntity<>(os.toByteArray(), headers, HttpStatus.OK);
  }

  private InputStream getTemplate(String name) {
    if (!name.endsWith(".xlsx")) {
      name = name + ".xlsx";
    }
    try {
      if (templatePath.startsWith(ResourceUtils.CLASSPATH_URL_PREFIX)) {
        String path = templatePath.substring(ResourceUtils.CLASSPATH_URL_PREFIX.length());
        ClassPathResource resource = new ClassPathResource(path(path, name));
        return resource.getInputStream();
      } else if (templatePath.startsWith(ResourceUtils.FILE_URL_PREFIX)) {
        String path = templatePath.substring(ResourceUtils.FILE_URL_PREFIX.length());
        File file = new File(path(path, name));
        return new FileInputStream(file);
      } else {
        logger.error("property [excel.template.path] = [{}]】 配置有误， 必须[classpath:]或者[file:]开头", templatePath);
        throw new RuntimeException();
      }
    } catch (IOException e) {
      logger.error("property [excel.template.path] = [{}]】 配置有误", templatePath);
      throw new RuntimeException(e);
    }
  }

  private String path(String path, String name) {
    if (path.endsWith("/")) {
      return path + name;
    } else {
      return path + "/" + name;
    }
  }
}
