package com.data.datatools.repository.master.entity;

import com.data.datatools.common.base.dao.BaseEntity;
import java.io.Serializable;
import java.time.LocalDateTime;
import javax.annotation.Generated;

/**
 * 权限分组表
 *
 * database table [data_analytics..sys_auth_group]
 */
public class SysAuthGroupEntity implements Serializable, BaseEntity {
    /**
     * 主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth_group.id")
    private Integer id;

    /**
     * 权限分组ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth_group.auth_group_id")
    private String authGroupId;

    /**
     * 权限分组名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth_group.auth_group_name")
    private String authGroupName;

    /**
     * 权限分组显示顺序
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth_group.show_sequence")
    private Short showSequence;

    /**
     * 是否删除
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth_group.deleted")
    private Boolean deleted;

    /**
     * 插入时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth_group.created_at")
    private LocalDateTime createdAt;

    /**
     * 做成者
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth_group.created_by")
    private String createdBy;

    /**
     * 更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth_group.updated_at")
    private LocalDateTime updatedAt;

    /**
     * 更新者
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth_group.updated_by")
    private String updatedBy;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_auth_group")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth_group.id")
    public Integer getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth_group.id")
    public void setId(Integer id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth_group.auth_group_id")
    public String getAuthGroupId() {
        return authGroupId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth_group.auth_group_id")
    public void setAuthGroupId(String authGroupId) {
        this.authGroupId = authGroupId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth_group.auth_group_name")
    public String getAuthGroupName() {
        return authGroupName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth_group.auth_group_name")
    public void setAuthGroupName(String authGroupName) {
        this.authGroupName = authGroupName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth_group.show_sequence")
    public Short getShowSequence() {
        return showSequence;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth_group.show_sequence")
    public void setShowSequence(Short showSequence) {
        this.showSequence = showSequence;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth_group.deleted")
    public Boolean getDeleted() {
        return deleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth_group.deleted")
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth_group.created_at")
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth_group.created_at")
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth_group.created_by")
    public String getCreatedBy() {
        return createdBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth_group.created_by")
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth_group.updated_at")
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth_group.updated_at")
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth_group.updated_by")
    public String getUpdatedBy() {
        return updatedBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth_group.updated_by")
    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }
}