package com.data.datatools.web.workbench.chat;

import com.data.datatools.common.response.BaseResponse;
import com.data.datatools.web.workbench.chat.dto.MessageDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class ChatController {

  @Autowired
  private ChatService chatService;

  @PostMapping("/workbench/chat/message")
  public BaseResponse chatMessage(@RequestBody MessageDto dto) {
    return chatService.chatMessage(dto);
  }
}
