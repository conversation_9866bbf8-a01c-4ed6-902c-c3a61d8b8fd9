package com.data.datatools.component.dto.meta;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

@Data
public class TableColumnMeta {

  private String id;

  private String name;

  private int index;

  private int type;

  private boolean key;

  private int length;

  private boolean unique;

  private boolean nullAble;

  private boolean hidden;

  private boolean score;

  private boolean summary;

  private int address;

  private String width; // table column show width.

  @JsonIgnore
  private Object object;

  private List<TableColumnMeta> children;

  public TableColumnMeta() {}

  public TableColumnMeta(String id, String name) {
    this.id = id;
    this.name = name;
  }

  public TableColumnMeta(String id, String name, String width) {
    this.id = id;
    this.name = name;
    this.width = width;
  }

  public boolean hasChildren() {
    return children != null && !children.isEmpty();
  }
}
