package com.data.datatools.repository.master.mapper;

import java.sql.JDBCType;
import java.time.LocalDateTime;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class MDataEditHistoryNames {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dp_master..t_data_edit_history")
    public static final TDataEditHistory TDataEditHistory = new TDataEditHistory();

    /**
     * 主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: dp_master..t_data_edit_history.id")
    public static final SqlColumn<Integer> id = TDataEditHistory.id;

    /**
     * 表名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: dp_master..t_data_edit_history.table_id")
    public static final SqlColumn<String> tableId = TDataEditHistory.tableId;

    /**
     * 数据ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: dp_master..t_data_edit_history.record_id")
    public static final SqlColumn<Integer> recordId = TDataEditHistory.recordId;

    /**
     * 插入时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: dp_master..t_data_edit_history.created_at")
    public static final SqlColumn<LocalDateTime> createdAt = TDataEditHistory.createdAt;

    /**
     * 做成者
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: dp_master..t_data_edit_history.created_by")
    public static final SqlColumn<String> createdBy = TDataEditHistory.createdBy;

    /**
     * Diff
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: dp_master..t_data_edit_history.edit_info")
    public static final SqlColumn<String> editInfo = TDataEditHistory.editInfo;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dp_master..t_data_edit_history")
    public static final class TDataEditHistory extends SqlTable {
        public final SqlColumn<Integer> id = column("id", JDBCType.INTEGER);

        public final SqlColumn<String> tableId = column("table_id", JDBCType.CHAR);

        public final SqlColumn<Integer> recordId = column("record_id", JDBCType.INTEGER);

        public final SqlColumn<LocalDateTime> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createdBy = column("created_by", JDBCType.VARCHAR);

        public final SqlColumn<String> editInfo = column("edit_info", JDBCType.LONGVARCHAR);

        public TDataEditHistory() {
            super("t_data_edit_history");
        }
    }
}
