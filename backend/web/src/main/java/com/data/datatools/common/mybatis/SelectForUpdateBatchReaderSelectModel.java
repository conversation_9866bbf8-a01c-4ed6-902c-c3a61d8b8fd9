package com.data.datatools.common.mybatis;

import java.util.HashMap;
import java.util.Map;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.SelectModel;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;

public class SelectForUpdateBatchReaderSelectModel {

  private final SelectModel selectModel;

  public SelectForUpdateBatchReaderSelectModel(SelectModel selectModel) {
    this.selectModel = selectModel;
  }

  public SelectStatementProvider render() {
    SelectStatementProvider selectStatement = selectModel.render(RenderingStrategies.MYBATIS3);
    return new SelectForUpdateStatement(selectStatement);
  }

  public static class SelectForUpdateStatement implements SelectStatementProvider {

    private final Map<String, Object> parameters = new HashMap<>();
    private final String selectStatement;

    public SelectForUpdateStatement(SelectStatementProvider delegate) {
      parameters.putAll(delegate.getParameters());

      selectStatement = delegate.getSelectStatement() + " FOR UPDATE "; //$NON-NLS-1$
    }

    @Override
    public Map<String, Object> getParameters() {
      return parameters;
    }

    @Override
    public String getSelectStatement() {
      return selectStatement;
    }
  }
}
