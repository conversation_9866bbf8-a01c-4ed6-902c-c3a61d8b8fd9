package com.data.datatools.web.workbench.model;

import com.data.datatools.common.base.dao.IBasePagingMapper;
import com.data.datatools.common.response.ListResponse;
import com.data.datatools.repository.master.entity.MAiModelEntity;
import com.data.datatools.web.workbench.model.dto.ModelListCondition;
import com.data.datatools.web.workbench.model.dto.ModelListRecord;
import com.data.datatools.web.workbench.model.entity.ModelRecordEntity;
import java.util.List;
import java.util.function.Function;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ModelMapper extends IBasePagingMapper {

  default List<ModelListRecord> selectModelListWithPage(ModelListCondition condition,
    ListResponse<ModelListRecord> response, Function<ModelRecordEntity, ModelListRecord> convert) {
    return selectWithPage(condition, response, convert, this::selectModelList);
  }

  List<ModelRecordEntity> selectModelList(@Param("param") ModelListCondition condition);

  List<MAiModelEntity> selectAiModelList(String modelId);
}
