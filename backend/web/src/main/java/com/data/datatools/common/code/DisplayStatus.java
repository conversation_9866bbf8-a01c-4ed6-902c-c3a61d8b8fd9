package com.data.datatools.common.code;

import java.time.LocalDateTime;
import java.util.Objects;

public enum DisplayStatus {

  PROCESS_ING("0", "智能匹配中"),

  COMPLETE("1", "智能匹配完成"),

  FAIL("2", "智能匹配失败");

  private final String status;

  private final String statusName;

  DisplayStatus(String status, String statusName) {
    this.status = status;
    this.statusName = statusName;
  }

  public String status() {
    return this.status;
  }
  public String statusName() {
    return this.statusName;
  }

  public static String status(String status) {
    for (DisplayStatus value : DisplayStatus.values()) {
      if (Objects.equals(value.status, status)) {
        return value.status;
      }
    }
    return "";
  }

  public static String statusName(String status) {
    for (DisplayStatus value : DisplayStatus.values()) {
      if (Objects.equals(value.status, status)) {
        return value.statusName;
      }
    }
    return "-";
  }

  public boolean is(String datasetStatus) {
    return Objects.equals(this.status, datasetStatus);
  }

  public static DisplayStatus getStatus(String s, LocalDateTime time) {
    if (PROCESS_ING.is(s)) {
      if (time.plusHours(1).isBefore(LocalDateTime.now())) {
        return FAIL;
      }
      return PROCESS_ING;
    }
    if (COMPLETE.is(s)) {
      return COMPLETE;
    }
    return FAIL;
  }
}
