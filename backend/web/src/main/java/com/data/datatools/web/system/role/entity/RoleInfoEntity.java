package com.data.datatools.web.system.role.entity;

import com.data.datatools.common.base.dao.BaseEntity;
import lombok.Data;

@Data
public class RoleInfoEntity implements BaseEntity {
  /**
   * 主键
   */
  private Integer id;

  /**
   * 角色ID
   */
  private String roleId;

  /**
   * 角色名称
   */
  private String roleName;

  /**
   * 角色类型：1:个人用；2：组织用
   */
  private String roleType;

  /**
   * 显示顺序
   */
  private Integer showSequence;
}
