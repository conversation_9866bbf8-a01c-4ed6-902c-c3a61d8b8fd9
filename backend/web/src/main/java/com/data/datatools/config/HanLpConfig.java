package com.data.datatools.config;

import com.hankcs.hanlp.utility.Predefine;
import javax.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class HanLpConfig {

  @Value("${data-analytics.hanlp-path}")
  private String hanLpRoot;

  @PostConstruct
  public void initPath() {
    Predefine.HANLP_PROPERTIES_PATH = hanLpRoot;
  }
}
