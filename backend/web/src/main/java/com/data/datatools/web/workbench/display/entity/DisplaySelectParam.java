package com.data.datatools.web.workbench.display.entity;

import com.data.datatools.common.base.BaseCondition;
import com.data.datatools.common.model.ItemFilters;
import java.util.List;
import lombok.Data;

@Data
public class DisplaySelectParam extends BaseCondition {

  private String displayId;

  private List<ItemFilters> itemFilters;

  private String sql;

  private String type = "display";

  private String district;
  private String town;
}
