package com.data.datatools.repository.master.mapper;

import static com.data.datatools.repository.master.mapper.MDisplayMetaNames.*;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

import com.data.datatools.common.mybatis.CommonField;
import com.data.datatools.common.mybatis.Operator;
import com.data.datatools.common.mybatis.VersionAdd;
import com.data.datatools.repository.master.entity.MDisplayMetaEntity;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.insert.render.MultiRowInsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface MDisplayMetaMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_display_meta")
    BasicColumn[] selectList = BasicColumn.columnList(displayId, displayName, tableId, projectId, modelType, showExisting, status, analyticsTime, analyticsCount, remark, deleted, createdAt, createdBy, updatedAt, updatedBy, displayMeta);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_display_meta")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_display_meta")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_display_meta")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    int insert(InsertStatementProvider<MDisplayMetaEntity> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_display_meta")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultiple")
    int insertMultiple(MultiRowInsertStatementProvider<MDisplayMetaEntity> multipleInsertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_display_meta")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("MDisplayMetaEntityResult")
    Optional<MDisplayMetaEntity> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_display_meta")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="MDisplayMetaEntityResult", value = {
        @Result(column="display_id", property="displayId", jdbcType=JdbcType.CHAR, id=true),
        @Result(column="display_name", property="displayName", jdbcType=JdbcType.VARCHAR),
        @Result(column="table_id", property="tableId", jdbcType=JdbcType.CHAR),
        @Result(column="project_id", property="projectId", jdbcType=JdbcType.CHAR),
        @Result(column="model_type", property="modelType", jdbcType=JdbcType.CHAR),
        @Result(column="show_existing", property="showExisting", jdbcType=JdbcType.BIT),
        @Result(column="status", property="status", jdbcType=JdbcType.VARCHAR),
        @Result(column="analytics_time", property="analyticsTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="analytics_count", property="analyticsCount", jdbcType=JdbcType.BIGINT),
        @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
        @Result(column="deleted", property="deleted", jdbcType=JdbcType.BIT),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="created_by", property="createdBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="updated_at", property="updatedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_by", property="updatedBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="display_meta", property="displayMeta", jdbcType=JdbcType.LONGVARCHAR)
    })
    List<MDisplayMetaEntity> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_display_meta")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_display_meta")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, MDisplayMeta, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_display_meta")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, MDisplayMeta, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_display_meta")
    default int deleteByPrimaryKey(String displayId_) {
        return delete(c -> 
            c.where(displayId, isEqualTo(displayId_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_display_meta")
    default int insert(MDisplayMetaEntity record) {
        CommonField.fill(record);
        return MyBatis3Utils.insert(this::insert, record, MDisplayMeta, c ->
            c.map(displayId).toProperty("displayId")
            .map(displayName).toProperty("displayName")
            .map(tableId).toProperty("tableId")
            .map(projectId).toProperty("projectId")
            .map(modelType).toProperty("modelType")
            .map(showExisting).toProperty("showExisting")
            .map(status).toProperty("status")
            .map(analyticsTime).toProperty("analyticsTime")
            .map(analyticsCount).toProperty("analyticsCount")
            .map(remark).toProperty("remark")
            .map(deleted).toProperty("deleted")
            .map(createdAt).toProperty("createdAt")
            .map(createdBy).toProperty("createdBy")
            .map(updatedAt).toProperty("updatedAt")
            .map(updatedBy).toProperty("updatedBy")
            .map(displayMeta).toProperty("displayMeta")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_display_meta")
    default int insertMultiple(Collection<MDisplayMetaEntity> records) {
        CommonField.fill(records);
        return MyBatis3Utils.insertMultiple(this::insertMultiple, records, MDisplayMeta, c ->
            c.map(displayId).toProperty("displayId")
            .map(displayName).toProperty("displayName")
            .map(tableId).toProperty("tableId")
            .map(projectId).toProperty("projectId")
            .map(modelType).toProperty("modelType")
            .map(showExisting).toProperty("showExisting")
            .map(status).toProperty("status")
            .map(analyticsTime).toProperty("analyticsTime")
            .map(analyticsCount).toProperty("analyticsCount")
            .map(remark).toProperty("remark")
            .map(deleted).toProperty("deleted")
            .map(createdAt).toProperty("createdAt")
            .map(createdBy).toProperty("createdBy")
            .map(updatedAt).toProperty("updatedAt")
            .map(updatedBy).toProperty("updatedBy")
            .map(displayMeta).toProperty("displayMeta")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_display_meta")
    default int insertSelective(MDisplayMetaEntity record) {
        CommonField.fill(record);
        return MyBatis3Utils.insert(this::insert, record, MDisplayMeta, c ->
            c.map(displayId).toPropertyWhenPresent("displayId", record::getDisplayId)
            .map(displayName).toPropertyWhenPresent("displayName", record::getDisplayName)
            .map(tableId).toPropertyWhenPresent("tableId", record::getTableId)
            .map(projectId).toPropertyWhenPresent("projectId", record::getProjectId)
            .map(modelType).toPropertyWhenPresent("modelType", record::getModelType)
            .map(showExisting).toPropertyWhenPresent("showExisting", record::getShowExisting)
            .map(status).toPropertyWhenPresent("status", record::getStatus)
            .map(analyticsTime).toPropertyWhenPresent("analyticsTime", record::getAnalyticsTime)
            .map(analyticsCount).toPropertyWhenPresent("analyticsCount", record::getAnalyticsCount)
            .map(remark).toPropertyWhenPresent("remark", record::getRemark)
            .map(deleted).toPropertyWhenPresent("deleted", record::getDeleted)
            .map(createdAt).toPropertyWhenPresent("createdAt", record::getCreatedAt)
            .map(createdBy).toPropertyWhenPresent("createdBy", record::getCreatedBy)
            .map(updatedAt).toPropertyWhenPresent("updatedAt", record::getUpdatedAt)
            .map(updatedBy).toPropertyWhenPresent("updatedBy", record::getUpdatedBy)
            .map(displayMeta).toPropertyWhenPresent("displayMeta", record::getDisplayMeta)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_display_meta")
    default Optional<MDisplayMetaEntity> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, MDisplayMeta, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_display_meta")
    default List<MDisplayMetaEntity> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, MDisplayMeta, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_display_meta")
    default List<MDisplayMetaEntity> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, MDisplayMeta, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_display_meta")
    default Optional<MDisplayMetaEntity> selectByPrimaryKey(String displayId_) {
        return selectOne(c ->
            c.where(displayId, isEqualTo(displayId_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_display_meta")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, MDisplayMeta, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_display_meta")
    static UpdateDSL<UpdateModel> updateAllColumns(MDisplayMetaEntity record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(displayId).equalTo(record::getDisplayId)
                .set(displayName).equalTo(record::getDisplayName)
                .set(tableId).equalTo(record::getTableId)
                .set(projectId).equalTo(record::getProjectId)
                .set(modelType).equalTo(record::getModelType)
                .set(showExisting).equalTo(record::getShowExisting)
                .set(status).equalTo(record::getStatus)
                .set(analyticsTime).equalTo(record::getAnalyticsTime)
                .set(analyticsCount).equalTo(record::getAnalyticsCount)
                .set(remark).equalTo(record::getRemark)
                .set(deleted).equalTo(record::getDeleted)
                .set(updatedAt).equalTo(LocalDateTime.now())
                .set(updatedBy).equalTo(Operator.of())
                .set(displayMeta).equalTo(record::getDisplayMeta);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_display_meta")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(MDisplayMetaEntity record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(displayId).equalToWhenPresent(record::getDisplayId)
                .set(displayName).equalToWhenPresent(record::getDisplayName)
                .set(tableId).equalToWhenPresent(record::getTableId)
                .set(projectId).equalToWhenPresent(record::getProjectId)
                .set(modelType).equalToWhenPresent(record::getModelType)
                .set(showExisting).equalToWhenPresent(record::getShowExisting)
                .set(status).equalToWhenPresent(record::getStatus)
                .set(analyticsTime).equalToWhenPresent(record::getAnalyticsTime)
                .set(analyticsCount).equalToWhenPresent(record::getAnalyticsCount)
                .set(remark).equalToWhenPresent(record::getRemark)
                .set(deleted).equalToWhenPresent(record::getDeleted)
                .set(updatedAt).equalTo(LocalDateTime.now())
                .set(updatedBy).equalTo(Operator.of())
                .set(displayMeta).equalToWhenPresent(record::getDisplayMeta);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_display_meta")
    default int updateByPrimaryKey(MDisplayMetaEntity record) {
        return update(c ->
            c.set(displayName).equalTo(record::getDisplayName)
            .set(tableId).equalTo(record::getTableId)
            .set(projectId).equalTo(record::getProjectId)
            .set(modelType).equalTo(record::getModelType)
            .set(showExisting).equalTo(record::getShowExisting)
            .set(status).equalTo(record::getStatus)
            .set(analyticsTime).equalTo(record::getAnalyticsTime)
            .set(analyticsCount).equalTo(record::getAnalyticsCount)
            .set(remark).equalTo(record::getRemark)
            .set(deleted).equalTo(record::getDeleted)
            .set(updatedAt).equalTo(LocalDateTime.now())
            .set(updatedBy).equalTo(Operator.of())
            .set(displayMeta).equalTo(record::getDisplayMeta)
            .where(displayId, isEqualTo(record::getDisplayId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_display_meta")
    default int updateByPrimaryKeySelective(MDisplayMetaEntity record) {
        return update(c ->
            c.set(displayName).equalToWhenPresent(record::getDisplayName)
            .set(tableId).equalToWhenPresent(record::getTableId)
            .set(projectId).equalToWhenPresent(record::getProjectId)
            .set(modelType).equalToWhenPresent(record::getModelType)
            .set(showExisting).equalToWhenPresent(record::getShowExisting)
            .set(status).equalToWhenPresent(record::getStatus)
            .set(analyticsTime).equalToWhenPresent(record::getAnalyticsTime)
            .set(analyticsCount).equalToWhenPresent(record::getAnalyticsCount)
            .set(remark).equalToWhenPresent(record::getRemark)
            .set(deleted).equalToWhenPresent(record::getDeleted)
            .set(updatedAt).equalTo(LocalDateTime.now())
            .set(updatedBy).equalTo(Operator.of())
            .set(displayMeta).equalToWhenPresent(record::getDisplayMeta)
            .where(displayId, isEqualTo(record::getDisplayId))
        );
    }
}