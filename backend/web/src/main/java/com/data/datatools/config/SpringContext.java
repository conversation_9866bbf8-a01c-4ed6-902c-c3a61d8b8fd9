package com.data.datatools.config;

import lombok.Setter;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class SpringContext implements ApplicationContextAware {

  @Setter
  private static ApplicationContext appContext;

  @Override
  public void setApplicationContext(ApplicationContext applicationContext) {
    setAppContext(applicationContext);
  }

  public static <T> T getBean(Class<T> className) {
    return appContext.getBean(className);
  }

  public static <T> Map<String, T> getBeanMap(Class<T> className) {
    return appContext.getBeansOfType(className);
  }
}
