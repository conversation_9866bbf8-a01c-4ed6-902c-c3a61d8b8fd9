package com.data.datatools.web.workbench.model.entity;

import com.data.datatools.web.workbench.display.entity.RuleText;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

@Data
public class AiModelColumnMeta {

  private Integer index;
  // 上级分类
  private String category;
  // 标签名称
  private String labelText;
  // 案例
  public List<RuleText> caseList = new ArrayList<>();

  private String remarkText;
}
