package com.data.datatools.web.workbench.address;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.data.datatools.web.workbench.address.dto.AddressProcessDto;
import org.apache.poi.ss.usermodel.*;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.Head;
import com.data.datatools.common.response.BaseResponse;
import com.data.datatools.common.response.DataResponse;
import com.data.datatools.common.response.MessageResponse;
import com.data.datatools.component.ExcelComponent;
import com.data.datatools.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class AddressProcessServiceNew {
    
    private static final Logger logger = LoggerFactory.getLogger(AddressProcessServiceNew.class);

    @Autowired
    private ExcelComponent excelComponent;

    @Value("${export.excel.base.path}")
    private String basePath;
    
    // 任务状态管理
    private final Map<String, TaskStatus> taskStatusMap = new ConcurrentHashMap<>();
    
    /**
     * 任务状态枚举
     */
    public enum TaskStatus {
        PROCESSING("处理中"),
        COMPLETED("已完成"),
        FAILED("处理失败");
        
        private final String description;
        
        TaskStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }

    /**
     * 启动地址处理任务（同步方法，立即返回任务ID）
     */
    public BaseResponse processAddressExcel(AddressProcessDto dto) {
        try {
            MultipartFile file = dto.getFile();
            if (file == null || file.isEmpty()) {
                return MessageResponse.newErrorMessage("请选择要处理的Excel文件");
            }

            // 生成任务ID
            String taskId = generateFileId();
            
            // 设置任务状态为处理中
            taskStatusMap.put(taskId, TaskStatus.PROCESSING);
            
            // 异步处理
            processAddressExcelAsync(file, taskId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("taskId", taskId);
            result.put("status", TaskStatus.PROCESSING.getDescription());
            
            return DataResponse.of(result);
            
        } catch (Exception e) {
            logger.error("启动地址处理任务时发生错误", e);
            return MessageResponse.newErrorMessage("启动处理失败：" + e.getMessage());
        }
    }
    
    /**
     * 异步处理Excel中的地址数据
     */
    @Async
    public void processAddressExcelAsync(MultipartFile file, String taskId) {
        try {
            // 存储原始数据和处理后的结果
            List<Map<String, String>> originalRows = new ArrayList<>();
            List<Map<String, String>> processedRows = new ArrayList<>();
            Map<Integer, String> headerMap = new HashMap<>();

            // 读取Excel文件
            ExcelComponent.ExcelData excelData = excelComponent.readExcelWithHeaderCheck(file, 100,
                headerMapCheck -> {
                    // 检查是否包含"地址"列
                    headerMap.putAll(headerMapCheck);
                    return !headerMapCheck.containsValue("地址");
                },
                rowList -> {
                    for (ExcelComponent.ExcelRowData<Map<String, String>> row : rowList) {
                        Map<String, String> dataMap = row.getData();
                        
                        // 保存原始行数据（保持原始顺序）
                        Map<String, String> originalRowData = new HashMap<>(dataMap);
                        originalRowData.put("_rowIndex", String.valueOf(originalRows.size())); // 添加行索引
                        originalRows.add(originalRowData);

                        // 处理地址列
                        String addressValue = dataMap.get("地址");
                        if (addressValue != null && !addressValue.trim().isEmpty()) {
                            List<String> addresses = processAddress(addressValue.trim());

                            // 为每个处理后的地址创建新行（保持地址在原文中的顺序）
                            for (String processedAddress : addresses) {
                                Map<String, String> processedRow = new HashMap<>(dataMap);
                                processedRow.put("地址", processedAddress);
                                processedRow.put("_originalRowIndex", String.valueOf(originalRows.size() - 1)); // 关联原始行
                                processedRows.add(processedRow);
                            }
                        }
                    }
                });
            
            // 检查Excel读取是否有错误
            if (excelData.isHeaderError()) {
                taskStatusMap.put(taskId, TaskStatus.FAILED);
                logger.error("Excel文件格式错误：未找到地址列，任务ID: {}", taskId);
                return;
            }
            
            if (excelData.isNodataError()) {
                taskStatusMap.put(taskId, TaskStatus.FAILED);
                logger.error("Excel文件中没有数据，任务ID: {}", taskId);
                return;
            }

            // 生成结果Excel文件
            String fileName = "processed_addresses_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".xlsx";
            
            createResultExcel(originalRows, processedRows, headerMap, taskId, fileName);

            // 设置任务状态为完成
            taskStatusMap.put(taskId, TaskStatus.COMPLETED);
            
            logger.info("地址处理任务完成，任务ID: {}, 原始行数: {}, 处理后行数: {}", 
                       taskId, originalRows.size(), processedRows.size());

        } catch (Exception e) {
            logger.error("处理地址Excel文件时发生错误，任务ID: " + taskId, e);
            taskStatusMap.put(taskId, TaskStatus.FAILED);
        }
    }
    
    /**
     * 查询任务状态
     */
    public BaseResponse getTaskStatus(String taskId) {
        TaskStatus status = taskStatusMap.get(taskId);
        if (status == null) {
            return MessageResponse.newErrorMessage("任务不存在");
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("taskId", taskId);
        result.put("status", status.getDescription());
        result.put("canDownload", status == TaskStatus.COMPLETED);
        
        return DataResponse.of(result);
    }

    /**
     * 处理单个地址字符串，解析到具体门牌号
     */
    private List<String> processAddress(String addressText) {
        List<String> results = new ArrayList<>();

        // 按逗号、分号、中文顿号分割主要部分
        String[] mainParts = addressText.split("[,，;；、]");

        for (String mainPart : mainParts) {
            mainPart = mainPart.trim();
            if (mainPart.isEmpty()) continue;

            // 处理每个主要部分
            List<String> partResults = parseMainPart(mainPart);
            results.addAll(partResults);
        }

        // 去重但保持第一次出现的顺序
        return removeDuplicatesKeepOrder(results);
    }

    /**
     * 解析主要地址部分
     */
    private List<String> parseMainPart(String mainPart) {
        List<String> results = new ArrayList<>();

        // 提取路名
        String roadName = extractRoadName(mainPart);
        if (roadName.isEmpty()) {
            return results;
        }

        // 移除路名，获取剩余部分
        String remaining = mainPart.replaceFirst("^" + Pattern.quote(roadName), "").trim();

        // 处理剩余部分
        results.addAll(parseRemainingPart(roadName, remaining));

        return results;
    }

    /**
     * 解析剩余部分（路名后面的内容）
     */
    private List<String> parseRemainingPart(String roadName, String remaining) {
        List<String> results = new ArrayList<>();

        // 1. 处理点分隔的复杂格式：2591.2599.2603.2605弄
        if (remaining.contains(".") && remaining.contains("弄")) {
            return parseDotSeparatedWithLong(roadName, remaining);
        }

        // 2. 处理弄+号的格式：499弄1-3.14.15.1甲号.8甲号
        Pattern longWithDetailsPattern = Pattern.compile("(\\d+)弄(.+)");
        Matcher longWithDetailsMatcher = longWithDetailsPattern.matcher(remaining);
        if (longWithDetailsMatcher.find()) {
            String longNum = longWithDetailsMatcher.group(1);
            String details = longWithDetailsMatcher.group(2);
            return parseLongWithDetails(roadName, longNum, details);
        }

        // 3. 处理单独的弄号：475
        Pattern singleNumberPattern = Pattern.compile("^(\\d+)$");
        Matcher singleNumberMatcher = singleNumberPattern.matcher(remaining);
        if (singleNumberMatcher.find()) {
            String number = singleNumberMatcher.group(1);
            results.add(roadName + number + "弄");
            return results;
        }

        // 4. 处理号码格式：7、9、17、19、21号
        if (remaining.contains("号")) {
            return parseNumberFormats(roadName, remaining);
        }

        // 5. 处理连续范围：136-140号
        Pattern rangePattern = Pattern.compile("(\\d+)-(\\d+)号?");
        Matcher rangeMatcher = rangePattern.matcher(remaining);
        if (rangeMatcher.find()) {
            int start = Integer.parseInt(rangeMatcher.group(1));
            int end = Integer.parseInt(rangeMatcher.group(2));

            // 检查是否指定了单双数
            boolean isEven = remaining.contains("双");
            boolean isOdd = remaining.contains("单");

            if (end - start <= 50) { // 限制范围
                if (isEven) {
                    // 只生成双数
                    for (int i = start; i <= end; i++) {
                        if (i % 2 == 0) {
                            results.add(roadName + i + "号");
                        }
                    }
                } else if (isOdd) {
                    // 只生成单数
                    for (int i = start; i <= end; i++) {
                        if (i % 2 == 1) {
                            results.add(roadName + i + "号");
                        }
                    }
                } else {
                    // 生成所有数字
                    for (int i = start; i <= end; i++) {
                        results.add(roadName + i + "号");
                    }
                }
            }
            return results;
        }

        return results;
    }

    /**
     * 解析点分隔的弄号格式：2591.2599.2603.2605弄
     */
    private List<String> parseDotSeparatedWithLong(String roadName, String remaining) {
        List<String> results = new ArrayList<>();

        // 提取点分隔的数字
        Pattern dotPattern = Pattern.compile("([\\d\\.]+)弄");
        Matcher dotMatcher = dotPattern.matcher(remaining);
        if (dotMatcher.find()) {
            String dotNumbers = dotMatcher.group(1);
            String[] numbers = dotNumbers.split("\\.");

            // 第一个数字是号，其余是弄
            for (int i = 0; i < numbers.length; i++) {
                if (numbers[i].matches("\\d+")) {
                    if (i == 0) {
                        results.add(roadName + numbers[i] + "号");
                    } else {
                        results.add(roadName + numbers[i] + "弄");
                    }
                }
            }
        }

        return results;
    }

    /**
     * 解析弄+详细信息格式：499弄1-3.14.15.1甲号.8甲号
     */
    private List<String> parseLongWithDetails(String roadName, String longNum, String details) {
        List<String> results = new ArrayList<>();
        String longPrefix = roadName + longNum + "弄";

        // 按点分隔处理详细信息
        String[] detailParts = details.split("\\.");

        for (String part : detailParts) {
            part = part.trim();
            if (part.isEmpty()) continue;

            // 处理范围：1-3
            Pattern rangePattern = Pattern.compile("(\\d+)-(\\d+)");
            Matcher rangeMatcher = rangePattern.matcher(part);
            if (rangeMatcher.find()) {
                int start = Integer.parseInt(rangeMatcher.group(1));
                int end = Integer.parseInt(rangeMatcher.group(2));

                if (end - start <= 20) { // 限制范围
                    for (int i = start; i <= end; i++) {
                        results.add(longPrefix + i + "号");
                    }
                }
                continue;
            }

            // 处理甲号：1甲号、8甲号
            Pattern jiaPattern = Pattern.compile("(\\d+)甲号");
            Matcher jiaMatcher = jiaPattern.matcher(part);
            if (jiaMatcher.find()) {
                String number = jiaMatcher.group(1);
                results.add(longPrefix + number + "甲号");
                continue;
            }

            // 处理单个数字：14、15
            Pattern singlePattern = Pattern.compile("^(\\d+)$");
            Matcher singleMatcher = singlePattern.matcher(part);
            if (singleMatcher.find()) {
                String number = singleMatcher.group(1);
                results.add(longPrefix + number + "号");
                continue;
            }
        }

        return results;
    }

    /**
     * 解析号码格式：7、9、17、19、21号、44弄4.5号
     */
    private List<String> parseNumberFormats(String roadName, String remaining) {
        List<String> results = new ArrayList<>();

        // 处理弄+号的格式：44弄4.5号
        Pattern longHaoPattern = Pattern.compile("(\\d+)弄([\\d\\.]+)号");
        Matcher longHaoMatcher = longHaoPattern.matcher(remaining);
        if (longHaoMatcher.find()) {
            String longNum = longHaoMatcher.group(1);
            String haoNumbers = longHaoMatcher.group(2);
            String longPrefix = roadName + longNum + "弄";

            if (haoNumbers.contains(".")) {
                String[] numbers = haoNumbers.split("\\.");
                for (String num : numbers) {
                    if (num.matches("\\d+")) {
                        results.add(longPrefix + num + "号");
                    }
                }
            } else {
                results.add(longPrefix + haoNumbers + "号");
            }
            return results;
        }

        // 处理直接的号码：7、9、17、19、21号
        // 移除"号"字，按顿号分割
        String numbersOnly = remaining.replace("号", "");
        String[] numbers = numbersOnly.split("[、，,]");

        for (String num : numbers) {
            num = num.trim();
            if (num.matches("\\d+")) {
                results.add(roadName + num + "号");
            }
        }

        return results;
    }

    /**
     * 提取路名
     */
    private String extractRoadName(String addressPart) {
        Pattern roadPattern = Pattern.compile("([\\u4e00-\\u9fa5]+[路街道巷弄里])");
        Matcher roadMatcher = roadPattern.matcher(addressPart);
        if (roadMatcher.find()) {
            return roadMatcher.group(1);
        }
        return "";
    }

    /**
     * 去重但保持第一次出现的顺序
     */
    private List<String> removeDuplicatesKeepOrder(List<String> list) {
        Set<String> seen = new LinkedHashSet<>();
        for (String item : list) {
            seen.add(item);
        }
        return new ArrayList<>(seen);
    }

    /**
     * 创建结果Excel文件
     */
    private void createResultExcel(List<Map<String, String>> originalRows,
                                 List<Map<String, String>> processedRows,
                                 Map<Integer, String> headerMap,
                                 String fileId,
                                 String fileName) throws IOException {
        // 创建文件
        File file = new File(StringUtils.concatPath(basePath, "excel", fileId));
        file.getParentFile().mkdirs();

        // 创建最终的数据列表
        List<List<String>> allData = new ArrayList<>();

        // 创建表头（添加No列）
        List<String> headers = new ArrayList<>();
        headers.add("No");

        // 获取原始表头顺序
        List<String> originalHeaders = new ArrayList<>();
        headerMap.entrySet().stream()
            .sorted(Map.Entry.comparingByKey())
            .forEach(entry -> originalHeaders.add(entry.getValue()));
        headers.addAll(originalHeaders);
        allData.add(headers);

        // 按原始行的顺序处理数据
        for (int i = 0; i < originalRows.size(); i++) {
            Map<String, String> originalRow = originalRows.get(i);

            // 添加原始行（No列为空）
            List<String> originalRowData = new ArrayList<>();
            originalRowData.add(""); // No列为空
            for (String header : originalHeaders) {
                originalRowData.add(originalRow.getOrDefault(header, ""));
            }
            allData.add(originalRowData);

            // 添加该行对应的处理后的行
            int counter = 1;
            String currentRowIndex = String.valueOf(i);

            for (Map<String, String> processedRow : processedRows) {
                // 通过行索引匹配
                if (currentRowIndex.equals(processedRow.get("_originalRowIndex"))) {
                    List<String> processedRowData = new ArrayList<>();
                    processedRowData.add(String.valueOf(counter++)); // No列
                    for (String header : originalHeaders) {
                        processedRowData.add(processedRow.getOrDefault(header, ""));
                    }
                    allData.add(processedRowData);
                }
            }
        }

        // 使用EasyExcel写入文件，先不设置样式
        EasyExcel.write(file.getAbsolutePath())
            .head(createHead(headers))
            .registerWriteHandler(new CustomCellStyleHandler(allData))
            .sheet("地址处理结果")
            .doWrite(createDataWithoutHeader(allData));
    }

    /**
     * 创建表头
     */
    private List<List<String>> createHead(List<String> headers) {
        List<List<String>> head = new ArrayList<>();
        for (String header : headers) {
            List<String> headColumn = new ArrayList<>();
            headColumn.add(header);
            head.add(headColumn);
        }
        return head;
    }

    /**
     * 创建数据（不包含表头）
     */
    private List<List<String>> createDataWithoutHeader(List<List<String>> allData) {
        if (allData.size() > 1) {
            return allData.subList(1, allData.size());
        }
        return new ArrayList<>();
    }

    /**
     * 自定义单元格样式处理器
     */
    private static class CustomCellStyleHandler implements CellWriteHandler {
        private final List<List<String>> allData;

        public CustomCellStyleHandler(List<List<String>> allData) {
            this.allData = allData;
        }

        @Override
        public void beforeCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder,
                                   Row row, Head head, Integer columnIndex, Integer relativeRowIndex, Boolean isHead) {
            // 不需要实现
        }

        @Override
        public void afterCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder,
                                  Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
            // 不需要实现
        }

        @Override
        public void afterCellDataConverted(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder,
                                         CellData cellData, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
            // 不需要实现
        }

        @Override
        public void afterCellDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder,
                                   List<CellData> cellDataList, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
            // 跳过表头
            if (isHead) {
                return;
            }

            // 获取实际数据行索引（加1是因为跳过了表头）
            int dataRowIndex = relativeRowIndex;
            if (dataRowIndex < allData.size() - 1) { // -1是因为allData包含了表头
                List<String> rowData = allData.get(dataRowIndex + 1); // +1是因为allData的第0行是表头

                // 如果No列为空，说明是原始行，设置背景色
                if (rowData.size() > 0 && (rowData.get(0) == null || rowData.get(0).trim().isEmpty())) {
                    Workbook workbook = writeSheetHolder.getSheet().getWorkbook();
                    CellStyle cellStyle = workbook.createCellStyle();
                    cellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
                    cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                    cell.setCellStyle(cellStyle);
                }
            }
        }
    }

    /**
     * 生成文件ID
     */
    private String generateFileId() {
        return UUID.randomUUID().toString().replace("-", "");
    }
}
