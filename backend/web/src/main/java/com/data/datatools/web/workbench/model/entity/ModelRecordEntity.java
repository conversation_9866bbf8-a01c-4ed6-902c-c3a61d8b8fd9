package com.data.datatools.web.workbench.model.entity;

import java.time.LocalDateTime;
import lombok.Data;

@Data
public class ModelRecordEntity {


  private String modelId;

  /**
   * 模型名称
   */
  private String modelName;

  /**
   * 模型类型
   */
  private String modelType;

  /**
   * 所属项目ID
   */
  private String projectId;

  private String userName;

  private String status;

  private String aiModelName;

  /**
   * 描述
   */
  private String remark;

  /**
   * 版本
   */
  private Integer version;

  private LocalDateTime createdAt;
}
