package com.data.datatools.web.auth;

import com.data.datatools.common.code.UserType;
import com.data.datatools.common.message.MessageConstants;
import com.data.datatools.common.model.SelectOption;
import com.data.datatools.config.security.model.LoginUser;
import com.data.datatools.repository.master.entity.SysAuthEntity;
import com.data.datatools.repository.master.entity.SysAuthGroupEntity;
import com.data.datatools.repository.master.entity.SysRoleAuthEntity;
import com.data.datatools.repository.master.entity.MProjectEntity;
import com.data.datatools.repository.master.entity.ViewSysUseAuthEntity;
import com.data.datatools.repository.master.mapper.MProjectMapper;
import com.data.datatools.repository.master.mapper.SysAuthGroupMapper;
import com.data.datatools.repository.master.mapper.SysAuthMapper;
import com.data.datatools.repository.master.mapper.SysRoleAuthMapper;
import com.data.datatools.repository.master.mapper.SysRoleAuthNames;
import com.data.datatools.repository.master.mapper.MProjectNames;
import com.data.datatools.repository.master.mapper.ViewSysUseAuthMapper;
import com.data.datatools.repository.master.mapper.ViewSysUseAuthNames;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.mybatis.dynamic.sql.SqlBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

@Service
public class LoginAuthService implements UserDetailsService {

  @Autowired
  private ViewSysUseAuthMapper sysUserMapper;

  @Autowired
  private MProjectMapper tProjectMapper;

  @Autowired
  private SysAuthMapper sysAuthMapper;

  @Autowired
  private SysAuthGroupMapper sysAuthGroupMapper;

  @Autowired
  private SysRoleAuthMapper sysRoleAuthMapper;

  @Override
  public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
    if (username == null || "".equals(username.trim())) {
      throw new UsernameNotFoundException(MessageConstants.MESSAGE_E0003);
    }

    Optional<ViewSysUseAuthEntity> optUserEntity = sysUserMapper.selectOne(c -> {
      c.where().and(ViewSysUseAuthNames.userId, SqlBuilder.isEqualTo(username));
      return c;
    });

    if (!optUserEntity.isPresent()) {
      throw new UsernameNotFoundException(MessageConstants.MESSAGE_E0004);
    }
    ViewSysUseAuthEntity entity = optUserEntity.get();

    LoginUser.UserParam param = new LoginUser.UserParam();
    param.setUserId(String.valueOf(entity.getUserId()));
    param.setUserName(entity.getUserName());
    param.setUserPwd(entity.getPassword());
    param.setCurrentProject(entity.getCurrentProject());
    param.setAccountLocked(false);
    param.setAccountEnabled(true);
    param.setSolidUser(entity.getSolidUser() == 1);
    param.setUserType(entity.getUserType());
    param.setCity(entity.getCity());
    param.setDistrict(entity.getDistrict());
    param.setStreet(entity.getTown());

//    List<SysAuthEntity> authEntityList = sysAuthMapper.select(c -> c);
//    List<Authority> authList = authEntityList.stream().map(SysAuthEntity::getAuthId).map(Authority::createAuthority)
//      .collect(Collectors.toList());
//    List<SysAuthGroupEntity> authGroupEntityList = sysAuthGroupMapper.select(c -> c);
//    List<Authority> authGroupList = authGroupEntityList.stream().map(SysAuthGroupEntity::getAuthGroupId)
//      .map(Authority::createAuthority).collect(Collectors.toList());
//
//    authList.addAll(authGroupList);

    String authority = UserType.getAuthority(entity.getUserType());
    param.setAuthorities(Collections.singleton(new Authority(authority)));

//    if (param.isSolidUser()) {
//
//    } else {
//      String[] roleIds = entity.getRoleName().split(",");
//      List<SysRoleAuthEntity> roleAuthEntityList = sysRoleAuthMapper
//        .select(c -> c.where().and(SysRoleAuthNames.roleId, SqlBuilder.isIn(Arrays.asList(roleIds))));
//      List<Authority> authList = roleAuthEntityList.stream().map(SysRoleAuthEntity::getAuthId).distinct()
//        .map(Authority::createAuthority).collect(Collectors.toList());
//      List<String> authIdList = authList.stream().map(Authority::getAuthority).collect(Collectors.toList());
//      List<Authority> authGroupList = sysAuthMapper.select(c -> c).stream()
//        .filter(e -> authIdList.contains(e.getAuthId())).map(SysAuthEntity::getAuthGroupId).distinct()
//        .map(Authority::createAuthority).collect(Collectors.toList());
//      authList.addAll(authGroupList);
//      param.setAuthorities(authList);
//    }
    return new LoginUser(param);
  }

  public List<SelectOption> loadProjectOptions() {
    List<MProjectEntity> projectEntities = tProjectMapper
      .select(c -> c.where().and(MProjectNames.deleted, SqlBuilder.isEqualTo(false))
        .orderBy(MProjectNames.createdAt.descending()));

    return projectEntities.stream().map(e -> new SelectOption(e.getProjectId(), e.getProjectName()))
      .collect(Collectors.toList());
  }

  static class Authority implements GrantedAuthority {

    private static final long serialVersionUID = 1L;

    static HashMap<String, Authority> authorities = new HashMap<>();

    private String value;

    private Authority() {
    }

    private Authority(String authority) {
      this.value = authority;
    }

    static Authority createAuthority(String authority) {

      return authorities.computeIfAbsent(authority, Authority::new);
    }

    @Override
    public String getAuthority() {
      return value;
    }

    @Override
    public String toString() {
      return getAuthority();
    }
  }

  static class Role extends Authority {

    private static final long serialVersionUID = 1L;

    private Role() {
    }

    private Role(String role) {
      super(role);
    }

    @Override
    public String getAuthority() {
      return "ROLE_" + super.getAuthority();
    }

    public static Authority createRole(String authority) {
      return authorities.computeIfAbsent(authority, Role::new);
    }

  }
}
