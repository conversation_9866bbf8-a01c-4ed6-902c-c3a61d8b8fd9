package com.data.datatools.common.mybatis;

import com.data.datatools.common.base.dao.BaseEntity;
import com.data.datatools.utils.ReflectUtils;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.Collection;
import org.apache.commons.collections.CollectionUtils;


/**
 * エンティティ共通フィールド処理用クラスです。
 *
 * <AUTHOR>
 */
public final class CommonField {

  private CommonField() {
  }

  /**
   * エンティティ共通フィールドを設定処理を行います。
   *
   * @param entity エンティティ
   */
  public static void fill(BaseEntity entity) {
    fill(entity, LocalDateTime.now());
  }

  /**
   * エンティティ共通フィールドを設定処理を行います。
   *
   * @param entities エンティティリスト
   */
  public static void fill(Collection<? extends BaseEntity> entities) {
    LocalDateTime time = LocalDateTime.now();
    if (CollectionUtils.isNotEmpty(entities)) {
      entities.forEach(e -> fill(e, time));
    }
  }

  private static void fill(BaseEntity entity, LocalDateTime time) {

    Field createTime = ReflectUtils.getField(entity.getClass(), "createdAt");
    if (createTime != null) {
      ReflectUtils.setFieldValue(entity, "createdAt", time);
    }
    Field createdBy = ReflectUtils.getField(entity.getClass(), "createdBy");
    if (createdBy != null) {
      ReflectUtils.setFieldValue(entity, "createdBy", Operator.of().get());
    }
    Field updateTime = ReflectUtils.getField(entity.getClass(), "updatedAt");
    if (updateTime != null) {
      ReflectUtils.setFieldValue(entity, "updatedAt", time);
    }
    Field createBy = ReflectUtils.getField(entity.getClass(), "updatedBy");
    if (createBy != null) {
      ReflectUtils.setFieldValue(entity, "updatedBy", Operator.of().get());
    }

  }
}
