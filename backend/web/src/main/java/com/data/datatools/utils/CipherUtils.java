package com.data.datatools.utils;

import com.data.datatools.common.exception.BusinessException;
import java.nio.charset.StandardCharsets;
import java.security.GeneralSecurityException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.spec.AlgorithmParameterSpec;
import java.util.Base64;
import javax.crypto.Cipher;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;

/**
 * AES encryption and decryption class.
 */
public final class CipherUtils {

  private static final String AES_ALGORITHM = "AES/GCM/NoPadding";
  private static final int GCM_BLOCK_LENGTH = 128;

  /**
   * HEX
   */
  private static final int HEX = 0xFF;
  private static final String KEY_ALGORITHM = "AES";

  private static final String key = "^x1z@12*au28c&9j";
  /**
   * CipherUtils.
   */
  private CipherUtils() {
  }

  public static String encrypt(String plainText) {
    try {
      return encrypt(plainText, key);
    } catch (GeneralSecurityException e) {
      throw new RuntimeException(e);
    }
  }
  public static String encrypt(String plainText, String cipherKey) throws GeneralSecurityException {
    if (cipherKey == null) {
      throw new IllegalArgumentException("cipher key(null)");
    }
    if (cipherKey.length() != 16 && cipherKey.length() != 24 && cipherKey.length() != 32) {
      throw new IllegalArgumentException("cipher key error.");
    }

    if (plainText == null) {
      return null;
    }

    byte[] keySpecBytes = cipherKey.getBytes();
    byte[] ivBytes = getIVBytes(cipherKey);

    SecretKeySpec sks = new SecretKeySpec(keySpecBytes, KEY_ALGORITHM);
    GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(GCM_BLOCK_LENGTH, ivBytes);
    Cipher encryptCipher = getAESCipher(sks, gcmParameterSpec, Cipher.ENCRYPT_MODE);

    return new String(Base64.getEncoder().encode(encryptCipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8))));
  }

  public static String decrypt(String cipherText) throws GeneralSecurityException {
    return decrypt(cipherText, key);
  }

  public static String decrypt(String cipherText, String cipherKey) throws GeneralSecurityException {
    if (cipherKey == null) {
      throw new IllegalArgumentException("cipher key(null)");
    }
    if (cipherKey.length() != 16 && cipherKey.length() != 24 && cipherKey.length() != 32) {
      throw new IllegalArgumentException("cipher key error.");
    }

    if (StringUtils.isEmpty(cipherText)) {
      return cipherText;
    }

    byte[] keySpecBytes = cipherKey.getBytes();
    byte[] ivBytes = getIVBytes(cipherKey);

    SecretKeySpec sks = new SecretKeySpec(keySpecBytes, KEY_ALGORITHM);
    GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(GCM_BLOCK_LENGTH, ivBytes);
    Cipher decryptCipher = getAESCipher(sks, gcmParameterSpec, Cipher.DECRYPT_MODE);

    return new String(decryptCipher.doFinal(Base64.getDecoder().decode(cipherText)));
  }

  public static String sha256(String msg) {
    try {
      MessageDigest md = MessageDigest.getInstance("SHA-256");
      byte[] digest = md.digest(msg.getBytes(StandardCharsets.UTF_8));
      return bytesToHEX(digest);
    } catch (NoSuchAlgorithmException e) {
      throw new BusinessException("");
    }
  }

  private static String bytesToHEX(byte[] bytes) {
    if (bytes == null || bytes.length == 0) {
      return null;
    }
    StringBuilder result = new StringBuilder();
    for (byte thisByte : bytes) {
      String hv = Integer.toHexString(thisByte & HEX);
      result.append(hv.length() < 2 ? ("0" + hv) : hv);
    }
    return result.toString().toLowerCase();
  }

  private static Cipher getAESCipher(SecretKeySpec sks, AlgorithmParameterSpec algorithmParameterSpec, int cipherMode)
    throws GeneralSecurityException {
    Cipher cipher = Cipher.getInstance(AES_ALGORITHM);
    cipher.init(cipherMode, sks, algorithmParameterSpec);
    return cipher;
  }
  private static byte[] getIVBytes(String cipherKey) {
    byte[] cipherKeyBytes = cipherKey.getBytes();
    byte[] ivBytes = new byte[16];
    System.arraycopy(cipherKeyBytes, cipherKeyBytes.length - 8, ivBytes, 0, 8);
    System.arraycopy(cipherKeyBytes, 0, ivBytes, 8, 8);
    return ivBytes;
  }
}
