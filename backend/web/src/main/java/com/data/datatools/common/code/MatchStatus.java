package com.data.datatools.common.code;

import java.util.Objects;

public enum MatchStatus {

  COMPLETE("0", "完成"),

  PROCESS_ING("1", "进行中"),

  COMPLETE_FAIL("2", "完成(有匹配失败)");

  private final String status;

  private final String name;

  MatchStatus(String status, String name) {
    this.status = status;
    this.name = name;
  }

  public String status() {
    return this.status;
  }

  public String statusName() {
    return this.name;
  }

  public static String status(String status) {
    for (MatchStatus value : MatchStatus.values()) {
      if (Objects.equals(value.status, status)) {
        return value.status;
      }
    }
    return "";
  }

  public static String statusName(String status) {
    for (MatchStatus value : MatchStatus.values()) {
      if (Objects.equals(value.status, status)) {
        return value.name;
      }
    }
    return "-";
  }

  public boolean is(String datasetStatus) {
    return Objects.equals(this.status, datasetStatus);
  }
}
