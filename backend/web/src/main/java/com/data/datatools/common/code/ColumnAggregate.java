package com.data.datatools.common.code;

import com.data.datatools.common.model.SelectOption;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public enum ColumnAggregate {

  SUM("sum", "求和"), MAX("max", "最大值"), MIN("min", "最小值"), AVG("avg", "平均值"), COUNT("count", "计数"), //
  COUNT_COND("count_cond", "计数(含条件)"), STDDEV("stddev", "标准偏差"), VARIANCE("variance", "方差"), LEFT("left", "左取(n)位");

  private final String value;
  private final String label;

  ColumnAggregate(String value, String label) {
    this.value = value;
    this.label = label;
  }

  public static String label(String aggregate) {
    for (ColumnAggregate value : ColumnAggregate.values()) {
      if (value.value.equalsIgnoreCase(aggregate)) {
        return value.label;
      }
    }
    return "";
  }

  public static List<SelectOption> selectOptions() {
    return Arrays.stream(ColumnAggregate.values()).map(e -> new SelectOption(e.value, e.label))
      .collect(Collectors.toList());
  }

  public static Map<String, String> getList() {
    Map<String, String> map = new HashMap<>();
    for (ColumnAggregate value : ColumnAggregate.values()) {
      map.put(value.value, value.label);
    }
    return map;
  }

  public static String getAggregate(int type) {
    if (ColumnType.NUMBER.is(type)) {
      return ColumnAggregate.SUM.value;
    }
    return ColumnAggregate.COUNT.value;
  }

  public static String getAggregateFunc(String aggregate) {
    if (COUNT_COND.is(aggregate)) {
      return COUNT.value;
    }
    return aggregate;
  }

  public boolean is(String aggregate) {
    return this.value.equalsIgnoreCase(aggregate);
  }
}
