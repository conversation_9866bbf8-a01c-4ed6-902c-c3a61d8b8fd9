package com.data.datatools.utils;

import com.alibaba.fastjson.JSONObject;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

public final class StringUtils {

  private StringUtils() {
  }

  public static boolean isEmpty(String text) {
    return org.apache.commons.lang3.StringUtils.isEmpty(text)
      || org.apache.commons.lang3.StringUtils.isBlank(text);
  }

  public static boolean isNotEmpty(String text) {
    return !isEmpty(text);
  }

  public static boolean isEmpty(String[] array) {
    return array == null || array.length == 0;
  }

  public static boolean isNotEmpty(String[] array) {
    return !isEmpty(array);
  }

  public static String join(Iterable<?> iterable, String separator) {
    return org.apache.commons.lang3.StringUtils.join(iterable, separator);
  }

  public static String concat(String... texts) {
    StringBuilder sb = new StringBuilder();
    if (texts != null) {
      for (String text : texts) {
        sb.append(text);
      }
      return sb.toString();
    }
    return sb.toString();
  }

  public static String encodeExtraData(Map<String, String> extraData) {
    Map<String, String> map = new HashMap<>();
    extraData.forEach((k, v) -> {
      try {
        map.put(k, URLEncoder.encode(v, StandardCharsets.UTF_8.name()));
      } catch (UnsupportedEncodingException e) {
        throw new RuntimeException(e);
      }
    });
    return JSONObject.toJSONString(map);
  }

  public static String generateUUID() {
    return UUID.randomUUID().toString().replace("-", "");
  }

  public static String concatPath(String parent, String... children) {
    String basePath = parent;
    for (String child : children) {
      basePath = concat(basePath, child);
    }
    return basePath;
  }

  private static String concat(String base, String child) {
    String basePath = base;
    if (base.endsWith("/") && child.startsWith("/")) {
      basePath += child.substring(1);
    } else if (!base.endsWith("/") && !child.startsWith("/")) {
      basePath += "/" + child;
    } else {
      basePath += child;
    }
    return basePath;
  }

  private static final String[] hexDigits = new String[]{"0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "a",
    "b", "c", "d", "e", "f"};

  public static String encodeByMD5(String originText) {
    if (originText != null) {
      try {
        // 创建具有指定算法名称的信息摘要
        MessageDigest md = MessageDigest.getInstance("MD5");
        // 使用指定的字节数组对摘要进行最后的更新，然后完成摘要计算
        byte[] b = md.digest(originText.getBytes());
        // 将得到的字节数组编程字符串返回
        StringBuilder sb = new StringBuilder();
        for (int j : b) {
          int d = j;
          if (d < 0) {
            d += 256;
          }
          int d1 = d / 16;
          int d2 = d % 16;
          sb.append(hexDigits[d1]).append(hexDigits[d2]);
        }
        return sb.toString().toLowerCase();
      } catch (Exception ex) {
        throw new RuntimeException(ex);
      }
    }
    return null;
  }

  public static String show(String... names) {
    for (String name : names) {
      if (StringUtils.isNotEmpty(name)) {
        return name;
      }
    }
    return names[names.length - 1];
  }

  public static String clean(String text, String c) {
    return text.replaceAll(c, "");
  }

  public static boolean equals(String a, String b) {
    return Objects.equals(a, b);
  }
}
