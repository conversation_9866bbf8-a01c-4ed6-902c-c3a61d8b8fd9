package com.data.datatools.web.system.user.model;

import com.data.datatools.common.base.BaseCondition;
import lombok.Data;

@Data
public class UserCondition extends BaseCondition {

  /**
   * 是否是初始操作
   */
  private boolean init;

  /**
   * userId
   */
  private String userId;

  private String optType;

  /**
   * searchInput
   */
  private String searchInput;

  /**
   * searchRoles
   */
  private String[] userTypes;

  private String district;
}
