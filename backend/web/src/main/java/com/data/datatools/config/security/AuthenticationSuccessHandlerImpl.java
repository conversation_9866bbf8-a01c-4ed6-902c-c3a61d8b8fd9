package com.data.datatools.config.security;

import com.data.datatools.common.model.SelectOption;
import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import com.data.datatools.common.response.DataResponse;
import com.data.datatools.config.security.model.LoginUser;
import com.data.datatools.web.auth.LoginAuthService;
import com.data.datatools.web.auth.model.UserModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;

public class AuthenticationSuccessHandlerImpl implements AuthenticationSuccessHandler {

  private static final Logger LOGGER = LoggerFactory.getLogger(AuthenticationSuccessHandlerImpl.class);

  private final LoginAuthService loginAuthService;

  public AuthenticationSuccessHandlerImpl(LoginAuthService loginAuthService) {
    this.loginAuthService = loginAuthService;
  }

  @Override
  public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response,
    Authentication authentication) throws IOException {
    LoginUser loginUser = (LoginUser) authentication.getPrincipal();

    List<SelectOption> projectOptions = loginAuthService.loadProjectOptions();

    // loginAuthService.updateWrongCount(loginUser.getUserId(), true);
    UserModel user = new UserModel(request.getSession().getId(), loginUser, projectOptions);
    DataResponse<UserModel> data = new DataResponse<>();
    data.setValue(user);
    LOGGER.info("[{}] - login success!", user.getUserId());
    ResponseSupport.jsonResponse(response, data);
  }
}
