package com.data.datatools.web.system.user.model;

import java.util.List;
import lombok.Data;

@Data
public class UserInfoModel {

  /**
   * id
   */
  private String userId;

  /**
   * user
   */
  private String userName;

  /**
   * email
   */
  private String userEmail;

  private String password;

  private boolean updatePass;

  /**
   * role_Id
   */
  private String userType;

  private List<String> project;

  private String city;

  private String district;

  private String street;

  private List<String> town;

  /**
   * deleted
   */
  private boolean userState;

}
