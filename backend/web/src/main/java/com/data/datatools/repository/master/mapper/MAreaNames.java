package com.data.datatools.repository.master.mapper;

import java.sql.JDBCType;
import java.time.LocalDateTime;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class MAreaNames {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_area")
    public static final MArea MArea = new MArea();

    /**
     * 主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_area.area_id")
    public static final SqlColumn<String> areaId = MArea.areaId;

    /**
     * 地址
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_area.address")
    public static final SqlColumn<String> address = MArea.address;

    /**
     * 省
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_area.prov")
    public static final SqlColumn<String> prov = MArea.prov;

    /**
     * 市
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_area.city")
    public static final SqlColumn<String> city = MArea.city;

    /**
     * 区
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_area.district")
    public static final SqlColumn<String> district = MArea.district;

    /**
     * 镇
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_area.town")
    public static final SqlColumn<String> town = MArea.town;

    /**
     * 街道
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_area.road")
    public static final SqlColumn<String> road = MArea.road;

    /**
     * 路
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_area.roads")
    public static final SqlColumn<String> roads = MArea.roads;

    /**
     * 子路
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_area.subroad")
    public static final SqlColumn<String> subroad = MArea.subroad;

    /**
     * 插入时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_area.created_at")
    public static final SqlColumn<LocalDateTime> createdAt = MArea.createdAt;

    /**
     * 做成者
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_area.created_by")
    public static final SqlColumn<String> createdBy = MArea.createdBy;

    /**
     * 更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_area.updated_at")
    public static final SqlColumn<LocalDateTime> updatedAt = MArea.updatedAt;

    /**
     * 更新者
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_area.updated_by")
    public static final SqlColumn<String> updatedBy = MArea.updatedBy;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_area")
    public static final class MArea extends SqlTable {
        public final SqlColumn<String> areaId = column("area_id", JDBCType.VARCHAR);

        public final SqlColumn<String> address = column("address", JDBCType.VARCHAR);

        public final SqlColumn<String> prov = column("prov", JDBCType.VARCHAR);

        public final SqlColumn<String> city = column("city", JDBCType.VARCHAR);

        public final SqlColumn<String> district = column("district", JDBCType.VARCHAR);

        public final SqlColumn<String> town = column("town", JDBCType.VARCHAR);

        public final SqlColumn<String> road = column("road", JDBCType.VARCHAR);

        public final SqlColumn<String> roads = column("roads", JDBCType.VARCHAR);

        public final SqlColumn<String> subroad = column("subroad", JDBCType.VARCHAR);

        public final SqlColumn<LocalDateTime> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createdBy = column("created_by", JDBCType.VARCHAR);

        public final SqlColumn<LocalDateTime> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updatedBy = column("updated_by", JDBCType.VARCHAR);

        public MArea() {
            super("m_area");
        }
    }
}