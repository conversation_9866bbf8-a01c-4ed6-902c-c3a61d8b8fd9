package com.data.datatools.repository.master.entity;

import com.data.datatools.common.base.dao.BaseEntity;
import java.io.Serializable;
import java.time.LocalDateTime;
import javax.annotation.Generated;

/**
 * 数据修改历史
 *
 * database table [dp_master..t_data_edit_history]
 */
public class MDataEditHistoryEntity implements Serializable, BaseEntity {
    /**
     * 主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: dp_master..t_data_edit_history.id")
    private Integer id;

    /**
     * 表名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: dp_master..t_data_edit_history.table_id")
    private String tableId;

    /**
     * 数据ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: dp_master..t_data_edit_history.record_id")
    private Integer recordId;

    /**
     * 插入时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: dp_master..t_data_edit_history.created_at")
    private LocalDateTime createdAt;

    /**
     * 做成者
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: dp_master..t_data_edit_history.created_by")
    private String createdBy;

    /**
     * Diff
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: dp_master..t_data_edit_history.edit_info")
    private String editInfo;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dp_master..t_data_edit_history")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: dp_master..t_data_edit_history.id")
    public Integer getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: dp_master..t_data_edit_history.id")
    public void setId(Integer id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: dp_master..t_data_edit_history.table_id")
    public String getTableId() {
        return tableId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: dp_master..t_data_edit_history.table_id")
    public void setTableId(String tableId) {
        this.tableId = tableId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: dp_master..t_data_edit_history.record_id")
    public Integer getRecordId() {
        return recordId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: dp_master..t_data_edit_history.record_id")
    public void setRecordId(Integer recordId) {
        this.recordId = recordId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: dp_master..t_data_edit_history.created_at")
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: dp_master..t_data_edit_history.created_at")
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: dp_master..t_data_edit_history.created_by")
    public String getCreatedBy() {
        return createdBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: dp_master..t_data_edit_history.created_by")
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: dp_master..t_data_edit_history.edit_info")
    public String getEditInfo() {
        return editInfo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: dp_master..t_data_edit_history.edit_info")
    public void setEditInfo(String editInfo) {
        this.editInfo = editInfo;
    }
}
