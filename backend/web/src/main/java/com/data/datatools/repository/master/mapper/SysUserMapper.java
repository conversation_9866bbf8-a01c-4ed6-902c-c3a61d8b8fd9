package com.data.datatools.repository.master.mapper;

import static com.data.datatools.repository.master.mapper.SysUserNames.*;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

import com.data.datatools.common.mybatis.CommonField;
import com.data.datatools.common.mybatis.Operator;
import com.data.datatools.common.mybatis.VersionAdd;
import com.data.datatools.repository.master.entity.SysUserEntity;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.insert.render.MultiRowInsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface SysUserMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_user")
    BasicColumn[] selectList = BasicColumn.columnList(userId, userName, userEmail, password, passwordStatus, passwordUpdatedAt, currentProject, token, loginWrongCount, userType, prov, city, district, town, road, remark, loginTime, loginIp, deleted, createdAt, createdBy, updatedAt, updatedBy);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_user")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_user")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_user")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    int insert(InsertStatementProvider<SysUserEntity> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_user")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultiple")
    int insertMultiple(MultiRowInsertStatementProvider<SysUserEntity> multipleInsertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_user")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("SysUserEntityResult")
    Optional<SysUserEntity> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_user")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="SysUserEntityResult", value = {
        @Result(column="user_id", property="userId", jdbcType=JdbcType.VARCHAR, id=true),
        @Result(column="user_name", property="userName", jdbcType=JdbcType.VARCHAR),
        @Result(column="user_email", property="userEmail", jdbcType=JdbcType.VARCHAR),
        @Result(column="password", property="password", jdbcType=JdbcType.VARCHAR),
        @Result(column="password_status", property="passwordStatus", jdbcType=JdbcType.CHAR),
        @Result(column="password_updated_at", property="passwordUpdatedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="current_project", property="currentProject", jdbcType=JdbcType.CHAR),
        @Result(column="token", property="token", jdbcType=JdbcType.VARCHAR),
        @Result(column="login_wrong_count", property="loginWrongCount", jdbcType=JdbcType.INTEGER),
        @Result(column="user_type", property="userType", jdbcType=JdbcType.CHAR),
        @Result(column="prov", property="prov", jdbcType=JdbcType.VARCHAR),
        @Result(column="city", property="city", jdbcType=JdbcType.VARCHAR),
        @Result(column="district", property="district", jdbcType=JdbcType.VARCHAR),
        @Result(column="town", property="town", jdbcType=JdbcType.VARCHAR),
        @Result(column="road", property="road", jdbcType=JdbcType.VARCHAR),
        @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
        @Result(column="login_time", property="loginTime", jdbcType=JdbcType.VARCHAR),
        @Result(column="login_ip", property="loginIp", jdbcType=JdbcType.VARCHAR),
        @Result(column="deleted", property="deleted", jdbcType=JdbcType.BIT),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="created_by", property="createdBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="updated_at", property="updatedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_by", property="updatedBy", jdbcType=JdbcType.VARCHAR)
    })
    List<SysUserEntity> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_user")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_user")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, sysUser, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_user")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, sysUser, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_user")
    default int deleteByPrimaryKey(String userId_) {
        return delete(c -> 
            c.where(userId, isEqualTo(userId_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_user")
    default int insert(SysUserEntity record) {
        CommonField.fill(record);
        return MyBatis3Utils.insert(this::insert, record, sysUser, c ->
            c.map(userId).toProperty("userId")
            .map(userName).toProperty("userName")
            .map(userEmail).toProperty("userEmail")
            .map(password).toProperty("password")
            .map(passwordStatus).toProperty("passwordStatus")
            .map(passwordUpdatedAt).toProperty("passwordUpdatedAt")
            .map(currentProject).toProperty("currentProject")
            .map(token).toProperty("token")
            .map(loginWrongCount).toProperty("loginWrongCount")
            .map(userType).toProperty("userType")
            .map(prov).toProperty("prov")
            .map(city).toProperty("city")
            .map(district).toProperty("district")
            .map(town).toProperty("town")
            .map(road).toProperty("road")
            .map(remark).toProperty("remark")
            .map(loginTime).toProperty("loginTime")
            .map(loginIp).toProperty("loginIp")
            .map(deleted).toProperty("deleted")
            .map(createdAt).toProperty("createdAt")
            .map(createdBy).toProperty("createdBy")
            .map(updatedAt).toProperty("updatedAt")
            .map(updatedBy).toProperty("updatedBy")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_user")
    default int insertMultiple(Collection<SysUserEntity> records) {
        CommonField.fill(records);
        return MyBatis3Utils.insertMultiple(this::insertMultiple, records, sysUser, c ->
            c.map(userId).toProperty("userId")
            .map(userName).toProperty("userName")
            .map(userEmail).toProperty("userEmail")
            .map(password).toProperty("password")
            .map(passwordStatus).toProperty("passwordStatus")
            .map(passwordUpdatedAt).toProperty("passwordUpdatedAt")
            .map(currentProject).toProperty("currentProject")
            .map(token).toProperty("token")
            .map(loginWrongCount).toProperty("loginWrongCount")
            .map(userType).toProperty("userType")
            .map(prov).toProperty("prov")
            .map(city).toProperty("city")
            .map(district).toProperty("district")
            .map(town).toProperty("town")
            .map(road).toProperty("road")
            .map(remark).toProperty("remark")
            .map(loginTime).toProperty("loginTime")
            .map(loginIp).toProperty("loginIp")
            .map(deleted).toProperty("deleted")
            .map(createdAt).toProperty("createdAt")
            .map(createdBy).toProperty("createdBy")
            .map(updatedAt).toProperty("updatedAt")
            .map(updatedBy).toProperty("updatedBy")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_user")
    default int insertSelective(SysUserEntity record) {
        CommonField.fill(record);
        return MyBatis3Utils.insert(this::insert, record, sysUser, c ->
            c.map(userId).toPropertyWhenPresent("userId", record::getUserId)
            .map(userName).toPropertyWhenPresent("userName", record::getUserName)
            .map(userEmail).toPropertyWhenPresent("userEmail", record::getUserEmail)
            .map(password).toPropertyWhenPresent("password", record::getPassword)
            .map(passwordStatus).toPropertyWhenPresent("passwordStatus", record::getPasswordStatus)
            .map(passwordUpdatedAt).toPropertyWhenPresent("passwordUpdatedAt", record::getPasswordUpdatedAt)
            .map(currentProject).toPropertyWhenPresent("currentProject", record::getCurrentProject)
            .map(token).toPropertyWhenPresent("token", record::getToken)
            .map(loginWrongCount).toPropertyWhenPresent("loginWrongCount", record::getLoginWrongCount)
            .map(userType).toPropertyWhenPresent("userType", record::getUserType)
            .map(prov).toPropertyWhenPresent("prov", record::getProv)
            .map(city).toPropertyWhenPresent("city", record::getCity)
            .map(district).toPropertyWhenPresent("district", record::getDistrict)
            .map(town).toPropertyWhenPresent("town", record::getTown)
            .map(road).toPropertyWhenPresent("road", record::getRoad)
            .map(remark).toPropertyWhenPresent("remark", record::getRemark)
            .map(loginTime).toPropertyWhenPresent("loginTime", record::getLoginTime)
            .map(loginIp).toPropertyWhenPresent("loginIp", record::getLoginIp)
            .map(deleted).toPropertyWhenPresent("deleted", record::getDeleted)
            .map(createdAt).toPropertyWhenPresent("createdAt", record::getCreatedAt)
            .map(createdBy).toPropertyWhenPresent("createdBy", record::getCreatedBy)
            .map(updatedAt).toPropertyWhenPresent("updatedAt", record::getUpdatedAt)
            .map(updatedBy).toPropertyWhenPresent("updatedBy", record::getUpdatedBy)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_user")
    default Optional<SysUserEntity> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, sysUser, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_user")
    default List<SysUserEntity> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, sysUser, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_user")
    default List<SysUserEntity> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, sysUser, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_user")
    default Optional<SysUserEntity> selectByPrimaryKey(String userId_) {
        return selectOne(c ->
            c.where(userId, isEqualTo(userId_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_user")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, sysUser, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_user")
    static UpdateDSL<UpdateModel> updateAllColumns(SysUserEntity record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(userId).equalTo(record::getUserId)
                .set(userName).equalTo(record::getUserName)
                .set(userEmail).equalTo(record::getUserEmail)
                .set(password).equalTo(record::getPassword)
                .set(passwordStatus).equalTo(record::getPasswordStatus)
                .set(passwordUpdatedAt).equalTo(record::getPasswordUpdatedAt)
                .set(currentProject).equalTo(record::getCurrentProject)
                .set(token).equalTo(record::getToken)
                .set(loginWrongCount).equalTo(record::getLoginWrongCount)
                .set(userType).equalTo(record::getUserType)
                .set(prov).equalTo(record::getProv)
                .set(city).equalTo(record::getCity)
                .set(district).equalTo(record::getDistrict)
                .set(town).equalTo(record::getTown)
                .set(road).equalTo(record::getRoad)
                .set(remark).equalTo(record::getRemark)
                .set(loginTime).equalTo(record::getLoginTime)
                .set(loginIp).equalTo(record::getLoginIp)
                .set(deleted).equalTo(record::getDeleted)
                .set(updatedAt).equalTo(LocalDateTime.now())
                .set(updatedBy).equalTo(Operator.of());
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_user")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(SysUserEntity record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(userId).equalToWhenPresent(record::getUserId)
                .set(userName).equalToWhenPresent(record::getUserName)
                .set(userEmail).equalToWhenPresent(record::getUserEmail)
                .set(password).equalToWhenPresent(record::getPassword)
                .set(passwordStatus).equalToWhenPresent(record::getPasswordStatus)
                .set(passwordUpdatedAt).equalToWhenPresent(record::getPasswordUpdatedAt)
                .set(currentProject).equalToWhenPresent(record::getCurrentProject)
                .set(token).equalToWhenPresent(record::getToken)
                .set(loginWrongCount).equalToWhenPresent(record::getLoginWrongCount)
                .set(userType).equalToWhenPresent(record::getUserType)
                .set(prov).equalToWhenPresent(record::getProv)
                .set(city).equalToWhenPresent(record::getCity)
                .set(district).equalToWhenPresent(record::getDistrict)
                .set(town).equalToWhenPresent(record::getTown)
                .set(road).equalToWhenPresent(record::getRoad)
                .set(remark).equalToWhenPresent(record::getRemark)
                .set(loginTime).equalToWhenPresent(record::getLoginTime)
                .set(loginIp).equalToWhenPresent(record::getLoginIp)
                .set(deleted).equalToWhenPresent(record::getDeleted)
                .set(updatedAt).equalTo(LocalDateTime.now())
                .set(updatedBy).equalTo(Operator.of());
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_user")
    default int updateByPrimaryKey(SysUserEntity record) {
        return update(c ->
            c.set(userName).equalTo(record::getUserName)
            .set(userEmail).equalTo(record::getUserEmail)
            .set(password).equalTo(record::getPassword)
            .set(passwordStatus).equalTo(record::getPasswordStatus)
            .set(passwordUpdatedAt).equalTo(record::getPasswordUpdatedAt)
            .set(currentProject).equalTo(record::getCurrentProject)
            .set(token).equalTo(record::getToken)
            .set(loginWrongCount).equalTo(record::getLoginWrongCount)
            .set(userType).equalTo(record::getUserType)
            .set(prov).equalTo(record::getProv)
            .set(city).equalTo(record::getCity)
            .set(district).equalTo(record::getDistrict)
            .set(town).equalTo(record::getTown)
            .set(road).equalTo(record::getRoad)
            .set(remark).equalTo(record::getRemark)
            .set(loginTime).equalTo(record::getLoginTime)
            .set(loginIp).equalTo(record::getLoginIp)
            .set(deleted).equalTo(record::getDeleted)
            .set(updatedAt).equalTo(LocalDateTime.now())
            .set(updatedBy).equalTo(Operator.of())
            .where(userId, isEqualTo(record::getUserId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_user")
    default int updateByPrimaryKeySelective(SysUserEntity record) {
        return update(c ->
            c.set(userName).equalToWhenPresent(record::getUserName)
            .set(userEmail).equalToWhenPresent(record::getUserEmail)
            .set(password).equalToWhenPresent(record::getPassword)
            .set(passwordStatus).equalToWhenPresent(record::getPasswordStatus)
            .set(passwordUpdatedAt).equalToWhenPresent(record::getPasswordUpdatedAt)
            .set(currentProject).equalToWhenPresent(record::getCurrentProject)
            .set(token).equalToWhenPresent(record::getToken)
            .set(loginWrongCount).equalToWhenPresent(record::getLoginWrongCount)
            .set(userType).equalToWhenPresent(record::getUserType)
            .set(prov).equalToWhenPresent(record::getProv)
            .set(city).equalToWhenPresent(record::getCity)
            .set(district).equalToWhenPresent(record::getDistrict)
            .set(town).equalToWhenPresent(record::getTown)
            .set(road).equalToWhenPresent(record::getRoad)
            .set(remark).equalToWhenPresent(record::getRemark)
            .set(loginTime).equalToWhenPresent(record::getLoginTime)
            .set(loginIp).equalToWhenPresent(record::getLoginIp)
            .set(deleted).equalToWhenPresent(record::getDeleted)
            .set(updatedAt).equalTo(LocalDateTime.now())
            .set(updatedBy).equalTo(Operator.of())
            .where(userId, isEqualTo(record::getUserId))
        );
    }
}