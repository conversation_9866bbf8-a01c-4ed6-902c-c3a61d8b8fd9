package com.data.datatools.common.code;

public enum UserType {

  /**
   * 超级管理员
   */
  MANAGER("0", "超级管理员"),

  /**
   * 市级管理员
   */
  CITY("1", "市级管理员"),

  /**
   * 区级管理员
   */
  DISTRICT("2", "区级管理员"),

  /**
   * 街道管理员
   */
  STREET("3", "街道管理员");

  private final String type;

  private final String typeName;

  UserType(String type, String typeName) {
    this.type = type;
    this.typeName = typeName;
  }

  public static String getAuthority(String userType) {
    if (MANAGER.is(userType)) {
      return "A";
    }
    if (CITY.is(userType)) {
      return "C";
    }
    if (DISTRICT.is(userType)) {
      return "D";
    }
    return "S";
  }

  public String type() {
    return this.type;
  }

  public String typeName() {
    return this.typeName;
  }

  public static String getTypeName(String type) {
    for (UserType value : UserType.values()) {
      if (value.is(type)) {
        return value.typeName();
      }
    }
    return "";
  }

  public boolean is(String type) {
    return this.type.equalsIgnoreCase(type);
  }
}
