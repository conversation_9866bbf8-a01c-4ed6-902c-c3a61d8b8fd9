package com.data.datatools.common.converter;

import com.data.datatools.common.exception.BusinessException;
import com.data.datatools.common.message.MessageConstants;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.convert.converter.Converter;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

public class LocalDateConverter implements Converter<String, LocalDate> {

  @Override
  public LocalDate convert(String s) {
    if (!StringUtils.isBlank(s)) {
      try {
        return LocalDate.parse(s, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
      } catch (DateTimeParseException e) {
        throw new BusinessException(e, MessageConstants.MESSAGE_E0099);
      }
    } else {
      return null;
    }
  }
}
