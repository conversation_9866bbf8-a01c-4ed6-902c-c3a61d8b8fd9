package com.data.datatools.config.security;

import ch.qos.logback.classic.ClassicConstants;
import java.io.IOException;
import java.util.Date;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import com.data.datatools.common.security.SecuritySupport;
import org.slf4j.MDC;
import org.springframework.web.filter.GenericFilterBean;

public class SecurityMDCInitializerFilter extends GenericFilterBean {

  private static final String UN_KNOWN = "unknown";

  @Override
  public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
    throws IOException, ServletException {

    HttpServletRequest req = (HttpServletRequest) request;

    MDC.put("user_agent", req.getHeader("User-Agent"));
    MDC.put("ip_address", getIpAddress(req));
    MDC.put("session_id", req.getSession().getId());
    MDC.put(ClassicConstants.REQUEST_X_FORWARDED_FOR, req.getHeader("X-Forwarded-For"));
    MDC.put("startTime", String.valueOf(new Date().getTime()));
    String userCd = SecuritySupport.getLoginUserId();
    if (userCd == null) {
      userCd = request.getParameter("username");
    }
    MDC.put("user_id", userCd);

    chain.doFilter(request, response);
  }

  public static String getIpAddress(HttpServletRequest request) {
    String ip = request.getHeader("x-forwarded-for");
    if (ip == null || ip.length() == 0 || UN_KNOWN.equalsIgnoreCase(ip)) {
      ip = request.getHeader("Proxy-Client-IP");
    }
    if (ip == null || ip.length() == 0 || UN_KNOWN.equalsIgnoreCase(ip)) {
      ip = request.getHeader("WL-Proxy-Client-IP");
    }
    if (ip == null || ip.length() == 0 || UN_KNOWN.equalsIgnoreCase(ip)) {
      ip = request.getRemoteAddr();
    }
    return ip;
  }
}
