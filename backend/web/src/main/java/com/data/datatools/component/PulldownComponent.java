package com.data.datatools.component;

import com.data.datatools.common.code.ModelType;
import com.data.datatools.common.code.TableType;
import com.data.datatools.common.code.UserType;
import com.data.datatools.common.model.SelectOption;
import com.data.datatools.common.model.TableOption;
import com.data.datatools.common.security.SecuritySupport;
import com.data.datatools.repository.master.entity.MTableMetaEntity;
import com.data.datatools.repository.master.entity.SysAreaShanghaiEntity;
import com.data.datatools.repository.master.mapper.MTableMetaMapper;
import com.data.datatools.repository.master.mapper.MTableMetaNames;
import com.data.datatools.repository.master.mapper.SysAreaShanghaiMapper;
import com.data.datatools.repository.master.mapper.SysAreaShanghaiNames;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.mybatis.dynamic.sql.SqlBuilder;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class PulldownComponent {

  @Autowired
  private MTableMetaMapper tTableMetaMapper;

  @Autowired
  private SysAreaShanghaiMapper areaShanghaiMapper;

  public List<SelectOption> getTableOptions() {
    List<MTableMetaEntity> tableMetaEntities = tTableMetaMapper.select(c -> {
      c.where().and(MTableMetaNames.projectId, SqlBuilder.isEqualTo(SecuritySupport.getCurrentProject()))
        .and(MTableMetaNames.deleted, SqlBuilder.isEqualTo(false));
      return c;
    });

    List<SelectOption> tableOptions = new ArrayList<>();
    for (MTableMetaEntity entity : tableMetaEntities) {
      tableOptions.add(new SelectOption(entity.getTableId(), entity.getTableName()));
    }

    return tableOptions;
  }

  public List<SelectOption> getTableOptions(boolean tree) {
    List<MTableMetaEntity> tableMetaEntities = tTableMetaMapper.select(c -> {
      c.where().and(MTableMetaNames.projectId, SqlBuilder.isEqualTo(SecuritySupport.getCurrentProject()))
        .and(MTableMetaNames.deleted, SqlBuilder.isEqualTo(false));
      return c;
    });

    List<SelectOption> tableOptionList = new ArrayList<>();
    for (MTableMetaEntity entity : tableMetaEntities) {
      tableOptionList
        .add(new SelectOption(new TableOption(entity.getTableId(), TableType.TABLE).toString(),
          entity.getTableName()));
    }

    List<SelectOption> options = new ArrayList<>();
    SelectOption tableOptions = new SelectOption(TableType.TABLE.getType(), TableType.TABLE.getName());
    if (tree) {
      tableOptions.setChildren(tableOptionList);
    } else {
      tableOptions.setOptions(tableOptionList);
    }
    options.add(tableOptions);
    return options;
  }

  public List<SelectOption> useTypeOptions() {
    List<SelectOption> options = new ArrayList<>();

    if (SecuritySupport.isSuperManager()) {
      options.add(new SelectOption(UserType.CITY.type(), UserType.CITY.typeName()));
      options.add(new SelectOption(UserType.DISTRICT.type(), UserType.DISTRICT.typeName()));
      options.add(new SelectOption(UserType.STREET.type(), UserType.STREET.typeName()));
    } else if (SecuritySupport.isCityManager()) {
      options.add(new SelectOption(UserType.DISTRICT.type(), UserType.DISTRICT.typeName()));
      options.add(new SelectOption(UserType.STREET.type(), UserType.STREET.typeName()));
    } else if (SecuritySupport.isDistrict()) {
      options.add(new SelectOption(UserType.STREET.type(), UserType.STREET.typeName()));
    }
    return options;
  }

  public List<SelectOption> districtOptions() {
    return areaShanghaiMapper.select(c -> c).stream().map(SysAreaShanghaiEntity::getDistrict).distinct()
      .map(e -> new SelectOption(e, e)).collect(Collectors.toList());
  }

  public List<SelectOption> townOptions() {
    SelectDSLCompleter completer;
    if (SecuritySupport.isDistrict()) {
      completer = c -> c.where().and(SysAreaShanghaiNames.district, SqlBuilder.isEqualTo(SecuritySupport.getDistrict()));
    } else {
      completer = c -> c;
    }
    List<SysAreaShanghaiEntity> entityList = areaShanghaiMapper.select(completer);
    Map<String, List<SysAreaShanghaiEntity>> entityMap = entityList.stream()
      .collect(Collectors.groupingBy(SysAreaShanghaiEntity::getDistrict, Collectors.toList()));
    List<SelectOption> options = new ArrayList<>();
    entityMap.forEach((k, v) -> {
      SelectOption option = new SelectOption(k, k);
      option.setChildren(v.stream().map(e -> new SelectOption(e.getTown(), e.getTown())).collect(Collectors.toList()));
      options.add(option);
    });
    return options;
  }

  public List<SelectOption> modelTypeOptions() {
    List<SelectOption> options = new ArrayList<>();
    for (ModelType value : ModelType.values()) {
      options.add(new SelectOption(value.type(), value.typeName()));
    }
    return options;
  }
}
