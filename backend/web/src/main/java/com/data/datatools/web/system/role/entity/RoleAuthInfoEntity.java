package com.data.datatools.web.system.role.entity;

import com.data.datatools.common.base.dao.BaseEntity;
import lombok.Data;

@Data
public class RoleAuthInfoEntity implements BaseEntity {
  /**
   * 角色ID
   */
  private Integer roleId;

  /**
   * 权限ID
   */
  private String authId;

  /**
   * 权限名称
   */
  private String authName;

  /**
   * 权限值
   */
  private String roleAuthValue;

  /**
   * 权限分组ID
   */
  private String authGroupId;

  /**
   * 权限分组父ID
   */
  private String authParentGroupId;

  /**
   * 显示顺序
   */
  private int showSequence;
}
