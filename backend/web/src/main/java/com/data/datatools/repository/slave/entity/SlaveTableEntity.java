package com.data.datatools.repository.slave.entity;

import com.data.datatools.common.base.dao.BaseEntity;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

@Data
public class SlaveTableEntity implements Serializable, BaseEntity {

  private Integer id;

  private String importKey;

  private String md5;

  private String areaId;

  private String content;

  private boolean deleted;

  private String createdBy;

  private String updatedBy;

  private LocalDateTime updatedTime;
}
