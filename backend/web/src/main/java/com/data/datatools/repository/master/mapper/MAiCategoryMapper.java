package com.data.datatools.repository.master.mapper;

import static com.data.datatools.repository.master.mapper.MAiCategoryNames.*;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

import com.data.datatools.common.mybatis.CommonField;
import com.data.datatools.common.mybatis.Operator;
import com.data.datatools.common.mybatis.VersionAdd;
import com.data.datatools.repository.master.entity.MAiCategoryEntity;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.insert.render.MultiRowInsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface MAiCategoryMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_category")
    BasicColumn[] selectList = BasicColumn.columnList(id, category, text, createdAt, createdBy, updatedAt, updatedBy);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_category")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_category")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_category")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    int insert(InsertStatementProvider<MAiCategoryEntity> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_category")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultiple")
    int insertMultiple(MultiRowInsertStatementProvider<MAiCategoryEntity> multipleInsertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_category")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("MAiCategoryEntityResult")
    Optional<MAiCategoryEntity> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_category")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="MAiCategoryEntityResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.INTEGER, id=true),
        @Result(column="category", property="category", jdbcType=JdbcType.VARCHAR),
        @Result(column="text", property="text", jdbcType=JdbcType.VARCHAR),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="created_by", property="createdBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="updated_at", property="updatedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_by", property="updatedBy", jdbcType=JdbcType.VARCHAR)
    })
    List<MAiCategoryEntity> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_category")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_category")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, MAiCategory, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_category")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, MAiCategory, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_category")
    default int deleteByPrimaryKey(Integer id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_category")
    default int insert(MAiCategoryEntity record) {
        CommonField.fill(record);
        return MyBatis3Utils.insert(this::insert, record, MAiCategory, c ->
            c.map(id).toProperty("id")
            .map(category).toProperty("category")
            .map(text).toProperty("text")
            .map(createdAt).toProperty("createdAt")
            .map(createdBy).toProperty("createdBy")
            .map(updatedAt).toProperty("updatedAt")
            .map(updatedBy).toProperty("updatedBy")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_category")
    default int insertMultiple(Collection<MAiCategoryEntity> records) {
        CommonField.fill(records);
        return MyBatis3Utils.insertMultiple(this::insertMultiple, records, MAiCategory, c ->
            c.map(id).toProperty("id")
            .map(category).toProperty("category")
            .map(text).toProperty("text")
            .map(createdAt).toProperty("createdAt")
            .map(createdBy).toProperty("createdBy")
            .map(updatedAt).toProperty("updatedAt")
            .map(updatedBy).toProperty("updatedBy")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_category")
    default int insertSelective(MAiCategoryEntity record) {
        CommonField.fill(record);
        return MyBatis3Utils.insert(this::insert, record, MAiCategory, c ->
            c.map(id).toPropertyWhenPresent("id", record::getId)
            .map(category).toPropertyWhenPresent("category", record::getCategory)
            .map(text).toPropertyWhenPresent("text", record::getText)
            .map(createdAt).toPropertyWhenPresent("createdAt", record::getCreatedAt)
            .map(createdBy).toPropertyWhenPresent("createdBy", record::getCreatedBy)
            .map(updatedAt).toPropertyWhenPresent("updatedAt", record::getUpdatedAt)
            .map(updatedBy).toPropertyWhenPresent("updatedBy", record::getUpdatedBy)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_category")
    default Optional<MAiCategoryEntity> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, MAiCategory, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_category")
    default List<MAiCategoryEntity> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, MAiCategory, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_category")
    default List<MAiCategoryEntity> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, MAiCategory, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_category")
    default Optional<MAiCategoryEntity> selectByPrimaryKey(Integer id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_category")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, MAiCategory, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_category")
    static UpdateDSL<UpdateModel> updateAllColumns(MAiCategoryEntity record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalTo(record::getId)
                .set(category).equalTo(record::getCategory)
                .set(text).equalTo(record::getText)
                .set(updatedAt).equalTo(LocalDateTime.now())
                .set(updatedBy).equalTo(Operator.of());
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_category")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(MAiCategoryEntity record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalToWhenPresent(record::getId)
                .set(category).equalToWhenPresent(record::getCategory)
                .set(text).equalToWhenPresent(record::getText)
                .set(updatedAt).equalTo(LocalDateTime.now())
                .set(updatedBy).equalTo(Operator.of());
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_category")
    default int updateByPrimaryKey(MAiCategoryEntity record) {
        return update(c ->
            c.set(category).equalTo(record::getCategory)
            .set(text).equalTo(record::getText)
            .set(updatedAt).equalTo(LocalDateTime.now())
            .set(updatedBy).equalTo(Operator.of())
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_category")
    default int updateByPrimaryKeySelective(MAiCategoryEntity record) {
        return update(c ->
            c.set(category).equalToWhenPresent(record::getCategory)
            .set(text).equalToWhenPresent(record::getText)
            .set(updatedAt).equalTo(LocalDateTime.now())
            .set(updatedBy).equalTo(Operator.of())
            .where(id, isEqualTo(record::getId))
        );
    }
}