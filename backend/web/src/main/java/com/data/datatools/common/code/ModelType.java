package com.data.datatools.common.code;

import java.util.Objects;

public enum ModelType {

  TAG("1", "标签匹配"),

  AI("2", "AI匹配");

  private final String type;

  private final String name;

  ModelType(String type, String name) {
    this.type = type;
    this.name = name;
  }

  public String type() {
    return this.type;
  }

  public String typeName() {
    return this.name;
  }

  public static String type(String type) {
    for (ModelType value : ModelType.values()) {
      if (Objects.equals(value.type, type)) {
        return value.type;
      }
    }
    return "";
  }

  public static String typeName(String type) {
    for (ModelType value : ModelType.values()) {
      if (Objects.equals(value.type, type)) {
        return value.name;
      }
    }
    return "-";
  }

  public boolean is(String type) {
    return Objects.equals(this.type, type);
  }
}
