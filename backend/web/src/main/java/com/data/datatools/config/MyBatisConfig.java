package com.data.datatools.config;

import com.data.datatools.common.mybatis.EscapeInterceptor;
import org.apache.ibatis.annotations.Mapper;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@MapperScan(basePackages = {"com.data.datatools"}, annotationClass = Mapper.class)
public class MyBatisConfig {

  @Bean
  public EscapeInterceptor escapeInterceptor() {
    return new EscapeInterceptor();
  }
}
