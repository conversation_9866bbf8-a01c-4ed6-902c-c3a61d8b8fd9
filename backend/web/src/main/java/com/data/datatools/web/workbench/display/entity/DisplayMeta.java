package com.data.datatools.web.workbench.display.entity;

import com.data.datatools.web.workbench.display.dto.DisplayColumn;
import com.data.datatools.web.workbench.display.dto.DisplayDto;
import com.data.datatools.web.workbench.display.dto.MatchFieldColumn;
import java.util.Comparator;
import java.util.List;
import lombok.Data;

@Data
public class DisplayMeta {

  private String columnMode;

  private List<DisplayColumn> columnList;

  private List<MatchFieldColumn> matchColumnList;

  public DisplayMeta() {
  }

  public DisplayMeta(DisplayDto dto) {
    this.columnMode = dto.getColumnMode();
    this.columnList = dto.getColumnList();
    this.matchColumnList = dto.getMatchColumnList();
  }

  public boolean diff(DisplayMeta other) {
    List<MatchFieldColumn> otherMatchColumnList = other.getMatchColumnList();
    if (otherMatchColumnList.size() != matchColumnList.size()) {
      return true;
    }

    otherMatchColumnList.sort(Comparator.comparing(MatchFieldColumn::getColumnField));
    matchColumnList.sort(Comparator.comparing(MatchFieldColumn::getColumnField));

    return !otherMatchColumnList.equals(matchColumnList);
  }
}
