package com.data.datatools.repository.master.mapper;

import java.sql.JDBCType;
import java.time.LocalDateTime;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class SysSolidUserNames {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_solid_user")
    public static final SysSolidUser sysSolidUser = new SysSolidUser();

    /**
     * 用户ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_solid_user.user_id")
    public static final SqlColumn<String> userId = sysSolidUser.userId;

    /**
     * 用户姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_solid_user.user_name")
    public static final SqlColumn<String> userName = sysSolidUser.userName;

    /**
     * 用户邮箱
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_solid_user.user_email")
    public static final SqlColumn<String> userEmail = sysSolidUser.userEmail;

    /**
     * 密码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_solid_user.password")
    public static final SqlColumn<String> password = sysSolidUser.password;

    /**
     * 密码状态
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_solid_user.password_status")
    public static final SqlColumn<String> passwordStatus = sysSolidUser.passwordStatus;

    /**
     * 密码变更时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_solid_user.password_updated_at")
    public static final SqlColumn<LocalDateTime> passwordUpdatedAt = sysSolidUser.passwordUpdatedAt;

    /**
     * 当前项目
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_solid_user.current_project")
    public static final SqlColumn<String> currentProject = sysSolidUser.currentProject;

    /**
     * URLトークン
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_solid_user.token")
    public static final SqlColumn<String> token = sysSolidUser.token;

    /**
     * 认证失败回数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_solid_user.login_wrong_count")
    public static final SqlColumn<Integer> loginWrongCount = sysSolidUser.loginWrongCount;

    /**
     * 备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_solid_user.remark")
    public static final SqlColumn<String> remark = sysSolidUser.remark;

    /**
     * 最后登录时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_solid_user.login_time")
    public static final SqlColumn<String> loginTime = sysSolidUser.loginTime;

    /**
     * 最后登录IP
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_solid_user.login_ip")
    public static final SqlColumn<String> loginIp = sysSolidUser.loginIp;

    /**
     * 是否删除
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_solid_user.deleted")
    public static final SqlColumn<Boolean> deleted = sysSolidUser.deleted;

    /**
     * 插入时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_solid_user.created_at")
    public static final SqlColumn<LocalDateTime> createdAt = sysSolidUser.createdAt;

    /**
     * 做成者
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_solid_user.created_by")
    public static final SqlColumn<String> createdBy = sysSolidUser.createdBy;

    /**
     * 更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_solid_user.updated_at")
    public static final SqlColumn<LocalDateTime> updatedAt = sysSolidUser.updatedAt;

    /**
     * 更新者
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_solid_user.updated_by")
    public static final SqlColumn<String> updatedBy = sysSolidUser.updatedBy;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_solid_user")
    public static final class SysSolidUser extends SqlTable {
        public final SqlColumn<String> userId = column("user_id", JDBCType.VARCHAR);

        public final SqlColumn<String> userName = column("user_name", JDBCType.VARCHAR);

        public final SqlColumn<String> userEmail = column("user_email", JDBCType.VARCHAR);

        public final SqlColumn<String> password = column("password", JDBCType.VARCHAR);

        public final SqlColumn<String> passwordStatus = column("password_status", JDBCType.CHAR);

        public final SqlColumn<LocalDateTime> passwordUpdatedAt = column("password_updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<String> currentProject = column("current_project", JDBCType.CHAR);

        public final SqlColumn<String> token = column("token", JDBCType.VARCHAR);

        public final SqlColumn<Integer> loginWrongCount = column("login_wrong_count", JDBCType.INTEGER);

        public final SqlColumn<String> remark = column("remark", JDBCType.VARCHAR);

        public final SqlColumn<String> loginTime = column("login_time", JDBCType.VARCHAR);

        public final SqlColumn<String> loginIp = column("login_ip", JDBCType.VARCHAR);

        public final SqlColumn<Boolean> deleted = column("deleted", JDBCType.BIT);

        public final SqlColumn<LocalDateTime> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createdBy = column("created_by", JDBCType.VARCHAR);

        public final SqlColumn<LocalDateTime> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updatedBy = column("updated_by", JDBCType.VARCHAR);

        public SysSolidUser() {
            super("sys_solid_user");
        }
    }
}