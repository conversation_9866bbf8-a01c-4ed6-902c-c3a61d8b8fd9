package com.data.datatools.common.model;

import com.data.datatools.utils.ListUtils;
import java.util.List;
import lombok.Data;

@Data
public class ItemFilter {

  private String operator;
  private String comparison;
  private String value;
  private List<String> values;

  public ItemFilter filter() {
    ItemFilter itemFilter = new ItemFilter();
    itemFilter.setOperator(operator);
    if (comparison.startsWith("like")) {
      itemFilter.setComparison("like");
      switch (comparison) {
        case "like _%":
          itemFilter.setValue(value + "%");
          break;
        case "like %_":
          itemFilter.setValue("%" + value);
          break;
        case "like %_%":
          itemFilter.setValue("%" + value + "%");
          break;
        default:
          itemFilter.setValue(value);
          break;
      }
    } else if (comparison.startsWith("not like")) {
      itemFilter.setComparison("not like");
      itemFilter.setValue("%" + value + "%");
    } else if (comparison.equals("in") || comparison.equals("not in")) {
      if (ListUtils.isEmpty(values)) {
        return null;
      }
      itemFilter.setComparison(comparison);
      itemFilter.setValue(value);
    }
    else {
      itemFilter.setComparison(comparison);
      itemFilter.setValue(value);
    }
    itemFilter.setValues(values);
    return itemFilter;
  }
}
