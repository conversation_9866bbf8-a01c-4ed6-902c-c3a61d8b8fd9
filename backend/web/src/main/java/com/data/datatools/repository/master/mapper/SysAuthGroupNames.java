package com.data.datatools.repository.master.mapper;

import java.sql.JDBCType;
import java.time.LocalDateTime;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class SysAuthGroupNames {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_auth_group")
    public static final SysAuthGroup sysAuthGroup = new SysAuthGroup();

    /**
     * 主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth_group.id")
    public static final SqlColumn<Integer> id = sysAuthGroup.id;

    /**
     * 权限分组ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth_group.auth_group_id")
    public static final SqlColumn<String> authGroupId = sysAuthGroup.authGroupId;

    /**
     * 权限分组名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth_group.auth_group_name")
    public static final SqlColumn<String> authGroupName = sysAuthGroup.authGroupName;

    /**
     * 权限分组显示顺序
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth_group.show_sequence")
    public static final SqlColumn<Short> showSequence = sysAuthGroup.showSequence;

    /**
     * 是否删除
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth_group.deleted")
    public static final SqlColumn<Boolean> deleted = sysAuthGroup.deleted;

    /**
     * 插入时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth_group.created_at")
    public static final SqlColumn<LocalDateTime> createdAt = sysAuthGroup.createdAt;

    /**
     * 做成者
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth_group.created_by")
    public static final SqlColumn<String> createdBy = sysAuthGroup.createdBy;

    /**
     * 更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth_group.updated_at")
    public static final SqlColumn<LocalDateTime> updatedAt = sysAuthGroup.updatedAt;

    /**
     * 更新者
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth_group.updated_by")
    public static final SqlColumn<String> updatedBy = sysAuthGroup.updatedBy;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_auth_group")
    public static final class SysAuthGroup extends SqlTable {
        public final SqlColumn<Integer> id = column("id", JDBCType.INTEGER);

        public final SqlColumn<String> authGroupId = column("auth_group_id", JDBCType.VARCHAR);

        public final SqlColumn<String> authGroupName = column("auth_group_name", JDBCType.VARCHAR);

        public final SqlColumn<Short> showSequence = column("show_sequence", JDBCType.SMALLINT);

        public final SqlColumn<Boolean> deleted = column("deleted", JDBCType.BIT);

        public final SqlColumn<LocalDateTime> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createdBy = column("created_by", JDBCType.VARCHAR);

        public final SqlColumn<LocalDateTime> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updatedBy = column("updated_by", JDBCType.VARCHAR);

        public SysAuthGroup() {
            super("sys_auth_group");
        }
    }
}