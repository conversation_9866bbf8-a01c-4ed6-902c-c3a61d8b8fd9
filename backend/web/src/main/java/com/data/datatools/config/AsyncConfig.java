package com.data.datatools.config;

import com.data.datatools.common.exception.BusinessException;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@Configuration
@EnableAsync
public class AsyncConfig implements AsyncConfigurer {

  @Bean
  @Override
  public Executor getAsyncExecutor() {
    ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    executor.setCorePoolSize(10); // 核心线程数
    executor.setMaxPoolSize(200); // 最大线程数
    executor.setThreadNamePrefix("Async$Executor-"); // 线程名称前缀
    executor.setRejectedExecutionHandler((Runnable r, ThreadPoolExecutor executor1) -> {
      throw new BusinessException("Async$Executor Task Rejected");
    });
    executor.initialize();
    return executor;
  }

  @Bean("taskExecutor")
  public Executor taskExecutor() {
    ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    executor.setCorePoolSize(2);        // 核心线程数
    executor.setMaxPoolSize(5);         // 最大线程数
    executor.setQueueCapacity(100);     // 队列容量
    executor.setThreadNamePrefix("AddressTask-");
    executor.setKeepAliveSeconds(60);   // 线程空闲时间
    executor.setRejectedExecutionHandler((Runnable r, ThreadPoolExecutor executor1) -> {
      throw new BusinessException("AddressTask Rejected");
    });
    executor.initialize();
    return executor;
  }
}
