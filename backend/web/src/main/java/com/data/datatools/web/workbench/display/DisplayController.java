package com.data.datatools.web.workbench.display;

import com.data.datatools.common.response.BaseResponse;
import com.data.datatools.web.workbench.display.dto.DisplayCondition;
import com.data.datatools.web.workbench.display.dto.DisplayDataCondition;
import com.data.datatools.web.workbench.display.dto.DisplayDto;
import com.data.datatools.web.workbench.display.dto.DisplayInitDto;
import com.data.datatools.web.workbench.display.dto.SubmitModelDto;
import com.data.datatools.web.workbench.display.dto.UpdateMatchDataDto;
import com.data.datatools.web.workbench.table.dto.ExportTableDataDto;
import com.data.datatools.web.workbench.table.dto.TableCtrlCondition;
import com.data.datatools.web.workbench.table.dto.TableDataCondition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class DisplayController {

  @Autowired
  private DisplayService displayService;

  @PostMapping("/workbench/select-display")
  public BaseResponse searchDisplayList(@RequestBody DisplayCondition condition) {
    return displayService.searchDisplayList(condition);
  }

  @PostMapping("/workbench/display-init")
  public BaseResponse datasetInit(@RequestBody DisplayInitDto dto) {
    return displayService.displayInit(dto);
  }

  @PostMapping("/workbench/display-save")
  public BaseResponse displaySave(@RequestBody DisplayDto dto) {
    return displayService.displaySave(dto);
  }

  @DeleteMapping("/workbench/delete-display/{displayId}")
  public BaseResponse displayDelete(@PathVariable String displayId) {
    return displayService.displayDelete(displayId);
  }

  @PostMapping("/workbench/select-display-data/{displayId}")
  public BaseResponse selectDisplayData(@PathVariable String displayId, @RequestBody DisplayDataCondition condition) {
    return displayService.selectDisplayData(displayId, condition);
  }

  @PostMapping("/workbench/display-re-analysis/{displayId}")
  public BaseResponse reAnalysis(@PathVariable String displayId) {
    return displayService.reAnalysis(displayId);
  }

  @GetMapping("/workbench/refresh-status/{displayId}")
  public BaseResponse refreshStatus(@PathVariable String displayId) {
    return displayService.refreshStatus(displayId);
  }

  @PostMapping("/workbench/export-display-data/{displayId}")
  public BaseResponse exportDisplayData(@PathVariable String displayId, @RequestBody ExportTableDataDto dto) {
    return displayService.exportDisplayData(displayId, dto);
  }

  @PostMapping("/workbench/edit-display-match-data/{displayId}")
  public BaseResponse updateMatchData(@PathVariable String displayId, @RequestBody UpdateMatchDataDto dto) {
    return displayService.updateMatchData(displayId, dto);
  }

  @PostMapping("/workbench/display-submit-model/{displayId}")
  public BaseResponse submitModel(@PathVariable String displayId, @RequestBody SubmitModelDto dto) {
    return displayService.submitModel(displayId, dto);
  }
}
