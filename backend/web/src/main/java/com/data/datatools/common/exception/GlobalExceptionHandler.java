package com.data.datatools.common.exception;

import com.data.datatools.common.interceptor.ControllerInterceptor;
import com.data.datatools.common.message.MessageConstants;
import com.data.datatools.common.response.MessageResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@ControllerAdvice
public class GlobalExceptionHandler {

  private static final Logger logger = LoggerFactory.getLogger(ControllerInterceptor.class.getName());

  @ExceptionHandler(value = Exception.class)
  @ResponseBody
  public Object defaultErrorHandler(HttpServletRequest request, HttpServletResponse response,
      Exception e) throws Exception { // NOSONAR

    if (e instanceof AccessDeniedException) {
      throw e;
    }

    logger.error(e.getMessage(), e);
    String requestURI = request.getRequestURI();

    return MessageResponse.newErrorMessage(MessageConstants.MESSAGE_E0099);
  }
}
