package com.data.datatools.web.system.user;

import com.data.datatools.common.code.OptType;
import com.data.datatools.common.code.UserType;
import com.data.datatools.common.exception.BusinessException;
import com.data.datatools.common.model.SelectOption;
import com.data.datatools.common.response.BaseResponse;
import com.data.datatools.common.response.DataResponse;
import com.data.datatools.common.response.ListResponse;
import com.data.datatools.common.response.MessageResponse;
import com.data.datatools.common.security.SecuritySupport;
import com.data.datatools.component.PulldownComponent;
import com.data.datatools.config.security.model.LoginUser;
import com.data.datatools.repository.master.entity.SysRoleEntity;
import com.data.datatools.repository.master.entity.SysSolidUserEntity;
import com.data.datatools.repository.master.entity.SysUserEntity;
import com.data.datatools.repository.master.entity.SysUserRoleEntity;
import com.data.datatools.repository.master.mapper.SysRoleMapper;
import com.data.datatools.repository.master.mapper.SysRoleNames;
import com.data.datatools.repository.master.mapper.SysSolidUserMapper;
import com.data.datatools.repository.master.mapper.SysUserMapper;
import com.data.datatools.repository.master.mapper.SysUserNames;
import com.data.datatools.repository.master.mapper.SysUserRoleMapper;
import com.data.datatools.repository.master.mapper.SysUserRoleNames;
import com.data.datatools.repository.master.mapper.ViewSysUseAuthMapper;
import com.data.datatools.web.system.user.entity.UserRoleInfoEntity;
import com.data.datatools.web.system.user.model.PasswordForm;
import com.data.datatools.web.system.user.model.UserCondition;
import com.data.datatools.web.system.user.model.UserInfoModel;

import java.time.LocalDateTime;
import java.util.*;

import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.SqlBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class UserService {

  protected final Logger logger = LoggerFactory.getLogger(getClass());

  @Autowired
  private UserMapper userMapper;

  @Autowired
  private ViewSysUseAuthMapper viewSysUseAuthMapper;

  @Autowired
  private SysUserMapper sysUserMapper;

  @Autowired
  private SysSolidUserMapper sysSolidUserMapper;

  @Autowired
  private PulldownComponent pulldownComponent;

  public void searchByPagingWithCond(UserCondition condition, ListResponse<UserInfoModel> listResponse) {
    if (SecuritySupport.isDistrict()) {
      condition.setDistrict(SecuritySupport.getDistrict());
    }
    userMapper.selectDataListWithPage(condition(condition), listResponse, e -> {
      UserInfoModel model = new UserInfoModel();
      BeanUtils.copyProperties(e, model, "town");
      model.setUserType(UserType.getTypeName(e.getUserType()));
      model.setCity("上海市");
      model.setDistrict(e.getDistrict());
      model.setStreet(e.getTown());
      model.setUserState(!StringUtils.equals(e.getUserState(), "1"));
      return model;
    });
    if (condition.isInit()) {
      listResponse.putData("userTypeOptions", pulldownComponent.useTypeOptions());
    }
  }

  private UserCondition condition(UserCondition condition) {
    UserCondition param = new UserCondition();
    BeanUtils.copyProperties(condition, param);
    return param;
  }

  /**
   * get user info by user ID（多表查询）
   *
   * @param user 用户
   * @return 用户详细信息
   */
  public BaseResponse getUser(UserCondition user) {

    DataResponse<UserInfoModel> dataMapResponse = new DataResponse<>();

    String id = user.getUserId();
    dataMapResponse.putData("roleOptions", pulldownComponent.useTypeOptions());
    dataMapResponse.putData("districtOptions", pulldownComponent.districtOptions());
    dataMapResponse.putData("townOptions", pulldownComponent.townOptions());

    if (!StringUtils.isBlank(id) && OptType.EDIT.is(user.getOptType())) {
      UserInfoModel infoModel = new UserInfoModel();
      UserRoleInfoEntity entity = userMapper.findById(id);
      if (entity != null) {
        BeanUtils.copyProperties(entity, infoModel, "town");
        if (UserType.DISTRICT.is(entity.getUserType())) {
          infoModel.setDistrict(entity.getDistrict());
          infoModel.setTown(new ArrayList<>());
        } else if (UserType.STREET.is(entity.getUserType())) {
          infoModel.setDistrict("");
          infoModel.setTown(Arrays.asList(entity.getDistrict(), entity.getTown()));
        } else {
          infoModel.setDistrict("");
          infoModel.setTown(new ArrayList<>());
        }
      } else {
        return MessageResponse.newErrorMessage("该数据不存在，请返回一览重新检索后再操作。");
      }

      return dataMapResponse.setValue(infoModel);
    }
    return dataMapResponse;
  }

  @Transactional
  public BaseResponse addUser(UserInfoModel model) {
    long count = viewSysUseAuthMapper.count(c -> c.where().and(SysUserNames.userId, SqlBuilder.isEqualTo(model.getUserId())));
    if (count > 0) {
      return MessageResponse.newErrorMessage("用户ID已经存在");
    }

    String encoderPassword = new BCryptPasswordEncoder().encode(model.getPassword());
    SysUserEntity entity = new SysUserEntity();
    entity.setPassword(encoderPassword);
    entity.setPasswordStatus("1");
    entity.setUserId(model.getUserId());
    entity.setUserName(model.getUserName());
    entity.setUserEmail(model.getUserEmail());
    entity.setLoginWrongCount(0);
    entity.setDeleted(false);
    entity.setUserType(model.getUserType());
    entity.setProv("上海");
    entity.setCity("上海市");
    if (UserType.DISTRICT.is(model.getUserType())) {
      entity.setDistrict(model.getDistrict());
    } else if (UserType.STREET.is(model.getUserType())) {
      entity.setDistrict(model.getTown().get(0));
      entity.setTown(model.getTown().get(1));
    }

    sysUserMapper.insert(entity);

    return MessageResponse.newInfoMessage("用户做成成功。");
  }

  /**
   * update user info
   *
   * @param model 更新的角色信息
   * @return 更新结果
   */
  @Transactional
  public BaseResponse updateUser(UserInfoModel model, MessageResponse message) {

    SysUserEntity entity = sysUserMapper.selectByPrimaryKey(model.getUserId())
      .orElseThrow(() -> new BusinessException("该用户不存在，请返回一览重新检索后再操作。"));

    entity.setUserName(model.getUserName());
    entity.setUserEmail(model.getUserEmail());
    if (model.isUpdatePass()) {
      String encoderPassword = new BCryptPasswordEncoder().encode(model.getPassword());
      entity.setPassword(encoderPassword);
    }
    entity.setUserType(model.getUserType());
    if (UserType.DISTRICT.is(model.getUserType())) {
      entity.setDistrict(model.getDistrict());
      entity.setTown("");
    } else if (UserType.STREET.is(model.getUserType())) {
      entity.setDistrict(model.getTown().get(0));
      entity.setTown(model.getTown().get(1));
    } else {
      entity.setDistrict("");
      entity.setTown("");
    }

    int statusUser = sysUserMapper.updateByPrimaryKey(entity);
    if (statusUser < 1) {
      return MessageResponse.newErrorMessage("保存失败，请刷新页面后重试。");
    }

    return MessageResponse.newInfoMessage("用户信息修改成功。");
  }

  @Transactional
  public void deleteUser(String userId, MessageResponse message) {
    sysUserMapper.delete(c -> c.where().and(SysUserNames.userId, SqlBuilder.isEqualTo(userId)));
  }

  @Transactional
  public void deleteUsers(List<String> users, MessageResponse msg) {

    for (String userId : users) {
      deleteUser(userId, msg);
    }
  }

  @Transactional
  public BaseResponse updatePassword(PasswordForm form) {
    LoginUser loginUser = SecuritySupport.getLoginUser();
    if (loginUser == null) {
      return MessageResponse.newErrorMessage("密码修改异常，请重新登录后再操作。");
    }

    if (!Objects.equals(form.getNewPwd(), form.getConfirmPwd())) {
      return MessageResponse.newErrorMessage("两次新密码输入不一致。");
    }
    BCryptPasswordEncoder cryptPasswordEncoder = new BCryptPasswordEncoder();
    if (loginUser.isSolidUser()) {
      SysSolidUserEntity entity = sysSolidUserMapper.selectByPrimaryKey(loginUser.getUserId()).orElseThrow(() -> new BusinessException("密码修改异常，请重新登录后再操作。"));


      if (!cryptPasswordEncoder.matches(form.getOldPwd(), entity.getPassword())) {
        return MessageResponse.newErrorMessage("原始密码输入不正确。");
      }
      entity.setPassword(cryptPasswordEncoder.encode(form.getNewPwd()));
      entity.setToken("");
      entity.setLoginWrongCount(0);
      entity.setPasswordUpdatedAt(LocalDateTime.now());
      sysSolidUserMapper.updateByPrimaryKey(entity);
    } else {

      SysUserEntity entity = sysUserMapper.selectByPrimaryKey(loginUser.getUserId())
        .orElseThrow(() -> new BusinessException("密码修改异常，请重新登录后再操作。"));
      if (!cryptPasswordEncoder.matches(form.getOldPwd(), entity.getPassword())) {
        return MessageResponse.newErrorMessage("原始密码输入不正确。");
      }
      entity.setPassword(cryptPasswordEncoder.encode(form.getNewPwd()));
      entity.setToken("");
      entity.setLoginWrongCount(0);
      entity.setPasswordUpdatedAt(LocalDateTime.now());
      sysUserMapper.updateByPrimaryKey(entity);
    }
    return MessageResponse.newInfoMessage("密码修改成功。");
  }
}
