package com.data.datatools.common.model;

import lombok.Data;

import java.util.List;
import java.util.Objects;

@Data
public class SelectOption {

  private final Object value;
  private final String label;

  private List<SelectOption> options;

  private List<SelectOption> children;

  private Object data;

  private boolean disabled;

  public SelectOption(Object value, String label) {
    this.value = value;
    this.label = label;
  }

  public SelectOption setData(Object data) {
    this.data = data;
    return this;
  }

  @Override
  public boolean equals(Object obj) {
    if (!(obj instanceof SelectOption)) {
      return false;
    }
    SelectOption other = (SelectOption) obj;
    return Objects.equals(value, other.value);
  }
}
