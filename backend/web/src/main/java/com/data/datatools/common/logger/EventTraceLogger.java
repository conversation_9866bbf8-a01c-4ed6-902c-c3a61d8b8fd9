package com.data.datatools.common.logger;

import org.slf4j.*;

public final class EventTraceLogger {

  private static final Marker MARKER = MarkerFactory.getMarker("EVENT_TRACK");

  private static final Logger LOGGER = LoggerFactory.getLogger(MARKER.toString());
  public static final String OPERATE_TYPE = "operate_type";

  private EventTraceLogger() {
  }

  public static void info(String type, String message) {
    MDC.put(OPERATE_TYPE, type);
    LOGGER.info(MARKER, message);
  }

  public static void info(String type, String message, String param) {
    MDC.put(OPERATE_TYPE, type);
    String infoMessage = message.concat(param);
    LOGGER.info(MARKER, infoMessage);
  }

  public static void error(String type, Throwable t, String message) {
    MDC.put(OPERATE_TYPE, type);
    LOGGER.error(MARKER, message, t);
  }

  public static void error(String type, String message) {
    MDC.put(OPERATE_TYPE, type);
    LOGGER.error(MARKER, message);
  }

  public static void error(String type, String message, String param) {
    MDC.put(OPERATE_TYPE, type);
    String errorMessage = message.concat(param);
    LOGGER.error(MARKER, errorMessage);
  }

  public static void initContext() {
    OperateLogContext.init();
  }

  public static void clearContext() {
    MDC.remove(OPERATE_TYPE);
    OperateLogContext.clear();
  }
}
