package com.data.datatools.common.validate;

import com.data.datatools.common.exception.BusinessException;
import com.data.datatools.common.message.MessageConstants;
import com.data.datatools.common.response.BaseResponse;
import com.data.datatools.common.response.MessageResponse;
import com.data.datatools.common.response.ValidErrorResponse;
import net.sf.json.JSONObject;
import org.springframework.context.MessageSourceResolvable;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class ValidationExceptionResolver extends AbstractHandlerExceptionResolver {

  public ValidationExceptionResolver() {
    this.setOrder(0);
  }

  @Override
  protected ModelAndView doResolveException(HttpServletRequest request,
      HttpServletResponse response, Object handler, Exception ex) {
    BindingResult bindingResult = null;
    if (ex instanceof MethodArgumentNotValidException) {
      bindingResult = ((MethodArgumentNotValidException) ex).getBindingResult();
    } else if (ex instanceof BindException) {
      bindingResult = ((BindException) ex).getBindingResult();
    }
    if (bindingResult != null) {
      try {
        return handleMethodArgumentNotValidException(bindingResult, response);
      } catch (IOException e) {
        throw new BusinessException(e, "spring validate fail.");
      }
    }
    return null;
  }

  private ModelAndView handleMethodArgumentNotValidException(BindingResult bindingResult,
      HttpServletResponse response) throws IOException {

    ModelAndView modelAndView = new ModelAndView();

    List<ObjectError> errors = bindingResult.getAllErrors();
    ValidErrorResponse messageResponse = ValidErrorResponse.newInstance();
    boolean bindingFailure = false;
    for (ObjectError error : errors) {
      String massage = error.getDefaultMessage();
      if (error instanceof FieldError) {
        bindingFailure = ((FieldError) error).isBindingFailure();
        if (bindingFailure) {
          logger.error(massage);
          break;
        }
      }
      Object[] args = error.getArguments();
      List<String> paramList = Arrays.stream(args != null ? args : new Object[0]).map(o -> {
        if (o instanceof MessageSourceResolvable) {
          return ((MessageSourceResolvable) o).getDefaultMessage();
        }
        return o.toString();
      }).collect(Collectors.toList());
      messageResponse.addMessage(massage, paramList.subList(1, paramList.size()));
    }
    if (bindingFailure) {
      jsonResponse(response, MessageResponse.newErrorMessage(MessageConstants.MESSAGE_E0099));
    } else {
      jsonResponse(response, messageResponse);
    }
    return modelAndView;
  }

  private void jsonResponse(HttpServletResponse response, BaseResponse data) throws IOException {

    response.setCharacterEncoding("UTF-8");
    response.setContentType("application/json; charset=utf-8");
    response.getWriter().write(JSONObject.fromObject(data).toString());
    response.getWriter().flush();
  }
}
