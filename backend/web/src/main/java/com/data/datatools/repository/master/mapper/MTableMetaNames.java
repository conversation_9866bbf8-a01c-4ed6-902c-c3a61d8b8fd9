package com.data.datatools.repository.master.mapper;

import java.sql.JDBCType;
import java.time.LocalDateTime;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class MTableMetaNames {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_table_meta")
    public static final MTableMeta MTableMeta = new MTableMeta();

    /**
     * 主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.table_id")
    public static final SqlColumn<String> tableId = MTableMeta.tableId;

    /**
     * 表名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.table_name")
    public static final SqlColumn<String> tableName = MTableMeta.tableName;

    /**
     * 所属项目ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.project_id")
    public static final SqlColumn<String> projectId = MTableMeta.projectId;

    /**
     * 地区匹配状态 0:完成 1:进行中 2:失败
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.match_status")
    public static final SqlColumn<String> matchStatus = MTableMeta.matchStatus;

    /**
     * 描述
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.remark")
    public static final SqlColumn<String> remark = MTableMeta.remark;

    /**
     * 是否删除
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.deleted")
    public static final SqlColumn<Boolean> deleted = MTableMeta.deleted;

    /**
     * 插入时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.created_at")
    public static final SqlColumn<LocalDateTime> createdAt = MTableMeta.createdAt;

    /**
     * 做成者
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.created_by")
    public static final SqlColumn<String> createdBy = MTableMeta.createdBy;

    /**
     * 更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.updated_at")
    public static final SqlColumn<LocalDateTime> updatedAt = MTableMeta.updatedAt;

    /**
     * 更新者
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.updated_by")
    public static final SqlColumn<String> updatedBy = MTableMeta.updatedBy;

    /**
     * 数据表Meta
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_table_meta.table_meta")
    public static final SqlColumn<String> tableMeta = MTableMeta.tableMeta;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_table_meta")
    public static final class MTableMeta extends SqlTable {
        public final SqlColumn<String> tableId = column("table_id", JDBCType.CHAR);

        public final SqlColumn<String> tableName = column("table_name", JDBCType.VARCHAR);

        public final SqlColumn<String> projectId = column("project_id", JDBCType.CHAR);

        public final SqlColumn<String> matchStatus = column("match_status", JDBCType.CHAR);

        public final SqlColumn<String> remark = column("remark", JDBCType.VARCHAR);

        public final SqlColumn<Boolean> deleted = column("deleted", JDBCType.BIT);

        public final SqlColumn<LocalDateTime> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createdBy = column("created_by", JDBCType.VARCHAR);

        public final SqlColumn<LocalDateTime> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updatedBy = column("updated_by", JDBCType.VARCHAR);

        public final SqlColumn<String> tableMeta = column("table_meta", JDBCType.LONGVARCHAR);

        public MTableMeta() {
            super("m_table_meta");
        }
    }
}