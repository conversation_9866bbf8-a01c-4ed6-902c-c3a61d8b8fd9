package com.data.datatools.web.workbench.address.parser;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 高精度的中文地址解析器，将非结构化地址文本解析为精确到门牌号或弄的地址列表
 */
public class AddressParser {
    
    private static final Logger logger = LoggerFactory.getLogger(AddressParser.class);
    
    // 噪音过滤正则表达式
    private static final Pattern NOISE_PARENTHESES_RE = Pattern.compile("[（\\(][^（）()双单]+[）\\)]");
    private static final String[] NOISE_KEYWORDS = {
        "门卫", "物业用房", "给水泵房", "活动室", "垃圾房", "变电站",
        "居委会", "警卫室", "等"
    };
    private static final Pattern NOISE_KEYWORDS_RE = Pattern.compile(
        Arrays.stream(NOISE_KEYWORDS).map(Pattern::quote).collect(Collectors.joining("|"))
    );
    private static final Pattern NOISE_SUFFIX_RE = Pattern.compile("等\\d+处\\s*$");
    
    // 街道分割正则表达式
    private static final Pattern STREET_SPLIT_RE = Pattern.compile("([\\u4e00-\\u9fa5]+(?:路|街|公路|大道|镇|乡|村))");

    // 地址模式正则表达式 - 简化版本
    private static final Pattern PATTERN_A_RE = Pattern.compile("(\\d+)\\s*弄\\s*([\\d\\-、，,\\s]+(?:[（\\(](?:双|单)[）\\)])?)\\s*号?");
    private static final Pattern PATTERN_C_RE = Pattern.compile("([\\d\\-、，,\\s]+(?:[（\\(](?:双|单)[）\\)])?)\\s*弄\\s*$");
    private static final Pattern PATTERN_B_RE = Pattern.compile("([\\d\\-、，,\\s]+(?:[（\\(](?:双|单)[）\\)])?)\\s*号?\\s*$");
    
    // 实例变量
    private List<String> results;
    private String currentStreet;
    private String currentLane;
    
    public AddressParser() {
        this.results = new ArrayList<>();
        this.currentStreet = "";
        this.currentLane = "";
    }
    
    /**
     * 应用语义过滤原则，清除噪音
     */
    private String preprocess(String text) {
        if (text == null || text.trim().isEmpty()) {
            return "";
        }
        
        // 移除噪音括号内容
        text = NOISE_PARENTHESES_RE.matcher(text).replaceAll("");
        
        // 移除噪音关键词
        text = NOISE_KEYWORDS_RE.matcher(text).replaceAll("");
        
        // 移除噪音后缀
        text = NOISE_SUFFIX_RE.matcher(text).replaceAll("");
        
        return text.trim();
    }
    
    /**
     * 数字与范围扩展引擎
     */
    private List<Integer> expandNumbers(String numStr) {
        Set<Integer> numbers = new HashSet<>();
        String[] parts = numStr.split("[、，,]");
        
        for (String part : parts) {
            part = part.trim();
            if (part.isEmpty()) continue;
            
            String mode = "all";
            if (part.contains("（双）") || part.contains("(双)")) {
                mode = "even";
                part = part.replace("（双）", "").replace("(双)", "");
            } else if (part.contains("（单）") || part.contains("(单)")) {
                mode = "odd";
                part = part.replace("（单）", "").replace("(单)", "");
            }
            
            part = part.trim();
            
            if (part.contains("-")) {
                String[] range = part.split("-");
                if (range.length == 2) {
                    try {
                        int start = Integer.parseInt(range[0].trim());
                        int end = Integer.parseInt(range[1].trim());
                        
                        for (int i = start; i <= end; i++) {
                            if (("even".equals(mode) && i % 2 != 0) ||
                                ("odd".equals(mode) && i % 2 == 0)) {
                                continue;
                            }
                            numbers.add(i);
                        }
                    } catch (NumberFormatException e) {
                        logger.debug("无法解析数字范围: {}", part);
                    }
                }
            } else {
                try {
                    int num = Integer.parseInt(part);
                    if (("even".equals(mode) && num % 2 != 0) ||
                        ("odd".equals(mode) && num % 2 == 0)) {
                        continue;
                    }
                    numbers.add(num);
                } catch (NumberFormatException e) {
                    logger.debug("无法解析数字: {}", part);
                }
            }
        }
        
        return numbers.stream().sorted().collect(Collectors.toList());
    }
    
    /**
     * 主解析函数，执行确定性流程
     */
    public List<String> parse(String text) {
        if (text == null || text.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        this.results = new ArrayList<>();
        
        // 使用街道分割正则表达式分割文本
        String[] blocks = STREET_SPLIT_RE.split(text);
        if (blocks.length < 2) {
            return new ArrayList<>();
        }

        Matcher streetMatcher = STREET_SPLIT_RE.matcher(text);
        List<String> streets = new ArrayList<>();
        while (streetMatcher.find()) {
            streets.add(streetMatcher.group(1)); // 使用数字索引而不是命名组
        }
        
        int streetIndex = 0;
        for (int i = 1; i < blocks.length && streetIndex < streets.size(); i++) {
            this.currentStreet = streets.get(streetIndex++).trim();
            String addressPart = blocks[i].trim();
            
            // 关键修复：清理从街道分割中产生的地址部分首尾可能残留的分隔符
            addressPart = addressPart.replaceAll("^[、，；,;]+|[、，；,;]+$", "");
            
            // 在分割单元前，对顿号'、'进行规整化处理
            addressPart = addressPart.replaceAll("(?<=\\d弄)\\s*、\\s*(?=\\d+弄)", ",");
            
            // 使用逗号和分号作为主要分隔符
            String[] units = addressPart.split("[，；,]");
            
            this.currentLane = "";
            for (String unit : units) {
                unit = preprocess(unit);
                if (unit.isEmpty()) continue;
                
                // 模式A：弄+号
                Matcher matcherA = PATTERN_A_RE.matcher(unit);
                if (matcherA.matches()) {
                    String laneNum = matcherA.group(1); // 第一个捕获组
                    this.currentLane = laneNum + "弄";
                    String houseNumStr = matcherA.group(2); // 第二个捕获组
                    List<Integer> houseNumbers = expandNumbers(houseNumStr);
                    for (Integer num : houseNumbers) {
                        this.results.add(this.currentStreet + this.currentLane + num + "号");
                    }
                    continue;
                }

                // 模式C：只有弄
                Matcher matcherC = PATTERN_C_RE.matcher(unit);
                if (matcherC.matches()) {
                    String laneNumStr = matcherC.group(1); // 第一个捕获组
                    List<Integer> laneNumbers = expandNumbers(laneNumStr);
                    for (Integer num : laneNumbers) {
                        this.results.add(this.currentStreet + num + "弄");
                    }
                    if (laneNumbers.size() == 1) {
                        this.currentLane = laneNumbers.get(0) + "弄";
                    } else {
                        this.currentLane = "";
                    }
                    continue;
                }

                // 模式B：只有号
                Matcher matcherB = PATTERN_B_RE.matcher(unit);
                if (matcherB.matches()) {
                    String houseNumStr = matcherB.group(1); // 第一个捕获组
                    List<Integer> houseNumbers = expandNumbers(houseNumStr);
                    for (Integer num : houseNumbers) {
                        this.results.add(this.currentStreet + this.currentLane + num + "号");
                    }
                }
            }
        }
        
        // 返回保留顺序并去重后的结果
        return this.results.stream()
            .distinct()
            .collect(Collectors.toCollection(LinkedHashSet::new))
            .stream()
            .collect(Collectors.toList());
    }
}
