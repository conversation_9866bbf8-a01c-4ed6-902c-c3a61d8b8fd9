package com.data.datatools.config;

import com.data.datatools.common.converter.LocalDateConverter;
import com.data.datatools.common.converter.LocalDateTimeConverter;
import com.data.datatools.common.interceptor.ControllerInterceptor;
import com.data.datatools.common.validate.ValidationExceptionResolver;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import javax.net.ssl.SSLContext;
import javax.validation.metadata.ConstraintDescriptor;
import org.apache.http.client.HttpClient;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContexts;
import org.hibernate.validator.HibernateValidator;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.format.FormatterRegistry;
import org.springframework.http.client.BufferingClientHttpRequestFactory;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.validation.Validator;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;
import org.springframework.web.client.DefaultResponseErrorHandler;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.HandlerExceptionResolver;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

  @Autowired
  private ControllerInterceptor accessLogInterceptor;

  @Override
  public void addInterceptors(InterceptorRegistry registry) {
    registry.addInterceptor(accessLogInterceptor) //
      .addPathPatterns("/**") //
      .excludePathPatterns("/", //
        "/health/check"); //
  }

  @Override
  public void addResourceHandlers(ResourceHandlerRegistry registry) {
    registry.addResourceHandler("/index.html").addResourceLocations("classpath:/META-INF/resources/index.html");
    registry.addResourceHandler("/static/**").addResourceLocations("classpath:/static/", "classpath:/META-INF/resources/static/");
  }

  @Override
  public void addCorsMappings(CorsRegistry registry) {
    registry.addMapping("/**").allowedOrigins("*")
      .allowedMethods("POST", "GET", "PUT", "OPTIONS", "DELETE").maxAge(3600)
      .allowCredentials(true);
  }

  @Override
  @Bean
  @Primary
  public Validator getValidator() {
    ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();
    messageSource.setBasenames("i18n/validation_error");

    LocalValidatorFactoryBean bean = new LocalValidatorFactoryBean() {

      private final List<String> internalAnnotationAttributes = Arrays.asList("message", "groups", "payload");

      private static final String MESSAGE_ARGS = "args";

      /**
       * {@inheritDoc}
       */
      @Override
      protected Object[] getArgumentsForConstraint(String objectName, String field, ConstraintDescriptor<?> descriptor) {

        List<Object> arguments = new LinkedList<>();
        arguments.add(getResolvableField(objectName, field));
        // Using a TreeMap for alphabetical ordering of attribute names
        Map<String, Object> attributesToExpose = new TreeMap<>();
        for (Map.Entry<String, Object> entry : descriptor.getAttributes().entrySet()) {
          String attributeName = entry.getKey();
          Object attributeValue = entry.getValue();
          if (!internalAnnotationAttributes.contains(attributeName)) {
            if (MESSAGE_ARGS.equals(attributeName)) {
              arguments.add(attributeValue);
            }
            attributesToExpose.put(attributeName, attributeValue);
          }
        }
        arguments.addAll(attributesToExpose.values());
        return arguments.toArray(new Object[0]);
      }
    };
    bean.setProviderClass(HibernateValidator.class);
    bean.setValidationMessageSource(messageSource);
    return bean;
  }

  @Bean
  public HandlerExceptionResolver validationExceptionResolver() {
    return new ValidationExceptionResolver();
  }

  @Override
  public void addFormatters(FormatterRegistry registry) {
    registry.addConverter(new LocalDateTimeConverter());
    registry.addConverter(new LocalDateConverter());
//    registry.addConverter(new OrderItemConverter());
//    registry.addConverter(new OprTypeConverter());
  }

  @Bean
  @Primary
  public RestTemplate getRestClient() throws KeyStoreException, NoSuchAlgorithmException, KeyManagementException {
    SSLContext sslContext = SSLContexts.custom().loadTrustMaterial((TrustStrategy) (chain, authType) -> true).build();
    HttpClient httpClient = HttpClients.custom().setSSLContext(sslContext).setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE).build();

    HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory(httpClient);

    requestFactory.setConnectTimeout(30000);
    requestFactory.setReadTimeout(30000);

    RestTemplate restTemplate = //
      new RestTemplate(new BufferingClientHttpRequestFactory(requestFactory));

    restTemplate.getMessageConverters().set(1, new StringHttpMessageConverter(StandardCharsets.UTF_8));
    restTemplate.setErrorHandler(new DefaultResponseErrorHandler() {
      @Override
      public boolean hasError(@NotNull ClientHttpResponse response) throws IOException {
        return response.getStatusCode().is5xxServerError();
      }
    });

    return restTemplate;
  }
}
