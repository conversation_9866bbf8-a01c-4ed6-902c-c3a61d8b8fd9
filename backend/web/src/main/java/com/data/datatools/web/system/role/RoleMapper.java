package com.data.datatools.web.system.role;

import com.data.datatools.common.base.dao.IBasePagingMapper;
import com.data.datatools.common.response.ListResponse;
import com.data.datatools.web.system.role.dto.RoleCondition;
import com.data.datatools.web.system.role.dto.RoleInfoModel;
import com.data.datatools.web.system.role.entity.RoleAuthInfoEntity;
import com.data.datatools.web.system.role.entity.RoleInfoEntity;
import java.util.List;
import java.util.function.Function;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface RoleMapper extends IBasePagingMapper {

  default List<RoleInfoModel> selectListWithPage(RoleCondition condition, ListResponse<RoleInfoModel> response,
    Function<RoleInfoEntity, RoleInfoModel> convert) {
    return selectWithPage(condition, response, convert, this::selectList);
  }

  List<RoleInfoEntity> selectList(@Param(value = "param") RoleCondition condition);

  RoleInfoEntity findById(String id);

  List<RoleAuthInfoEntity> findAuthsByRoleId(String roleId);

  Integer findMaxShowSequence();
}
