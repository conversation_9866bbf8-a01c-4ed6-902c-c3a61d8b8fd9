package com.data.datatools.repository.slave.mapper;

import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.data.datatools.common.base.dao.IBasePagingMapper;
import com.data.datatools.common.mybatis.Operator;
import com.data.datatools.common.response.ListResponse;
import com.data.datatools.repository.slave.entity.SlaveDataEntity;
import com.data.datatools.repository.slave.entity.SlaveDuplicationDataEntity;
import com.data.datatools.repository.slave.entity.SlaveTableEntity;
import com.data.datatools.repository.slave.mapper.SlaveTableNames.SlaveTable;
import com.data.datatools.web.workbench.display.entity.DisplaySelectParam;
import com.data.datatools.web.workbench.table.dto.DuplicationCheckCondition;
import com.data.datatools.web.workbench.table.entity.TableDataSelectParam;
import com.data.datatools.web.workbench.table.entity.TableDataUpdateParam;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.SqlBuilder;
import org.mybatis.dynamic.sql.insert.render.MultiRowInsertStatementProvider;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface SlaveControlTableMapper extends IBasePagingMapper {

  @Select("select name from test_table limit 1")
  String getTestTableName();

  @Update("update test_table set name = #{userName} where id = 1")
  int updateTableName(String userName);

  @Update(
    "CREATE TABLE `${tableId}`(`id` int NOT NULL AUTO_INCREMENT,`md5` varchar(32) NOT NULL,`area_id` varchar(32) NOT NULL,"
      + "`import_key` char(5) NOT NULL,`content` json,`deleted` tinyint(1) NOT NULL DEFAULT 0,`created_by` varchar(32) NOT NULL,"
      + "`updated_by` varchar(32) NOT NULL,`updated_time` timestamp NOT NULL,PRIMARY KEY (`id`),index(`md5`))"
      + "ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Compact;")
  int createTable(String tableId);

  @Update("drop table `${tableId}`")
  int deleteTable(String tableId);

  @Update("truncate table `${tableId}`")
  void truncateTable(String tableId);

  default int insertMultiple(String tableId, Collection<SlaveTableEntity> records) {

    SlaveTable slaveTable = new SlaveTable(tableId);
    return MyBatis3Utils.insertMultiple(this::insertMultiple, records, slaveTable, c ->
      c.map(slaveTable.id).toProperty("id")
        .map(slaveTable.md5).toProperty("md5")
        .map(slaveTable.importKey).toProperty("importKey")
        .map(slaveTable.areaId).toProperty("areaId")
        .map(slaveTable.content).toProperty("content")
        .map(slaveTable.deleted).toProperty("deleted")
        .map(slaveTable.createdBy).toProperty("createdBy")
        .map(slaveTable.updatedBy).toProperty("updatedBy")
        .map(slaveTable.updatedTime).toProperty("updatedTime")
    );
  }

  @InsertProvider(type = SqlProviderAdapter.class, method = "insertMultiple")
  int insertMultiple(MultiRowInsertStatementProvider<SlaveTableEntity> multipleInsertStatement);

  default List<Map<String, Object>> selectDataListWithPage(TableDataSelectParam param,
    ListResponse<Map<String, Object>> response, Function<SlaveDataEntity, Map<String, Object>> convert) {
    return selectWithPage(param, response, convert, this::selectDataList);
  }

  List<SlaveDataEntity> selectDataList(@Param("param") TableDataSelectParam param);

  int deleteTableData(String tableId, String[] dataIds);

  int deleteAlwaysTableData(String tableId, String[] dataIds);

  default Optional<SlaveTableEntity> selectOne(String tableId, Integer rowId) {
    SlaveTable slaveTable = new SlaveTable(tableId);

    BasicColumn[] selectList = BasicColumn.columnList(slaveTable.id, slaveTable.md5, slaveTable.importKey,
      slaveTable.content, slaveTable.deleted, slaveTable.createdBy, slaveTable.updatedBy, slaveTable.updatedTime);

    return MyBatis3Utils.selectOne(this::selectOne, selectList, slaveTable, c -> c.where().and(slaveTable.id,
      SqlBuilder.isEqualTo(rowId)).and(slaveTable.deleted, SqlBuilder.isEqualTo(Boolean.FALSE)));
  }

  @SelectProvider(type = SqlProviderAdapter.class, method = "select")
  @Results(id = "SlaveDataEntityResult", value = {
    @Result(column = "id", property = "id", jdbcType = JdbcType.INTEGER, id = true),
    @Result(column = "md5", property = "md5", jdbcType = JdbcType.VARCHAR),
    @Result(column = "content", property = "content", jdbcType = JdbcType.VARCHAR)
  })
  Optional<SlaveTableEntity> selectOne(SelectStatementProvider selectStatement);

  default int updateByPrimaryKey(String tableId, SlaveTableEntity record) {
    SlaveTable slaveTable = new SlaveTable(tableId);
    return update(slaveTable, c ->
      c.set(slaveTable.md5).equalTo(record::getMd5)
        .set(slaveTable.content).equalTo(record::getContent)
        .set(slaveTable.updatedTime).equalTo(LocalDateTime.now())
        .set(slaveTable.updatedBy).equalTo(Operator.of())
        .where(slaveTable.id, isEqualTo(record::getId))
    );
  }

  default int update(SlaveTable slaveTable, UpdateDSLCompleter completer) {
    return MyBatis3Utils.update(this::update, slaveTable, completer);
  }

  @UpdateProvider(type = SqlProviderAdapter.class, method = "update")
  int update(UpdateStatementProvider updateStatement);

  int batchUpdate(@Param("param") TableDataUpdateParam param);


  @Select("${sql}")
  List<Map<String, Object>> execSelectSql(String sql);

  default List<Map<String, Object>> selectDuplicationDataWithPage(DuplicationCheckCondition condition,
    ListResponse<Map<String, Object>> response, Function<SlaveDuplicationDataEntity, Map<String, Object>> convert) {

    return selectWithPage(condition, response, convert, this::selectDuplicationDataList);
  }

  List<SlaveDuplicationDataEntity> selectDuplicationDataList(@Param("param") DuplicationCheckCondition param);

  int deleteDuplicationData(@Param("param") DuplicationCheckCondition param);
}
