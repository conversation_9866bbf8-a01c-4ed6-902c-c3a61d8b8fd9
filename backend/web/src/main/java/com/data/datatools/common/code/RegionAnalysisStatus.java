package com.data.datatools.common.code;

import java.util.Objects;

public enum RegionAnalysisStatus {

  PROCESS_ING("0", "区域分析中"),

  COMPLETE("1", "区域分析完成"),

  FAIL("2", "区域分析失败");

  private final String status;

  private final String name;

  RegionAnalysisStatus(String status, String name) {
    this.status = status;
    this.name = name;
  }

  public String status() {
    return this.status;
  }

  public static String status(String status) {
    for (RegionAnalysisStatus value : RegionAnalysisStatus.values()) {
      if (Objects.equals(value.status, status)) {
        return value.status;
      }
    }
    return "";
  }

  public static String statusName(String status) {
    for (RegionAnalysisStatus value : RegionAnalysisStatus.values()) {
      if (Objects.equals(value.status, status)) {
        return value.name;
      }
    }
    return "-";
  }

  public boolean is(String datasetStatus) {
    return Objects.equals(this.status, datasetStatus);
  }
}
