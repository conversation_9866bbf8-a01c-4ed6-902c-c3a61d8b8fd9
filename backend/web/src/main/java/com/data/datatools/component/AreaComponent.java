package com.data.datatools.component;

import com.alibaba.fastjson.JSON;
import com.data.datatools.common.code.MatchStatus;
import com.data.datatools.common.mybatis.Operator;
import com.data.datatools.component.dto.area.AreaRequest;
import com.data.datatools.component.dto.area.AreaResponse;
import com.data.datatools.repository.master.entity.MAreaEntity;
import com.data.datatools.repository.master.mapper.MAreaMapper;
import com.data.datatools.repository.master.mapper.MAreaNames;
import com.data.datatools.repository.master.mapper.MTableMetaMapper;
import com.data.datatools.repository.master.mapper.MTableMetaNames;
import com.data.datatools.utils.DateUtils;
import com.data.datatools.utils.StringUtils;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.dynamic.sql.SqlBuilder;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Component
public class AreaComponent {

  @Autowired
  private MAreaMapper mAreaMapper;

  @Autowired
  private RestTemplate restTemplate;

  @Autowired
  private MTableMetaMapper mTableMetaMapper;

  @Value("${data-analytics.area.url}")
  private String areaAnalyticsUrl;

  private static final int POOL_SIZE = 3;

  @Transactional(propagation = Propagation.NOT_SUPPORTED)
  public void analytics(String tableId, Map<String, String> areaMap) {

    if (areaMap.isEmpty()) {
      return;
    }

    SelectStatementProvider selectStatement = SqlBuilder.select(MAreaNames.areaId)
      .from(MAreaNames.MArea)
      .where().and(MAreaNames.areaId, SqlBuilder.isIn(areaMap.keySet()))
      .build().render(RenderingStrategies.MYBATIS3);
    List<MAreaEntity> entityList = mAreaMapper.selectMany(selectStatement);

    Map<String, MAreaEntity> areaEntityMap = entityList.stream()
      .collect(Collectors.toMap(MAreaEntity::getAreaId, e -> e));

    Map<String, String> processMap = new HashMap<>();
    areaMap.forEach((md5, area) -> {
      if (!areaEntityMap.containsKey(md5)) {
        processMap.put(md5, area);
      }
    });

    if (processMap.isEmpty()) {
      return;
    }

    UpdateStatementProvider updateProcessStatementProvider = SqlBuilder
      .update(MTableMetaNames.MTableMeta)
      .set(MTableMetaNames.matchStatus).equalTo(MatchStatus.PROCESS_ING.status())
      .where()
      .and(MTableMetaNames.tableId, SqlBuilder.isEqualTo(tableId))
      .build().render(RenderingStrategies.MYBATIS3);
    mTableMetaMapper.update(updateProcessStatementProvider);

    long start = System.nanoTime();
    if (processMap.size() > 10) {
      try {
        AtomicBoolean hasError = new AtomicBoolean(false);
        ExecutorService executorService = new ThreadPoolExecutor(POOL_SIZE, POOL_SIZE, 0L, TimeUnit.MILLISECONDS,
          new LinkedBlockingQueue<>());
        Operator operator = Operator.copy();

        List<Callable<Boolean>> process = processMap.entrySet().stream()
          .map(e -> (Callable<Boolean>) () -> {
            operator.reset();

            String md5 = e.getKey();
            String area = e.getValue();
            MAreaEntity entity = createEntity(md5, area);
            if (entity != null) {
              entity.setAreaId(md5);
              entity.setAddress(area);
              mAreaMapper.insert(entity);
            } else {
              hasError.set(true);
            }
            return true;
          }).collect(Collectors.toList());
        executorService.invokeAll(process);

        executorService.shutdown();
        log.info("Region parsing in imported data completed 1: size:{}, time {}", processMap.size(),
          DateUtils.formattingElapsedTime(System.nanoTime() - start));
      } catch (InterruptedException e) {
        Thread.currentThread().interrupt();
        throw new RuntimeException(e);
      } finally {
        UpdateStatementProvider updateCompleteStatementProvider = SqlBuilder
          .update(MTableMetaNames.MTableMeta)
          .set(MTableMetaNames.matchStatus).equalTo(MatchStatus.COMPLETE.status())
          .where()
          .and(MTableMetaNames.tableId, SqlBuilder.isEqualTo(tableId))
          .build().render(RenderingStrategies.MYBATIS3);
        mTableMetaMapper.update(updateCompleteStatementProvider);
      }
    } else {
      AtomicBoolean hasError = new AtomicBoolean(false);
      processMap.forEach((md5, area) -> {
        MAreaEntity entity = createEntity(md5, area);
        if (entity != null) {
          entity.setAreaId(md5);
          entity.setAddress(area);
          mAreaMapper.insert(entity);
        } else {
          hasError.set(true);
        }
      });
      log.info("Region parsing in imported data completed 2: size:{}, time {}", processMap.size(),
        DateUtils.formattingElapsedTime(System.nanoTime() - start));
      UpdateStatementProvider updateCompleteStatementProvider = SqlBuilder
        .update(MTableMetaNames.MTableMeta)
        .set(MTableMetaNames.matchStatus).equalTo(hasError.get() ? MatchStatus.COMPLETE_FAIL.status() : MatchStatus.COMPLETE.status())
        .where()
        .and(MTableMetaNames.tableId, SqlBuilder.isEqualTo(tableId))
        .build().render(RenderingStrategies.MYBATIS3);
      mTableMetaMapper.update(updateCompleteStatementProvider);
    }
  }

  private MAreaEntity createEntity(String md5, String area) {
    if (StringUtils.isNotEmpty(area)) {
      AreaRequest request = new AreaRequest(area);
      try {
        AreaResponse areaResponse = restTemplate.postForObject(areaAnalyticsUrl, request, AreaResponse.class);
        if (areaResponse != null && areaResponse.getCode() == 200) {
          return areaResponse.getData();
        } else {
          log.warn("area analytics rest error. [{}]", areaResponse == null ? "null" : JSON.toJSONString(areaResponse));
          return null;
        }
      } catch (Exception e) {
        log.error(e.getMessage(), e);
      }
    }
    return null;
  }

  public void updateArea(String addressId, String address, String[] area, String road) {
    Optional<MAreaEntity> optEntity = mAreaMapper.selectByPrimaryKey(addressId);
    if (optEntity.isPresent()) {
      MAreaEntity entity = optEntity.get();
      if (area.length > 0) {
        entity.setDistrict(area[0]);
      }
      if (area.length > 1) {
        entity.setTown(area[1]);
      }
      entity.setRoad(road);
      mAreaMapper.updateByPrimaryKey(entity);
    } else {
      MAreaEntity entity = new MAreaEntity();
      entity.setAreaId(addressId);
      entity.setAddress(address);
      entity.setProv("上海");
      entity.setCity("上海市");
      if (area.length > 0) {
        entity.setDistrict(area[0]);
      }
      if (area.length > 1) {
        entity.setTown(area[1]);
      }
      entity.setRoad(road);
      mAreaMapper.insert(entity);
    }
  }
}
