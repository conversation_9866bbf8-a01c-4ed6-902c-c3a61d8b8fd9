package com.data.datatools.web.project;

import com.data.datatools.common.code.OptType;
import com.data.datatools.common.exception.BusinessException;
import com.data.datatools.common.response.BaseResponse;
import com.data.datatools.common.response.DataResponse;
import com.data.datatools.common.response.MessageResponse;
import com.data.datatools.common.security.SecuritySupport;
import com.data.datatools.repository.master.entity.SysSolidUserEntity;
import com.data.datatools.repository.master.entity.SysUserEntity;
import com.data.datatools.repository.master.entity.MProjectEntity;
import com.data.datatools.repository.master.mapper.SysSolidUserMapper;
import com.data.datatools.repository.master.mapper.SysUserMapper;
import com.data.datatools.repository.master.mapper.MProjectMapper;
import com.data.datatools.repository.master.mapper.MProjectNames;
import com.data.datatools.utils.DateUtils;
import com.data.datatools.utils.SqlUtils;
import com.data.datatools.web.project.dto.ProjectModal;
import java.util.List;
import java.util.stream.Collectors;
import org.mybatis.dynamic.sql.SqlBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class ProjectService {

  @Autowired
  private MProjectMapper tProjectMapper;

  @Autowired
  private SysSolidUserMapper sysSolidUserMapper;

  @Autowired
  private SysUserMapper sysUserMapper;

  public BaseResponse selectProject() {
    DataResponse<List<ProjectModal>> response = new DataResponse<>();

    List<MProjectEntity> entities = tProjectMapper.select(c -> c.where()
      .and(MProjectNames.deleted, SqlBuilder.isEqualTo(false)).orderBy(MProjectNames.createdAt.descending()));

    return response.setValue(entities.stream().map(e -> {
      ProjectModal modal = new ProjectModal();
      modal.setProjectId(e.getProjectId());
      modal.setProjectName(e.getProjectName());
      modal.setRemark(e.getRemark());
      modal.setCreateDateTime(DateUtils.formatLocalDateTime(e.getCreatedAt()));
      return modal;
    }).collect(Collectors.toList()));
  }

  @Transactional
  public BaseResponse createProject(ProjectModal dto) {
    DataResponse<ProjectModal> response = new DataResponse<>();

    long count;
    if (OptType.ADD.is(dto.getOpt())) {
      count = tProjectMapper.count(c -> c.where()
        .and(MProjectNames.projectName, SqlBuilder.isEqualTo(dto.getProjectName()))
        .and(MProjectNames.deleted, SqlBuilder.isEqualTo(false)));
    } else {
      count = tProjectMapper
        .count(c -> c.where().and(MProjectNames.projectName, SqlBuilder.isEqualTo(dto.getProjectName()))
          .and(MProjectNames.projectId, SqlBuilder.isNotEqualTo(dto.getProjectId()))
          .and(MProjectNames.deleted, SqlBuilder.isEqualTo(false)));
    }
    if (count > 0) {
      return MessageResponse.newErrorMessage("项目名已经存在，请重新输入。");
    }

    MProjectEntity entity;
    if (OptType.ADD.is(dto.getOpt())) {
      entity = new MProjectEntity();
      String projectId = SqlUtils.generateProjectId();
      entity.setProjectId(projectId);
      dto.setProjectId(projectId);
    } else {
      entity = tProjectMapper.selectByPrimaryKey(dto.getProjectId())
        .orElseThrow(() -> new BusinessException("该项目已经不存在，请刷新页面后再操作。"));
    }

    entity.setProjectName(dto.getProjectName());
    entity.setRemark(dto.getRemark());
    entity.setDeleted(false);

    if (OptType.ADD.is(dto.getOpt())) {
      tProjectMapper.insert(entity);
    } else {
      tProjectMapper.updateByPrimaryKey(entity);
    }

    return response.setValue(dto);
  }

  @Transactional
  public BaseResponse deleteProject(String projectId) {

    MProjectEntity entity = tProjectMapper.selectByPrimaryKey(projectId)
      .orElseThrow(() -> new BusinessException("该项目已经不存在，请刷新页面后再操作。"));
    entity.setDeleted(true);
    tProjectMapper.updateByPrimaryKey(entity);
    // TODO

    return MessageResponse.newInfoMessage("项目及项目所属的数据表等全部删除成功。");
  }

  @Transactional
  public BaseResponse switchProject(String projectId) {

    MProjectEntity entity = tProjectMapper
      .selectOne(c -> c.where().and(MProjectNames.projectId, SqlBuilder.isEqualTo(projectId))
        .and(MProjectNames.deleted, SqlBuilder.isEqualTo(false)))
      .orElseThrow(() -> new BusinessException("该项目已经不存在，请刷新页面后再操作。"));

    if (SecuritySupport.isAdmin()) {
      SysSolidUserEntity userEntity = sysSolidUserMapper
        .selectByPrimaryKey(SecuritySupport.getLoginUserId()).orElseThrow(RuntimeException::new);
      userEntity.setCurrentProject(projectId);
      sysSolidUserMapper.updateByPrimaryKey(userEntity);
    } else {
      SysUserEntity userEntity = sysUserMapper.selectByPrimaryKey(SecuritySupport.getLoginUserId())
        .orElseThrow(RuntimeException::new);
      userEntity.setCurrentProject(projectId);
      sysUserMapper.updateByPrimaryKey(userEntity);
    }

    ProjectModal modal = new ProjectModal();
    modal.setProjectId(projectId);
    modal.setProjectName(entity.getProjectName());
    modal.setRemark(entity.getRemark());

    SecuritySupport.switchProject(projectId);

    return new DataResponse<>().setValue(modal);
  }
}
