package com.data.datatools.repository.master.mapper;

import static com.data.datatools.repository.master.mapper.ViewSysUseAuthNames.*;

import com.data.datatools.common.mybatis.CommonField;
import com.data.datatools.common.mybatis.Operator;
import com.data.datatools.repository.master.entity.ViewSysUseAuthEntity;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.insert.render.MultiRowInsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface ViewSysUseAuthMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..view_sys_use_auth")
    BasicColumn[] selectList = BasicColumn.columnList(userId, userName, password, passwordStatus, passwordUpdatedAt, currentProject, loginWrongCount, userEmail, userType, city, district, town, solidUser);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..view_sys_use_auth")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..view_sys_use_auth")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..view_sys_use_auth")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    int insert(InsertStatementProvider<ViewSysUseAuthEntity> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..view_sys_use_auth")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultiple")
    int insertMultiple(MultiRowInsertStatementProvider<ViewSysUseAuthEntity> multipleInsertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..view_sys_use_auth")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("ViewSysUseAuthEntityResult")
    Optional<ViewSysUseAuthEntity> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..view_sys_use_auth")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="ViewSysUseAuthEntityResult", value = {
        @Result(column="user_id", property="userId", jdbcType=JdbcType.VARCHAR),
        @Result(column="user_name", property="userName", jdbcType=JdbcType.VARCHAR),
        @Result(column="password", property="password", jdbcType=JdbcType.VARCHAR),
        @Result(column="password_status", property="passwordStatus", jdbcType=JdbcType.CHAR),
        @Result(column="password_updated_at", property="passwordUpdatedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="current_project", property="currentProject", jdbcType=JdbcType.CHAR),
        @Result(column="login_wrong_count", property="loginWrongCount", jdbcType=JdbcType.INTEGER),
        @Result(column="user_email", property="userEmail", jdbcType=JdbcType.VARCHAR),
        @Result(column="user_type", property="userType", jdbcType=JdbcType.VARCHAR),
        @Result(column="city", property="city", jdbcType=JdbcType.VARCHAR),
        @Result(column="district", property="district", jdbcType=JdbcType.CHAR),
        @Result(column="town", property="town", jdbcType=JdbcType.CHAR),
        @Result(column="solid_user", property="solidUser", jdbcType=JdbcType.INTEGER)
    })
    List<ViewSysUseAuthEntity> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..view_sys_use_auth")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..view_sys_use_auth")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, viewSysUseAuth, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..view_sys_use_auth")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, viewSysUseAuth, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..view_sys_use_auth")
    default int insert(ViewSysUseAuthEntity record) {
        CommonField.fill(record);
        return MyBatis3Utils.insert(this::insert, record, viewSysUseAuth, c ->
            c.map(userId).toProperty("userId")
            .map(userName).toProperty("userName")
            .map(password).toProperty("password")
            .map(passwordStatus).toProperty("passwordStatus")
            .map(passwordUpdatedAt).toProperty("passwordUpdatedAt")
            .map(currentProject).toProperty("currentProject")
            .map(loginWrongCount).toProperty("loginWrongCount")
            .map(userEmail).toProperty("userEmail")
            .map(userType).toProperty("userType")
            .map(city).toProperty("city")
            .map(district).toProperty("district")
            .map(town).toProperty("town")
            .map(solidUser).toProperty("solidUser")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..view_sys_use_auth")
    default int insertMultiple(Collection<ViewSysUseAuthEntity> records) {
        CommonField.fill(records);
        return MyBatis3Utils.insertMultiple(this::insertMultiple, records, viewSysUseAuth, c ->
            c.map(userId).toProperty("userId")
            .map(userName).toProperty("userName")
            .map(password).toProperty("password")
            .map(passwordStatus).toProperty("passwordStatus")
            .map(passwordUpdatedAt).toProperty("passwordUpdatedAt")
            .map(currentProject).toProperty("currentProject")
            .map(loginWrongCount).toProperty("loginWrongCount")
            .map(userEmail).toProperty("userEmail")
            .map(userType).toProperty("userType")
            .map(city).toProperty("city")
            .map(district).toProperty("district")
            .map(town).toProperty("town")
            .map(solidUser).toProperty("solidUser")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..view_sys_use_auth")
    default int insertSelective(ViewSysUseAuthEntity record) {
        CommonField.fill(record);
        return MyBatis3Utils.insert(this::insert, record, viewSysUseAuth, c ->
            c.map(userId).toPropertyWhenPresent("userId", record::getUserId)
            .map(userName).toPropertyWhenPresent("userName", record::getUserName)
            .map(password).toPropertyWhenPresent("password", record::getPassword)
            .map(passwordStatus).toPropertyWhenPresent("passwordStatus", record::getPasswordStatus)
            .map(passwordUpdatedAt).toPropertyWhenPresent("passwordUpdatedAt", record::getPasswordUpdatedAt)
            .map(currentProject).toPropertyWhenPresent("currentProject", record::getCurrentProject)
            .map(loginWrongCount).toPropertyWhenPresent("loginWrongCount", record::getLoginWrongCount)
            .map(userEmail).toPropertyWhenPresent("userEmail", record::getUserEmail)
            .map(userType).toPropertyWhenPresent("userType", record::getUserType)
            .map(city).toPropertyWhenPresent("city", record::getCity)
            .map(district).toPropertyWhenPresent("district", record::getDistrict)
            .map(town).toPropertyWhenPresent("town", record::getTown)
            .map(solidUser).toPropertyWhenPresent("solidUser", record::getSolidUser)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..view_sys_use_auth")
    default Optional<ViewSysUseAuthEntity> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, viewSysUseAuth, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..view_sys_use_auth")
    default List<ViewSysUseAuthEntity> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, viewSysUseAuth, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..view_sys_use_auth")
    default List<ViewSysUseAuthEntity> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, viewSysUseAuth, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..view_sys_use_auth")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, viewSysUseAuth, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..view_sys_use_auth")
    static UpdateDSL<UpdateModel> updateAllColumns(ViewSysUseAuthEntity record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(userId).equalTo(record::getUserId)
                .set(userName).equalTo(record::getUserName)
                .set(password).equalTo(record::getPassword)
                .set(passwordStatus).equalTo(record::getPasswordStatus)
                .set(passwordUpdatedAt).equalTo(record::getPasswordUpdatedAt)
                .set(currentProject).equalTo(record::getCurrentProject)
                .set(loginWrongCount).equalTo(record::getLoginWrongCount)
                .set(userEmail).equalTo(record::getUserEmail)
                .set(userType).equalTo(record::getUserType)
                .set(city).equalTo(record::getCity)
                .set(district).equalTo(record::getDistrict)
                .set(town).equalTo(record::getTown)
                .set(solidUser).equalTo(record::getSolidUser);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..view_sys_use_auth")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(ViewSysUseAuthEntity record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(userId).equalToWhenPresent(record::getUserId)
                .set(userName).equalToWhenPresent(record::getUserName)
                .set(password).equalToWhenPresent(record::getPassword)
                .set(passwordStatus).equalToWhenPresent(record::getPasswordStatus)
                .set(passwordUpdatedAt).equalToWhenPresent(record::getPasswordUpdatedAt)
                .set(currentProject).equalToWhenPresent(record::getCurrentProject)
                .set(loginWrongCount).equalToWhenPresent(record::getLoginWrongCount)
                .set(userEmail).equalToWhenPresent(record::getUserEmail)
                .set(userType).equalToWhenPresent(record::getUserType)
                .set(city).equalToWhenPresent(record::getCity)
                .set(district).equalToWhenPresent(record::getDistrict)
                .set(town).equalToWhenPresent(record::getTown)
                .set(solidUser).equalToWhenPresent(record::getSolidUser);
    }
}