package com.data.datatools.web.system.role;

import com.data.datatools.common.exception.BusinessException;
import com.data.datatools.common.response.BaseResponse;
import com.data.datatools.common.response.ListResponse;
import com.data.datatools.common.response.MessageResponse;
import com.data.datatools.repository.master.entity.SysRoleAuthEntity;
import com.data.datatools.repository.master.entity.SysRoleEntity;
import com.data.datatools.repository.master.mapper.SysRoleAuthMapper;
import com.data.datatools.repository.master.mapper.SysRoleAuthNames;
import com.data.datatools.repository.master.mapper.SysRoleMapper;
import com.data.datatools.repository.master.mapper.SysRoleNames;
import com.data.datatools.repository.master.mapper.SysUserRoleMapper;
import com.data.datatools.repository.master.mapper.SysUserRoleNames;
import com.data.datatools.web.system.role.dto.RoleAuthInfoModel;
import com.data.datatools.web.system.role.dto.RoleCondition;
import com.data.datatools.web.system.role.dto.RoleInfoModel;
import com.data.datatools.web.system.role.entity.RoleInfoEntity;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;
import java.util.UUID;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.SqlBuilder;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class RoleService {

  @Autowired
  private SysRoleMapper sysRoleMapper;

  @Autowired
  private SysRoleAuthMapper sysRoleAuthMapper;


  @Autowired
  private SysUserRoleMapper sysUserRoleMapper;

  @Autowired
  private RoleMapper roleMapper;

  /**
   * 条件分页查询角色信息
   *
   * @param condition 分页条件
   * @return 角色信息的分页结果集
   */
  public void searchByPagingWithCond(RoleCondition condition, ListResponse<RoleInfoModel> modelListResponse) {

    roleMapper.selectListWithPage(condition(condition), modelListResponse, e -> {
      RoleInfoModel infoModel = new RoleInfoModel();
      roleEntityToModel(e, infoModel, "list");
      return infoModel;
    });
  }

  private void roleEntityToModel(RoleInfoEntity entity, RoleInfoModel infoModel, String source) {
    BeanUtils.copyProperties(entity, infoModel);
    List<RoleAuthInfoModel> authModelList = roleMapper
      .findAuthsByRoleId(infoModel.getRoleId() == null ? "" : infoModel.getRoleId()).stream()
      .map(auth -> {
        RoleAuthInfoModel model = new RoleAuthInfoModel();
        BeanUtils.copyProperties(auth, model);
        return model;
      }).collect(Collectors.toList());

    Map<String, RoleAuthInfoModel> roleAuthMap = new TreeMap<>();
    authModelList.stream().filter((e -> Objects.equals(e.getAuthGroupId(), e.getAuthParentGroupId())))
      .forEach(e -> roleAuthMap.put(e.getAuthGroupId(), e));
    authModelList.stream().filter(e -> !Objects.equals(e.getAuthGroupId(), e.getAuthParentGroupId()))
      .filter(e -> e.getAuthGroupId() != null).forEach(e -> {
      RoleAuthInfoModel roleAuthInfoModel = roleAuthMap.get(e.getAuthGroupId());
      List<RoleAuthInfoModel> children = roleAuthInfoModel.getChildren();
      if (children == null) {
        children = new ArrayList<>();
        roleAuthInfoModel.setChildren(children);
      }
      children.add(e);
    });
    setAuthList(infoModel, roleAuthMap, source);
  }

  private void setAuthList(RoleInfoModel infoModel, Map<String, RoleAuthInfoModel> roleAuthMap, String source) {
    List<RoleAuthInfoModel> authList = new ArrayList<>(roleAuthMap.values()).stream()
      .sorted(Comparator.comparing(RoleAuthInfoModel::getShowSequence))
      .collect(Collectors.toList());

    authList.forEach(auth -> {
      // info，edit，add page switch
      long authCount = auth.getChildren().stream().filter(e -> StringUtils.equals(e.getRoleAuthValue(), "1")).count();
      if (authCount == 0) {
        auth.setRoleAuthValue("0");
      } else {
        auth.setRoleAuthValue("1");
      }
      // list page expand
      if (StringUtils.equals(source, "list")) {
        if (authCount > 0) {
          auth.setRoleAuthValue("1");
          StringBuilder childrenStr = new StringBuilder();
          for (RoleAuthInfoModel childrenAuth : auth.getChildren()) {
            if (StringUtils.equals(childrenAuth.getRoleAuthValue(), "1")) {
              childrenStr.append(childrenAuth.getAuthName()).append(" , ");
            }
          }
          childrenStr = new StringBuilder(childrenStr.substring(0, childrenStr.lastIndexOf(",")));
          auth.setChildrenAuthName(childrenStr.toString());
        }
      }
    });
    infoModel.setAuthList(authList);
  }

  private RoleCondition condition(RoleCondition condition) {
    RoleCondition param = new RoleCondition();
    BeanUtils.copyProperties(condition, param);
    param.setRoleName(StringUtils.isBlank(condition.getRoleName()) ? "" : condition.getRoleName().trim());
    param.setRoleType(StringUtils.isBlank(condition.getRoleType()) ? "" : condition.getRoleType().trim());
    return param;
  }

  /**
   * 根据角色ID查询角色详细信息（多表查询）
   *
   * @param id 用户ID
   * @return 用户详细信息
   */
  public RoleInfoModel findInfoById(String id) {
    RoleInfoModel infoModel = new RoleInfoModel();
    if (!StringUtils.isBlank(id)) {
      RoleInfoEntity infoEntity = roleMapper.findById(id);
      if (infoEntity != null) {
        roleEntityToModel(infoEntity, infoModel, "");
      } else {
        infoModel = null;
      }
    } else {
      roleEntityToModel(new RoleInfoEntity(), infoModel, "");
    }
    return infoModel;
  }

  /**
   * 根据roleId查询用户数量
   *
   * @param roleId 角色ID
   * @return 操作结果
   */
  public long getUserCountByRoleId(String roleId) {
    return sysUserRoleMapper.count(c -> c.where().and(SysUserRoleNames.roleId, SqlBuilder.isEqualTo(roleId)));
  }

  /**
   * 根据角色ID删除角色信息
   *
   * @param roleId 角色ID
   * @return 操作结果
   */
  @Transactional
  public int deleteById(String roleId) {
    sysUserRoleMapper.delete(c -> c.where().and(SysUserRoleNames.roleId, SqlBuilder.isEqualTo(roleId)));
    sysRoleAuthMapper.delete(c -> c.where().and(SysRoleAuthNames.roleId, SqlBuilder.isEqualTo(roleId)));
    return sysRoleMapper.delete(c -> c.where().and(SysRoleNames.roleId, SqlBuilder.isEqualTo(roleId)));
  }

  @Transactional
  public void deleteRoles(List<String> roleIds, MessageResponse msg) {
    roleIds.forEach(roleId -> {
      sysUserRoleMapper.delete(c -> c.where().and(SysUserRoleNames.roleId, SqlBuilder.isEqualTo(roleId)));
      sysRoleAuthMapper.delete(c -> c.where().and(SysRoleAuthNames.roleId, SqlBuilder.isEqualTo(roleId)));
      if (sysRoleMapper.delete(c -> c.where().and(SysRoleNames.roleId, SqlBuilder.isEqualTo(roleId))) == 0) {
        msg.addErrorMessage("删除失败。");
      }
    });
  }

  @Transactional
  public BaseResponse addRole(RoleInfoModel model) {
    long count = sysRoleMapper
      .count(c -> c.where().and(SysRoleNames.roleName, SqlBuilder.isEqualTo(model.getRoleName())));
    if (count > 0) {
      return MessageResponse.newErrorMessage("角色名称已存在.");
    }
    SysRoleEntity roleEntity = new SysRoleEntity();
    BeanUtils.copyProperties(model, roleEntity);
    String roleId = UUID.randomUUID().toString();
    roleEntity.setRoleId(roleId);
    roleEntity.setDeleted(false);
    if (null == roleEntity.getShowSequence()) {
      Integer maxShowSeq = roleMapper.findMaxShowSequence();
      if (null != maxShowSeq) {
        roleEntity.setShowSequence((short) (maxShowSeq + 1));
      } else {
        roleEntity.setShowSequence((short) 1);
      }
    }

    sysRoleMapper.insert(roleEntity);
    updateAuths(roleEntity.getRoleId(), model.getAuthList());
    return new BaseResponse();
  }

  /**
   * 更新角色信息
   *
   * @param model 更新的角色信息
   * @return 更新结果
   */
  @Transactional
  public BaseResponse updateRole(RoleInfoModel model) {
    long count = sysRoleMapper
      .count(c -> c.where().and(SysRoleNames.roleName, SqlBuilder.isEqualTo(model.getRoleName()))
        .and(SysRoleNames.roleId, SqlBuilder.isNotEqualTo(model.getRoleId())));
    if (count > 0) {
      return MessageResponse.newErrorMessage("角色名称已经存在。");
    }

    SysRoleEntity roleEntity = sysRoleMapper.selectOne(c -> c.where().and(SysRoleNames.roleId, SqlBuilder.isEqualTo(model.getRoleId())))
      .orElseThrow(() -> new BusinessException("该角色信息已经不存在，请刷新页面后再操作。"));

    BeanUtils.copyProperties(model, roleEntity);
    if (null == roleEntity.getShowSequence()) {
      roleEntity.setShowSequence((short) (roleMapper.findMaxShowSequence() + 1));
    }
    sysRoleMapper.updateByPrimaryKey(roleEntity);
    updateAuths(model.getRoleId(), model.getAuthList());
    return new BaseResponse();
  }

  /**
   * 根据角色ID更新权限关联表
   *
   * @param roleId   角色ID
   * @param authList 权限关联信息列表
   */
  private void updateAuths(String roleId, List<RoleAuthInfoModel> authList) {
    sysRoleAuthMapper.delete(c -> c.where().and(SysRoleAuthNames.roleId, SqlBuilder.isEqualTo(roleId)));
    authList.forEach(auth -> {
      if (auth.getChildren() != null) {
        auth.getChildren().stream().filter(e -> StringUtils.equals(e.getRoleAuthValue(), "1")).forEach(auth1 -> {
          SysRoleAuthEntity entity = new SysRoleAuthEntity();
          BeanUtils.copyProperties(auth1, entity);
          entity.setDeleted(false);
          entity.setRoleId(roleId);
          sysRoleAuthMapper.insert(entity);
        });
      }
    });
  }
}
