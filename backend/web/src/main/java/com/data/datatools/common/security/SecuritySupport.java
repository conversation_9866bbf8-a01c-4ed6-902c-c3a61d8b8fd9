package com.data.datatools.common.security;


import com.data.datatools.common.code.UserType;
import com.data.datatools.config.security.model.LoginUser;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * <pre>
 * SecuritySupport class
 * </pre>
 */
public class SecuritySupport {

  private static final String SESSION_SWITCHED_PROJECT_KEY = "__SESSION_SWITCHED_PROJECT_KEY__";

  private SecuritySupport() {
  }

  @Nullable
  public static String getLoginUserId() {
    LoginUser loginUser = getLoginUser();
    if (loginUser == null) {
      return null;
    }
    return loginUser.getUserId();
  }
  public static String getLoginUserIdParam() {
    return "'" + getLoginUserId() + "'";
  }

  public static String getLoginUserName() {
    return Objects.requireNonNull(getLoginUser()).getUserName();
  }

  public static String getUserType() {
    return Objects.requireNonNull(getLoginUser()).getUserType();
  }

  public static boolean isSuperManager() {
    return UserType.MANAGER.is(getUserType());
  }

  public static boolean isCityManager() {
    return UserType.CITY.is(getUserType());
  }

  public static boolean isDistrict() {
    return UserType.DISTRICT.is(getUserType());
  }

  public static String getDistrict() {
    return Objects.requireNonNull(getLoginUser()).getDistrict();
  }

  public static String getTown() {
    return Objects.requireNonNull(getLoginUser()).getStreet();
  }

  public static String getCurrentProjectParam() {
    return "'" + getCurrentProject() + "'";
  }

  public static String getCurrentProject() {
    HttpServletRequest request = ((ServletRequestAttributes) Objects
      .requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
    HttpSession session = request.getSession(false);
    String currentProject = (String) session.getAttribute(SESSION_SWITCHED_PROJECT_KEY);
    if (StringUtils.isEmpty(currentProject)) {
      LoginUser loginUser = getLoginUser();
      if (loginUser == null) {
        return null;
      }
      return loginUser.getCurrentProject();
    }
    return currentProject;
  }

  public static void switchProject(String currentProject) {
    Objects.requireNonNull(getLoginUser()).switchProject(currentProject);
    HttpServletRequest request = ((ServletRequestAttributes) Objects
      .requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
    HttpSession session = request.getSession(false);
    session.setAttribute(SESSION_SWITCHED_PROJECT_KEY, currentProject);
  }

  @Nullable
  public static LoginUser getLoginUser() {
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    if (authentication == null) {
      return null;
    }
    Object principal = authentication.getPrincipal();
    if (principal instanceof LoginUser) {
      return (LoginUser) principal;
    }
    return null;
  }

  public static boolean hasAuthority(String... authorities) {

    LoginUser loginUser = getLoginUser();

    List<String> authorityList = Arrays.asList(authorities);
    return loginUser != null && loginUser.getAuthorities().stream().map(GrantedAuthority::getAuthority).anyMatch(
      authorityList::contains);
  }

  public static boolean isLogin() {
    return getLoginUserId() != null;
  }

  public static boolean isAdmin() {
    LoginUser loginUser = getLoginUser();
    return loginUser != null && loginUser.isSolidUser();
  }
}
