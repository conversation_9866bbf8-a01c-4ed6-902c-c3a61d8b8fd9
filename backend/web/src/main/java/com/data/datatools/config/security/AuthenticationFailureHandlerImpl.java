package com.data.datatools.config.security;

import com.data.datatools.common.message.MessageConstants;
import java.io.IOException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import com.data.datatools.common.response.MessageResponse;
import com.data.datatools.web.auth.LoginAuthService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;

public class AuthenticationFailureHandlerImpl implements AuthenticationFailureHandler {

  private static final Logger LOGGER = LoggerFactory
      .getLogger(AuthenticationFailureHandlerImpl.class);

  private final LoginAuthService loginAuthService;

  public AuthenticationFailureHandlerImpl(LoginAuthService loginAuthService) {
    this.loginAuthService = loginAuthService;
  }

  @Override
  public void onAuthenticationFailure(HttpServletRequest request, HttpServletResponse response,
      AuthenticationException exception) throws IOException {
    String errorCode;
    if (exception instanceof UsernameNotFoundException) {
      errorCode = MessageConstants.MESSAGE_E0001;
    } else if (exception instanceof BadCredentialsException) {

      // loginAuthService.updateWrongCount(request.getParameter("username"), false);
      errorCode = MessageConstants.MESSAGE_E0001;
    } /*else if(exception instanceof LockedException) {
      errorCode = MessageIdConstants.MESSAGE_E0002;
    } else if(exception instanceof DisabledException) {
      errorCode = MessageIdConstants.MESSAGE_E0003;
    }*/ else {
      errorCode = MessageConstants.MESSAGE_E0002;
    }

    String username = request.getParameter("username");
    LOGGER.warn("[{}] - login failure!", username);


    ResponseSupport.jsonResponse(response, MessageResponse.newErrorMessage(errorCode));
  }
}
