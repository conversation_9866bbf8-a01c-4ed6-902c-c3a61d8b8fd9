package com.data.datatools.web.project;

import com.data.datatools.common.response.BaseResponse;
import com.data.datatools.web.project.dto.ProjectModal;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class ProjectController {

  @Autowired
  private ProjectService projectService;

  @GetMapping("/projects")
  public BaseResponse selectProject() {

    return projectService.selectProject();
  }

  @PostMapping("/project/create")
  public BaseResponse createProject(@RequestBody ProjectModal dto) {

    return projectService.createProject(dto);
  }

  @PostMapping("/project/delete/{projectId}")
  public BaseResponse deleteProject(@PathVariable("projectId") String projectId) {

    return projectService.deleteProject(projectId);
  }

  @PostMapping("/project/switch/{projectId}")
  public BaseResponse switchProject(@PathVariable("projectId") String projectId) {

    return projectService.switchProject(projectId);
  }
}
