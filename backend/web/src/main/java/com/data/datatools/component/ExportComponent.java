package com.data.datatools.component;

import com.data.datatools.common.code.ImportExportStatus;
import com.data.datatools.common.code.ImportExportType;
import com.data.datatools.common.mybatis.Operator;
import com.data.datatools.component.dto.excel.ExcelCreateModel;
import com.data.datatools.repository.master.entity.MImportExportHistoryEntity;
import com.data.datatools.repository.master.mapper.MImportExportHistoryMapper;
import com.data.datatools.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.OutputStream;
import java.nio.file.Files;
import java.util.Optional;
import java.util.function.Supplier;

@Component
public class ExportComponent {

  private static final Logger logger = LoggerFactory.getLogger(ExportComponent.class);

  @Autowired
  private ExcelCreateComponent excelCreateComponent;

  @Autowired
  private MImportExportHistoryMapper mImportExportHistoryMapper;

  @Value("${export.excel.base.path}")
  private String basePath;

  @Async
  public void exportExcel(String fileId, String tableId, Supplier<ExcelCreateModel> supplier, String userId) {

    Operator.setCurrentUserId(userId);

    ExcelCreateModel model = supplier.get();
    boolean result = true;
    try {
      File file = new File(StringUtils.concatPath(basePath, "excel", fileId));
      try (OutputStream os = Files.newOutputStream(file.toPath())) {
        excelCreateComponent.createExcelDynamicGrid(os, model.getTemplateName(), model.getModel(), model.getHeaders(),
          model.getObjectProps(), model.getData(), model.getMergeCells());
      }
    } catch (Exception e) {
      result = false;
      logger.error(e.getMessage(), e);
    }

    Optional<MImportExportHistoryEntity> optEntity = mImportExportHistoryMapper.selectByPrimaryKey(fileId);
    MImportExportHistoryEntity entity = optEntity.orElse(new MImportExportHistoryEntity());

    if (entity.getFileId() == null) {
      entity.setFileId(fileId);
      entity.setTableId(tableId);
      entity.setTableMode(model.getTableMode().getMode());
      entity.setType(ImportExportType.EXPORT.getType());
      entity.setStatus(result ? ImportExportStatus.DONE.getStatus() : ImportExportStatus.ERR.getStatus());
      entity.setExportFile(model.getFileName());
      entity.setDataCount(model.getData().size());
      entity.setExtraSpec("");
      entity.setDeleted(false);
      mImportExportHistoryMapper.insert(entity);
    } else {
      entity.setStatus(result ? ImportExportStatus.DONE.getStatus() : ImportExportStatus.ERR.getStatus());
      entity.setDataCount(model.getData().size());
      mImportExportHistoryMapper.updateByPrimaryKey(entity);
    }
  }

  public File getExportedFile(String fileId) {
    return new File(StringUtils.concatPath(basePath, "excel", fileId));
  }
}
