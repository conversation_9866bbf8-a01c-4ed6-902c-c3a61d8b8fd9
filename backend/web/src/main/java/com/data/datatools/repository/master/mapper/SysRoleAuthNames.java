package com.data.datatools.repository.master.mapper;

import java.sql.JDBCType;
import java.time.LocalDateTime;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class SysRoleAuthNames {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_role_auth")
    public static final SysRoleAuth sysRoleAuth = new SysRoleAuth();

    /**
     * 主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_role_auth.id")
    public static final SqlColumn<Integer> id = sysRoleAuth.id;

    /**
     * 角色ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_role_auth.role_id")
    public static final SqlColumn<String> roleId = sysRoleAuth.roleId;

    /**
     * 权限ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_role_auth.auth_id")
    public static final SqlColumn<String> authId = sysRoleAuth.authId;

    /**
     * 是否删除
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_role_auth.deleted")
    public static final SqlColumn<Boolean> deleted = sysRoleAuth.deleted;

    /**
     * 插入时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_role_auth.created_at")
    public static final SqlColumn<LocalDateTime> createdAt = sysRoleAuth.createdAt;

    /**
     * 做成者
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_role_auth.created_by")
    public static final SqlColumn<String> createdBy = sysRoleAuth.createdBy;

    /**
     * 更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_role_auth.updated_at")
    public static final SqlColumn<LocalDateTime> updatedAt = sysRoleAuth.updatedAt;

    /**
     * 更新者
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_role_auth.updated_by")
    public static final SqlColumn<String> updatedBy = sysRoleAuth.updatedBy;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_role_auth")
    public static final class SysRoleAuth extends SqlTable {
        public final SqlColumn<Integer> id = column("id", JDBCType.INTEGER);

        public final SqlColumn<String> roleId = column("role_id", JDBCType.VARCHAR);

        public final SqlColumn<String> authId = column("auth_id", JDBCType.VARCHAR);

        public final SqlColumn<Boolean> deleted = column("deleted", JDBCType.BIT);

        public final SqlColumn<LocalDateTime> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createdBy = column("created_by", JDBCType.VARCHAR);

        public final SqlColumn<LocalDateTime> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updatedBy = column("updated_by", JDBCType.VARCHAR);

        public SysRoleAuth() {
            super("sys_role_auth");
        }
    }
}