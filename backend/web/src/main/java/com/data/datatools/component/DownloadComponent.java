package com.data.datatools.component;

import com.alibaba.fastjson.JSON;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.util.UriUtils;

/**
 * <AUTHOR>
 */
@Component
public class DownloadComponent {

  public static final int BUFFER_SIZE = 1024;

  public ResponseEntity<byte[]> downLoad(ByteArrayOutputStream os, String filename) {
    return downLoad(os.toByteArray(), filename, null);
  }

  public ResponseEntity<byte[]> downLoad(byte[] bytes, String filename, Map<String, String> extraData) {
    MultiValueMap<String, String> headerMap = new LinkedMultiValueMap<>();
    headerMap.set("Content-Type",
      String.format("application/x-msdownload;filename=%s", UriUtils.encode(filename, StandardCharsets.UTF_8)));
    headerMap.set("Content-Length", String.valueOf(bytes.length));
    if (extraData != null && !extraData.isEmpty()) {
      headerMap.set("Extra-Data", encodeExtraData(extraData));
    }
    HttpHeaders headers = new HttpHeaders(headerMap);
    return new ResponseEntity<>(bytes, headers, HttpStatus.OK);
  }

  private static String encodeExtraData(Map<String, String> extraData) {
    Map<String, String> map = new HashMap<>();
    extraData.forEach((k, v) -> map.put(k, UriUtils.encode(v, StandardCharsets.UTF_8)));
    return JSON.toJSONString(map);
  }

  /**
   * InputStreamからファイルをダウンロードします。
   *
   * @param fileDirectory ファイルディレクトリ
   * @param fileName      ファイル名
   * @return 出力エンティティ
   */
  public ResponseEntity<byte[]> downloadFile(String fileDirectory, String fileName) {
    ClassLoader loader = Thread.currentThread().getContextClassLoader();
    String resourceFile = String.format("%s/%s", fileDirectory, fileName);
    try (ByteArrayOutputStream output = new ByteArrayOutputStream();
         InputStream is = loader.getResourceAsStream(resourceFile)) {
      byte[] buffer = new byte[BUFFER_SIZE];
      int n;
      while (-1 != (n = Objects.requireNonNull(is).read(buffer))) {
        output.write(buffer, 0, n);
      }
      return downLoad(output.toByteArray(), fileName, null);
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }
}
