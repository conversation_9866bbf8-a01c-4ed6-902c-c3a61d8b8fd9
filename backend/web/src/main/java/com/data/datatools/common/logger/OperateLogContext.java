package com.data.datatools.common.logger;

import com.data.datatools.utils.StringUtils;

import java.util.LinkedList;

public final class OperateLogContext {

  private static final ThreadLocal<LinkedList<String>> contextList = new ThreadLocal<>();

  private OperateLogContext() {
  }

  static void init() {
    contextList.remove();
    contextList.set(new LinkedList<>());
  }

  public static String pop() {
    return contextList.get().pop();
  }

  public static void put(String key, Object text) {
    contextList.get().add(StringUtils.concat(key, ":", String.valueOf(text)));
  }

  static void clear() {
    contextList.get().clear();
    contextList.remove();
  }
}
