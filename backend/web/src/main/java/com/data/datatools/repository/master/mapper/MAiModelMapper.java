package com.data.datatools.repository.master.mapper;

import static com.data.datatools.repository.master.mapper.MAiModelNames.*;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

import com.data.datatools.common.mybatis.CommonField;
import com.data.datatools.common.mybatis.Operator;
import com.data.datatools.common.mybatis.VersionAdd;
import com.data.datatools.repository.master.entity.MAiModelEntity;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.insert.render.MultiRowInsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface MAiModelMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_model")
    BasicColumn[] selectList = BasicColumn.columnList(modelId, name, pId, kId, dId, createdAt, createdBy, updatedAt, updatedBy);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_model")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_model")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_model")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    int insert(InsertStatementProvider<MAiModelEntity> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_model")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultiple")
    int insertMultiple(MultiRowInsertStatementProvider<MAiModelEntity> multipleInsertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_model")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("MAiModelEntityResult")
    Optional<MAiModelEntity> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_model")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="MAiModelEntityResult", value = {
        @Result(column="model_id", property="modelId", jdbcType=JdbcType.VARCHAR, id=true),
        @Result(column="name", property="name", jdbcType=JdbcType.VARCHAR),
        @Result(column="p_id", property="pId", jdbcType=JdbcType.VARCHAR),
        @Result(column="k_id", property="kId", jdbcType=JdbcType.VARCHAR),
        @Result(column="d_id", property="dId", jdbcType=JdbcType.VARCHAR),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="created_by", property="createdBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="updated_at", property="updatedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_by", property="updatedBy", jdbcType=JdbcType.VARCHAR)
    })
    List<MAiModelEntity> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_model")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_model")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, MAiModel, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_model")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, MAiModel, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_model")
    default int deleteByPrimaryKey(String modelId_) {
        return delete(c -> 
            c.where(modelId, isEqualTo(modelId_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_model")
    default int insert(MAiModelEntity record) {
        CommonField.fill(record);
        return MyBatis3Utils.insert(this::insert, record, MAiModel, c ->
            c.map(modelId).toProperty("modelId")
            .map(name).toProperty("name")
            .map(pId).toProperty("pId")
            .map(kId).toProperty("kId")
            .map(dId).toProperty("dId")
            .map(createdAt).toProperty("createdAt")
            .map(createdBy).toProperty("createdBy")
            .map(updatedAt).toProperty("updatedAt")
            .map(updatedBy).toProperty("updatedBy")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_model")
    default int insertMultiple(Collection<MAiModelEntity> records) {
        CommonField.fill(records);
        return MyBatis3Utils.insertMultiple(this::insertMultiple, records, MAiModel, c ->
            c.map(modelId).toProperty("modelId")
            .map(name).toProperty("name")
            .map(pId).toProperty("pId")
            .map(kId).toProperty("kId")
            .map(dId).toProperty("dId")
            .map(createdAt).toProperty("createdAt")
            .map(createdBy).toProperty("createdBy")
            .map(updatedAt).toProperty("updatedAt")
            .map(updatedBy).toProperty("updatedBy")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_model")
    default int insertSelective(MAiModelEntity record) {
        CommonField.fill(record);
        return MyBatis3Utils.insert(this::insert, record, MAiModel, c ->
            c.map(modelId).toPropertyWhenPresent("modelId", record::getModelId)
            .map(name).toPropertyWhenPresent("name", record::getName)
            .map(pId).toPropertyWhenPresent("pId", record::getpId)
            .map(kId).toPropertyWhenPresent("kId", record::getkId)
            .map(dId).toPropertyWhenPresent("dId", record::getdId)
            .map(createdAt).toPropertyWhenPresent("createdAt", record::getCreatedAt)
            .map(createdBy).toPropertyWhenPresent("createdBy", record::getCreatedBy)
            .map(updatedAt).toPropertyWhenPresent("updatedAt", record::getUpdatedAt)
            .map(updatedBy).toPropertyWhenPresent("updatedBy", record::getUpdatedBy)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_model")
    default Optional<MAiModelEntity> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, MAiModel, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_model")
    default List<MAiModelEntity> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, MAiModel, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_model")
    default List<MAiModelEntity> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, MAiModel, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_model")
    default Optional<MAiModelEntity> selectByPrimaryKey(String modelId_) {
        return selectOne(c ->
            c.where(modelId, isEqualTo(modelId_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_model")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, MAiModel, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_model")
    static UpdateDSL<UpdateModel> updateAllColumns(MAiModelEntity record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(modelId).equalTo(record::getModelId)
                .set(name).equalTo(record::getName)
                .set(pId).equalTo(record::getpId)
                .set(kId).equalTo(record::getkId)
                .set(dId).equalTo(record::getdId)
                .set(updatedAt).equalTo(LocalDateTime.now())
                .set(updatedBy).equalTo(Operator.of());
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_model")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(MAiModelEntity record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(modelId).equalToWhenPresent(record::getModelId)
                .set(name).equalToWhenPresent(record::getName)
                .set(pId).equalToWhenPresent(record::getpId)
                .set(kId).equalToWhenPresent(record::getkId)
                .set(dId).equalToWhenPresent(record::getdId)
                .set(updatedAt).equalTo(LocalDateTime.now())
                .set(updatedBy).equalTo(Operator.of());
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_model")
    default int updateByPrimaryKey(MAiModelEntity record) {
        return update(c ->
            c.set(name).equalTo(record::getName)
            .set(pId).equalTo(record::getpId)
            .set(kId).equalTo(record::getkId)
            .set(dId).equalTo(record::getdId)
            .set(updatedAt).equalTo(LocalDateTime.now())
            .set(updatedBy).equalTo(Operator.of())
            .where(modelId, isEqualTo(record::getModelId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_model")
    default int updateByPrimaryKeySelective(MAiModelEntity record) {
        return update(c ->
            c.set(name).equalToWhenPresent(record::getName)
            .set(pId).equalToWhenPresent(record::getpId)
            .set(kId).equalToWhenPresent(record::getkId)
            .set(dId).equalToWhenPresent(record::getdId)
            .set(updatedAt).equalTo(LocalDateTime.now())
            .set(updatedBy).equalTo(Operator.of())
            .where(modelId, isEqualTo(record::getModelId))
        );
    }
}