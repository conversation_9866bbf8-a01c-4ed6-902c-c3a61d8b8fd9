package com.data.datatools.repository.master.entity;

import com.data.datatools.common.base.dao.BaseEntity;
import java.io.Serializable;
import java.time.LocalDateTime;
import javax.annotation.Generated;

/**
 * AI分类字典项
 *
 * database table [data_analytics..m_ai_category]
 */
public class MAiCategoryEntity implements Serializable, BaseEntity {
    /**
     * 主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_ai_category.id")
    private Integer id;

    /**
     * 上级分类
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_ai_category.category")
    private String category;

    /**
     * 分类
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_ai_category.text")
    private String text;

    /**
     * 插入时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_ai_category.created_at")
    private LocalDateTime createdAt;

    /**
     * 做成者
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_ai_category.created_by")
    private String createdBy;

    /**
     * 更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_ai_category.updated_at")
    private LocalDateTime updatedAt;

    /**
     * 更新者
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_ai_category.updated_by")
    private String updatedBy;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_category")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_ai_category.id")
    public Integer getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_ai_category.id")
    public void setId(Integer id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_ai_category.category")
    public String getCategory() {
        return category;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_ai_category.category")
    public void setCategory(String category) {
        this.category = category;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_ai_category.text")
    public String getText() {
        return text;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_ai_category.text")
    public void setText(String text) {
        this.text = text;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_ai_category.created_at")
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_ai_category.created_at")
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_ai_category.created_by")
    public String getCreatedBy() {
        return createdBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_ai_category.created_by")
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_ai_category.updated_at")
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_ai_category.updated_at")
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_ai_category.updated_by")
    public String getUpdatedBy() {
        return updatedBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_ai_category.updated_by")
    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }
}