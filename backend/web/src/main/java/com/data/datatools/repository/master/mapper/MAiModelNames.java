package com.data.datatools.repository.master.mapper;

import java.sql.JDBCType;
import java.time.LocalDateTime;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class MAiModelNames {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_model")
    public static final MAiModel MAiModel = new MAiModel();

    /**
     * 主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_ai_model.model_id")
    public static final SqlColumn<String> modelId = MAiModel.modelId;

    /**
     * 名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_ai_model.name")
    public static final SqlColumn<String> name = MAiModel.name;

    /**
     * PID(调用识别接口用)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_ai_model.p_id")
    public static final SqlColumn<String> pId = MAiModel.pId;

    /**
     * 知识库ID(调用创建文档接口用)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_ai_model.k_id")
    public static final SqlColumn<String> kId = MAiModel.kId;

    /**
     * Doc ID(创建文档接口返回)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_ai_model.d_id")
    public static final SqlColumn<String> dId = MAiModel.dId;

    /**
     * 插入时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_ai_model.created_at")
    public static final SqlColumn<LocalDateTime> createdAt = MAiModel.createdAt;

    /**
     * 做成者
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_ai_model.created_by")
    public static final SqlColumn<String> createdBy = MAiModel.createdBy;

    /**
     * 更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_ai_model.updated_at")
    public static final SqlColumn<LocalDateTime> updatedAt = MAiModel.updatedAt;

    /**
     * 更新者
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..m_ai_model.updated_by")
    public static final SqlColumn<String> updatedBy = MAiModel.updatedBy;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_ai_model")
    public static final class MAiModel extends SqlTable {
        public final SqlColumn<String> modelId = column("model_id", JDBCType.VARCHAR);

        public final SqlColumn<String> name = column("name", JDBCType.VARCHAR);

        public final SqlColumn<String> pId = column("p_id", JDBCType.VARCHAR);

        public final SqlColumn<String> kId = column("k_id", JDBCType.VARCHAR);

        public final SqlColumn<String> dId = column("d_id", JDBCType.VARCHAR);

        public final SqlColumn<LocalDateTime> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createdBy = column("created_by", JDBCType.VARCHAR);

        public final SqlColumn<LocalDateTime> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updatedBy = column("updated_by", JDBCType.VARCHAR);

        public MAiModel() {
            super("m_ai_model");
        }
    }
}