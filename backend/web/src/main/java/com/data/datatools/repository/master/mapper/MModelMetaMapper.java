package com.data.datatools.repository.master.mapper;

import static com.data.datatools.repository.master.mapper.MModelMetaNames.*;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

import com.data.datatools.common.mybatis.CommonField;
import com.data.datatools.common.mybatis.Operator;
import com.data.datatools.common.mybatis.VersionAdd;
import com.data.datatools.repository.master.entity.MModelMetaEntity;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.insert.render.MultiRowInsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface MModelMetaMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_model_meta")
    BasicColumn[] selectList = BasicColumn.columnList(modelId, modelName, modelType, projectId, aiModelId, status, remark, deleted, version, createdAt, createdBy, updatedAt, updatedBy, modelMeta);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_model_meta")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_model_meta")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_model_meta")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    int insert(InsertStatementProvider<MModelMetaEntity> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_model_meta")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultiple")
    int insertMultiple(MultiRowInsertStatementProvider<MModelMetaEntity> multipleInsertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_model_meta")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("MModelMetaEntityResult")
    Optional<MModelMetaEntity> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_model_meta")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="MModelMetaEntityResult", value = {
        @Result(column="model_id", property="modelId", jdbcType=JdbcType.CHAR, id=true),
        @Result(column="model_name", property="modelName", jdbcType=JdbcType.VARCHAR),
        @Result(column="model_type", property="modelType", jdbcType=JdbcType.CHAR),
        @Result(column="project_id", property="projectId", jdbcType=JdbcType.CHAR),
        @Result(column="ai_model_id", property="aiModelId", jdbcType=JdbcType.VARCHAR),
        @Result(column="status", property="status", jdbcType=JdbcType.CHAR),
        @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
        @Result(column="deleted", property="deleted", jdbcType=JdbcType.BIT),
        @Result(column="version", property="version", jdbcType=JdbcType.INTEGER),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="created_by", property="createdBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="updated_at", property="updatedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_by", property="updatedBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="model_meta", property="modelMeta", jdbcType=JdbcType.LONGVARCHAR)
    })
    List<MModelMetaEntity> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_model_meta")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_model_meta")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, MModelMeta, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_model_meta")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, MModelMeta, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_model_meta")
    default int deleteByPrimaryKey(String modelId_) {
        return delete(c -> 
            c.where(modelId, isEqualTo(modelId_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_model_meta")
    default int insert(MModelMetaEntity record) {
        CommonField.fill(record);
        return MyBatis3Utils.insert(this::insert, record, MModelMeta, c ->
            c.map(modelId).toProperty("modelId")
            .map(modelName).toProperty("modelName")
            .map(modelType).toProperty("modelType")
            .map(projectId).toProperty("projectId")
            .map(aiModelId).toProperty("aiModelId")
            .map(status).toProperty("status")
            .map(remark).toProperty("remark")
            .map(deleted).toProperty("deleted")
            .map(version).toProperty("version")
            .map(createdAt).toProperty("createdAt")
            .map(createdBy).toProperty("createdBy")
            .map(updatedAt).toProperty("updatedAt")
            .map(updatedBy).toProperty("updatedBy")
            .map(modelMeta).toProperty("modelMeta")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_model_meta")
    default int insertMultiple(Collection<MModelMetaEntity> records) {
        CommonField.fill(records);
        return MyBatis3Utils.insertMultiple(this::insertMultiple, records, MModelMeta, c ->
            c.map(modelId).toProperty("modelId")
            .map(modelName).toProperty("modelName")
            .map(modelType).toProperty("modelType")
            .map(projectId).toProperty("projectId")
            .map(aiModelId).toProperty("aiModelId")
            .map(status).toProperty("status")
            .map(remark).toProperty("remark")
            .map(deleted).toProperty("deleted")
            .map(version).toProperty("version")
            .map(createdAt).toProperty("createdAt")
            .map(createdBy).toProperty("createdBy")
            .map(updatedAt).toProperty("updatedAt")
            .map(updatedBy).toProperty("updatedBy")
            .map(modelMeta).toProperty("modelMeta")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_model_meta")
    default int insertSelective(MModelMetaEntity record) {
        CommonField.fill(record);
        return MyBatis3Utils.insert(this::insert, record, MModelMeta, c ->
            c.map(modelId).toPropertyWhenPresent("modelId", record::getModelId)
            .map(modelName).toPropertyWhenPresent("modelName", record::getModelName)
            .map(modelType).toPropertyWhenPresent("modelType", record::getModelType)
            .map(projectId).toPropertyWhenPresent("projectId", record::getProjectId)
            .map(aiModelId).toPropertyWhenPresent("aiModelId", record::getAiModelId)
            .map(status).toPropertyWhenPresent("status", record::getStatus)
            .map(remark).toPropertyWhenPresent("remark", record::getRemark)
            .map(deleted).toPropertyWhenPresent("deleted", record::getDeleted)
            .map(version).toPropertyWhenPresent("version", record::getVersion)
            .map(createdAt).toPropertyWhenPresent("createdAt", record::getCreatedAt)
            .map(createdBy).toPropertyWhenPresent("createdBy", record::getCreatedBy)
            .map(updatedAt).toPropertyWhenPresent("updatedAt", record::getUpdatedAt)
            .map(updatedBy).toPropertyWhenPresent("updatedBy", record::getUpdatedBy)
            .map(modelMeta).toPropertyWhenPresent("modelMeta", record::getModelMeta)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_model_meta")
    default Optional<MModelMetaEntity> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, MModelMeta, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_model_meta")
    default List<MModelMetaEntity> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, MModelMeta, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_model_meta")
    default List<MModelMetaEntity> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, MModelMeta, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_model_meta")
    default Optional<MModelMetaEntity> selectByPrimaryKey(String modelId_) {
        return selectOne(c ->
            c.where(modelId, isEqualTo(modelId_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_model_meta")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, MModelMeta, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_model_meta")
    static UpdateDSL<UpdateModel> updateAllColumns(MModelMetaEntity record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(modelId).equalTo(record::getModelId)
                .set(modelName).equalTo(record::getModelName)
                .set(modelType).equalTo(record::getModelType)
                .set(projectId).equalTo(record::getProjectId)
                .set(aiModelId).equalTo(record::getAiModelId)
                .set(status).equalTo(record::getStatus)
                .set(remark).equalTo(record::getRemark)
                .set(deleted).equalTo(record::getDeleted)
                .set(version).equalTo(record::getVersion)
                .set(updatedAt).equalTo(LocalDateTime.now())
                .set(updatedBy).equalTo(Operator.of())
                .set(modelMeta).equalTo(record::getModelMeta);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_model_meta")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(MModelMetaEntity record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(modelId).equalToWhenPresent(record::getModelId)
                .set(modelName).equalToWhenPresent(record::getModelName)
                .set(modelType).equalToWhenPresent(record::getModelType)
                .set(projectId).equalToWhenPresent(record::getProjectId)
                .set(aiModelId).equalToWhenPresent(record::getAiModelId)
                .set(status).equalToWhenPresent(record::getStatus)
                .set(remark).equalToWhenPresent(record::getRemark)
                .set(deleted).equalToWhenPresent(record::getDeleted)
                .set(version).equalToWhenPresent(record::getVersion)
                .set(updatedAt).equalTo(LocalDateTime.now())
                .set(updatedBy).equalTo(Operator.of())
                .set(modelMeta).equalToWhenPresent(record::getModelMeta);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_model_meta")
    default int updateByPrimaryKey(MModelMetaEntity record) {
        return update(c ->
            c.set(modelName).equalTo(record::getModelName)
            .set(modelType).equalTo(record::getModelType)
            .set(projectId).equalTo(record::getProjectId)
            .set(aiModelId).equalTo(record::getAiModelId)
            .set(status).equalTo(record::getStatus)
            .set(remark).equalTo(record::getRemark)
            .set(deleted).equalTo(record::getDeleted)
            .set(version).equalTo(record::getVersion)
            .set(updatedAt).equalTo(LocalDateTime.now())
            .set(updatedBy).equalTo(Operator.of())
            .set(modelMeta).equalTo(record::getModelMeta)
            .where(modelId, isEqualTo(record::getModelId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..m_model_meta")
    default int updateByPrimaryKeySelective(MModelMetaEntity record) {
        return update(c ->
            c.set(modelName).equalToWhenPresent(record::getModelName)
            .set(modelType).equalToWhenPresent(record::getModelType)
            .set(projectId).equalToWhenPresent(record::getProjectId)
            .set(aiModelId).equalToWhenPresent(record::getAiModelId)
            .set(status).equalToWhenPresent(record::getStatus)
            .set(remark).equalToWhenPresent(record::getRemark)
            .set(deleted).equalToWhenPresent(record::getDeleted)
            .set(version).equalToWhenPresent(record::getVersion)
            .set(updatedAt).equalTo(LocalDateTime.now())
            .set(updatedBy).equalTo(Operator.of())
            .set(modelMeta).equalToWhenPresent(record::getModelMeta)
            .where(modelId, isEqualTo(record::getModelId))
        );
    }
}