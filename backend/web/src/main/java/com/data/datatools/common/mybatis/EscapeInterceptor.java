package com.data.datatools.common.mybatis;

import com.data.datatools.utils.ReflectUtils;
import com.data.datatools.utils.StringUtils;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Set;
import org.apache.ibatis.cache.CacheKey;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;

/**
 * エスケープインターセプター。
 *
 * <AUTHOR>
 */
@Intercepts({
  @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class,
    ResultHandler.class}),
  @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class,
    ResultHandler.class, CacheKey.class, BoundSql.class}),
})
public class EscapeInterceptor implements Interceptor {

  /**
   * LIKE
   */
  private static final String LIKE = "like";
  private static final String ESCAPE_STR = " like '%' || ? || '%'";
  private static final String MULTIPLE_INTERCEPTIONS_FLAG = ".flag";
  private static final String SPLIT_SIGN = "\\.";
  public static final String QUESTION_MARK = "?";
  public static final String SIGN_PERCENT = "%";
  public static final String DOT_MARK = ".";
  public static final String UNDERLINE = "_";

  /**
   * 途中で捕らえる。
   *
   * @param invocation 呼び出し
   * @return 呼び出しオブジェクト
   * @throws Throwable
   */
  @Override
  public Object intercept(Invocation invocation) throws Throwable {
    Object[] args = invocation.getArgs();
    MappedStatement ms = (MappedStatement) args[0];
    Object parameterObject = args[1];
    BoundSql boundSql = ms.getBoundSql(parameterObject);
    modifyLikeSql(boundSql.getSql(), parameterObject, boundSql);
    return invocation.proceed();
  }

  private String modifyLikeSql(String sql, Object parameterObject, BoundSql boundSql) {
    if (!(parameterObject instanceof HashMap) || !sql.toLowerCase().contains(LIKE)) {
      return sql;
    }

    String[] strList = sql.split("\\?");
    Set<String> keynames = new HashSet<>();

    for (int i = 0; i < strList.length; i++) {
      if (strList[i].toLowerCase().contains(LIKE) || !sql.toLowerCase().contains(QUESTION_MARK)) {
        keynames.add(boundSql.getParameterMappings().get(i).getProperty());
      }
    }

    String finalSql = sql;
    keynames.forEach(key -> {
      HashMap<String, Object> parameter = (HashMap) parameterObject;
      boolean handlerFlag = (boolean) parameter.getOrDefault(key + MULTIPLE_INTERCEPTIONS_FLAG, false);
      if (handlerFlag) {
        return;
      }
      Object value;
      Object fieldValue;
      String paramKey;
      if (finalSql.toLowerCase().contains(ESCAPE_STR)) {
        if (key.contains(DOT_MARK)) {
          // when the param is condition object(list screen like query)
          String[] param = key.split(SPLIT_SIGN);
          paramKey = param[0];
          String fieldName = param[1];
          value = parameter.get(paramKey);
          fieldValue = ReflectUtils.getFieldValue(value, ReflectUtils.getField(value.getClass(), fieldName));
          ReflectUtils.setFieldValue(value, fieldName, escape((String) fieldValue));
        } else {
          // when the param is object
          fieldValue = parameter.get(key);
          paramKey = key;
          value = escape((String) fieldValue);
        }
        if (getEscapeFlag(fieldValue)) {
          parameter.put(paramKey, value);
          parameter.put(key + MULTIPLE_INTERCEPTIONS_FLAG, true);
        }
      }
    });

    return sql;
  }

  private boolean getEscapeFlag(Object fieldValue) {
    return fieldValue instanceof String
      && (fieldValue.toString().contains(UNDERLINE)
      || fieldValue.toString().contains("\\")
      || fieldValue.toString().contains(SIGN_PERCENT));
  }

  private static String escape(String cr) {
    if (StringUtils.isNotEmpty(cr)) {
      cr = cr.replace("\\", "\\\\");
      cr = cr.replace(UNDERLINE, "\\_");
      cr = cr.replace(SIGN_PERCENT, "\\%");
    }
    return cr;
  }

}
