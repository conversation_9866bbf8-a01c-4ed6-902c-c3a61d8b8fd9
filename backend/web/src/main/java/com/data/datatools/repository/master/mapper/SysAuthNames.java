package com.data.datatools.repository.master.mapper;

import java.sql.JDBCType;
import java.time.LocalDateTime;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class SysAuthNames {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_auth")
    public static final SysAuth sysAuth = new SysAuth();

    /**
     * 主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth.id")
    public static final SqlColumn<Integer> id = sysAuth.id;

    /**
     * 权限ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth.auth_id")
    public static final SqlColumn<String> authId = sysAuth.authId;

    /**
     * 权限名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth.auth_name")
    public static final SqlColumn<String> authName = sysAuth.authName;

    /**
     * 所属的权限分组ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth.auth_group_id")
    public static final SqlColumn<String> authGroupId = sysAuth.authGroupId;

    /**
     * 组内显示顺序
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth.show_sequence")
    public static final SqlColumn<Short> showSequence = sysAuth.showSequence;

    /**
     * 是否删除
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth.deleted")
    public static final SqlColumn<Boolean> deleted = sysAuth.deleted;

    /**
     * 插入时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth.created_at")
    public static final SqlColumn<LocalDateTime> createdAt = sysAuth.createdAt;

    /**
     * 做成者
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth.created_by")
    public static final SqlColumn<String> createdBy = sysAuth.createdBy;

    /**
     * 更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth.updated_at")
    public static final SqlColumn<LocalDateTime> updatedAt = sysAuth.updatedAt;

    /**
     * 更新者
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..sys_auth.updated_by")
    public static final SqlColumn<String> updatedBy = sysAuth.updatedBy;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..sys_auth")
    public static final class SysAuth extends SqlTable {
        public final SqlColumn<Integer> id = column("id", JDBCType.INTEGER);

        public final SqlColumn<String> authId = column("auth_id", JDBCType.VARCHAR);

        public final SqlColumn<String> authName = column("auth_name", JDBCType.VARCHAR);

        public final SqlColumn<String> authGroupId = column("auth_group_id", JDBCType.VARCHAR);

        public final SqlColumn<Short> showSequence = column("show_sequence", JDBCType.SMALLINT);

        public final SqlColumn<Boolean> deleted = column("deleted", JDBCType.BIT);

        public final SqlColumn<LocalDateTime> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createdBy = column("created_by", JDBCType.VARCHAR);

        public final SqlColumn<LocalDateTime> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updatedBy = column("updated_by", JDBCType.VARCHAR);

        public SysAuth() {
            super("sys_auth");
        }
    }
}