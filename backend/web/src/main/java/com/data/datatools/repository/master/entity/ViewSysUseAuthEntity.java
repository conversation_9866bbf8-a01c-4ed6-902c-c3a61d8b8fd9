package com.data.datatools.repository.master.entity;

import com.data.datatools.common.base.dao.BaseEntity;
import java.io.Serializable;
import java.time.LocalDateTime;
import javax.annotation.Generated;

/**
 * VIEW
 *
 * database table [data_analytics..view_sys_use_auth]
 */
public class ViewSysUseAuthEntity implements Serializable, BaseEntity {
    /**
     * 用户ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.user_id")
    private String userId;

    /**
     * 用户姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.user_name")
    private String userName;

    /**
     * 密码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.password")
    private String password;

    /**
     * 密码状态
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.password_status")
    private String passwordStatus;

    /**
     * 密码变更时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.password_updated_at")
    private LocalDateTime passwordUpdatedAt;

    /**
     * 当前项目
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.current_project")
    private String currentProject;

    /**
     * 认证失败回数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.login_wrong_count")
    private Integer loginWrongCount;

    /**
     * 用户邮箱
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.user_email")
    private String userEmail;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.user_type")
    private String userType;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.city")
    private String city;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.district")
    private String district;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.town")
    private String town;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.solid_user")
    private Integer solidUser;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_analytics..view_sys_use_auth")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.user_id")
    public String getUserId() {
        return userId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.user_id")
    public void setUserId(String userId) {
        this.userId = userId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.user_name")
    public String getUserName() {
        return userName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.user_name")
    public void setUserName(String userName) {
        this.userName = userName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.password")
    public String getPassword() {
        return password;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.password")
    public void setPassword(String password) {
        this.password = password;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.password_status")
    public String getPasswordStatus() {
        return passwordStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.password_status")
    public void setPasswordStatus(String passwordStatus) {
        this.passwordStatus = passwordStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.password_updated_at")
    public LocalDateTime getPasswordUpdatedAt() {
        return passwordUpdatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.password_updated_at")
    public void setPasswordUpdatedAt(LocalDateTime passwordUpdatedAt) {
        this.passwordUpdatedAt = passwordUpdatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.current_project")
    public String getCurrentProject() {
        return currentProject;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.current_project")
    public void setCurrentProject(String currentProject) {
        this.currentProject = currentProject;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.login_wrong_count")
    public Integer getLoginWrongCount() {
        return loginWrongCount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.login_wrong_count")
    public void setLoginWrongCount(Integer loginWrongCount) {
        this.loginWrongCount = loginWrongCount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.user_email")
    public String getUserEmail() {
        return userEmail;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.user_email")
    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.user_type")
    public String getUserType() {
        return userType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.user_type")
    public void setUserType(String userType) {
        this.userType = userType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.city")
    public String getCity() {
        return city;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.city")
    public void setCity(String city) {
        this.city = city;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.district")
    public String getDistrict() {
        return district;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.district")
    public void setDistrict(String district) {
        this.district = district;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.town")
    public String getTown() {
        return town;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.town")
    public void setTown(String town) {
        this.town = town;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.solid_user")
    public Integer getSolidUser() {
        return solidUser;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_analytics..view_sys_use_auth.solid_user")
    public void setSolidUser(Integer solidUser) {
        this.solidUser = solidUser;
    }
}