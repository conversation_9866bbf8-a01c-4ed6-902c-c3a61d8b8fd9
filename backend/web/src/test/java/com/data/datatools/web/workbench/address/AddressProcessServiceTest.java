package com.data.datatools.web.workbench.address;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.lang.reflect.Method;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
public class AddressProcessServiceTest {

    @Test
    public void testAddressProcessing() throws Exception {
        AddressProcessService service = new AddressProcessService();
        
        // 使用反射调用私有方法进行测试
        Method processAddressMethod = AddressProcessService.class.getDeclaredMethod("processAddress", String.class);
        processAddressMethod.setAccessible(true);
        
        // 测试用例1: 昭化路60/1-10,55/1-8,延安西路1265/3.4.5,武夷路160/1.2,52/27.29,338/1.2,290/2
        String input1 = "昭化路60/1-10,55/1-8,延安西路1265/3.4.5,武夷路160/1.2,52/27.29,338/1.2,290/2";
        @SuppressWarnings("unchecked")
        List<String> result1 = (List<String>) processAddressMethod.invoke(service, input1);

        System.out.println("输入: " + input1);
        System.out.println("输出: " + result1);

        // 验证结果和顺序
        assertEquals(7, result1.size());
        assertEquals("昭化路60弄", result1.get(0));
        assertEquals("昭化路55弄", result1.get(1));
        assertEquals("延安西路1265弄", result1.get(2));
        assertEquals("武夷路160弄", result1.get(3));
        assertEquals("武夷路52弄", result1.get(4));
        assertEquals("武夷路338弄", result1.get(5));
        assertEquals("武夷路290弄", result1.get(6));
        
        // 测试用例2: 四川北路2044-2056,甜爱路1号,59号,山阴路112弄,124弄
        String input2 = "四川北路2044-2056,甜爱路1号,59号,山阴路112弄,124弄";
        @SuppressWarnings("unchecked")
        List<String> result2 = (List<String>) processAddressMethod.invoke(service, input2);
        
        System.out.println("输入: " + input2);
        System.out.println("输出: " + result2);
        
        assertTrue(result2.contains("四川北路2044弄"));
        assertTrue(result2.contains("四川北路2056弄"));
        assertTrue(result2.contains("山阴路112弄"));
        assertTrue(result2.contains("山阴路124弄"));
        
        // 测试用例3: 松花江路2591.2599.2603.2605弄
        String input3 = "松花江路2591.2599.2603.2605弄";
        @SuppressWarnings("unchecked")
        List<String> result3 = (List<String>) processAddressMethod.invoke(service, input3);

        System.out.println("输入: " + input3);
        System.out.println("输出: " + result3);

        assertTrue(result3.contains("松花江路2591弄"));
        assertTrue(result3.contains("松花江路2599弄"));
        assertTrue(result3.contains("松花江路2603弄"));
        assertTrue(result3.contains("松花江路2605弄"));

        // 测试用例4: 襄阳南路475,499弄1-12,14.15.17-22.1甲号.8甲号511.525.533弄3号
        String input4 = "襄阳南路475,499弄1-12,14.15.17-22.1甲号.8甲号511.525.533弄3号";
        @SuppressWarnings("unchecked")
        List<String> result4 = (List<String>) processAddressMethod.invoke(service, input4);

        System.out.println("输入: " + input4);
        System.out.println("输出: " + result4);

        assertTrue(result4.contains("襄阳南路475弄"));
        assertTrue(result4.contains("襄阳南路499弄"));
        assertTrue(result4.contains("襄阳南路511弄"));
        assertTrue(result4.contains("襄阳南路525弄"));
        assertTrue(result4.contains("襄阳南路533弄"));
        
        // 测试用例5: 新乐路7、9、17、19、21号、44弄*********.15号、56弄3号、151弄1
        String input5 = "新乐路7、9、17、19、21号、44弄*********.15号、56弄3号、151弄1";
        @SuppressWarnings("unchecked")
        List<String> result5 = (List<String>) processAddressMethod.invoke(service, input5);

        System.out.println("输入: " + input5);
        System.out.println("输出: " + result5);

        assertTrue(result5.contains("新乐路44弄"));
        assertTrue(result5.contains("新乐路56弄"));
        assertTrue(result5.contains("新乐路151弄"));

        // 测试用例6: 甜爱路1号,59号,山阴路112弄,124弄,132弄,16号,206号
        String input6 = "甜爱路1号,59号,山阴路112弄,124弄,132弄,16号,206号";
        @SuppressWarnings("unchecked")
        List<String> result6 = (List<String>) processAddressMethod.invoke(service, input6);

        System.out.println("输入: " + input6);
        System.out.println("输出: " + result6);

        // 应该只包含弄号，忽略号
        assertEquals(3, result6.size());
        assertEquals("山阴路112弄", result6.get(0));
        assertEquals("山阴路124弄", result6.get(1));
        assertEquals("山阴路132弄", result6.get(2));

        // 测试用例7: 松花江路2591.2599.2603.2605弄
        String input7 = "松花江路2591.2599.2603.2605弄";
        @SuppressWarnings("unchecked")
        List<String> result7 = (List<String>) processAddressMethod.invoke(service, input7);

        System.out.println("输入: " + input7);
        System.out.println("输出: " + result7);

        assertEquals(4, result7.size());
        assertEquals("松花江路2591弄", result7.get(0));
        assertEquals("松花江路2599弄", result7.get(1));
        assertEquals("松花江路2603弄", result7.get(2));
        assertEquals("松花江路2605弄", result7.get(3));

        // 测试用例8: 江西中路383-391d号;宁波路74弄3,7,11,15,80-86s号;
        String input8 = "江西中路383-391d号;宁波路74弄3,7,11,15,80-86s号;";
        @SuppressWarnings("unchecked")
        List<String> result8 = (List<String>) processAddressMethod.invoke(service, input8);

        System.out.println("输入: " + input8);
        System.out.println("输出: " + result8);

        // 应该生成江西中路383-391弄和宁波路74弄
        assertTrue(result8.contains("江西中路383弄"));
        assertTrue(result8.contains("江西中路391弄"));
        assertTrue(result8.contains("宁波路74弄"));
    }
}
