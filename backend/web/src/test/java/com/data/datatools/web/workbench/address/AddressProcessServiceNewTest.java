package com.data.datatools.web.workbench.address;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.lang.reflect.Method;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
public class AddressProcessServiceNewTest {

    @Test
    public void testAddressProcessing() throws Exception {
        AddressProcessServiceNew service = new AddressProcessServiceNew();
        
        // 使用反射调用私有方法
        Method processAddressMethod = AddressProcessServiceNew.class.getDeclaredMethod("processAddress", String.class);
        processAddressMethod.setAccessible(true);
        
        // 测试用例1: 松花江路2591.2599.2603.2605弄
        String input1 = "松花江路2591.2599.2603.2605弄";
        @SuppressWarnings("unchecked")
        List<String> result1 = (List<String>) processAddressMethod.invoke(service, input1);

        System.out.println("输入: " + input1);
        System.out.println("输出: " + result1);

        assertEquals(4, result1.size());
        assertEquals("松花江路2591号", result1.get(0));
        assertEquals("松花江路2599弄", result1.get(1));
        assertEquals("松花江路2603弄", result1.get(2));
        assertEquals("松花江路2605弄", result1.get(3));
        
        // 测试用例2: 襄阳南路475,499弄1-*********甲号.8甲号511.525.533弄3号
        String input2 = "襄阳南路475,499弄1-*********甲号.8甲号511.525.533弄3号";
        @SuppressWarnings("unchecked")
        List<String> result2 = (List<String>) processAddressMethod.invoke(service, input2);

        System.out.println("输入: " + input2);
        System.out.println("输出: " + result2);

        // 验证关键结果
        assertTrue(result2.contains("襄阳南路475弄"));
        assertTrue(result2.contains("襄阳南路499弄1号"));
        assertTrue(result2.contains("襄阳南路499弄2号"));
        assertTrue(result2.contains("襄阳南路499弄3号"));
        assertTrue(result2.contains("襄阳南路499弄14号"));
        assertTrue(result2.contains("襄阳南路499弄15号"));
        assertTrue(result2.contains("襄阳南路499弄1甲号"));
        assertTrue(result2.contains("襄阳南路499弄8甲号"));
        assertTrue(result2.contains("襄阳南路511弄"));
        assertTrue(result2.contains("襄阳南路525弄"));
        assertTrue(result2.contains("襄阳南路533弄3号"));

        // 测试用例3: 新村路170，宜川路413.451弄
        String input3 = "新村路170，宜川路413.451弄";
        @SuppressWarnings("unchecked")
        List<String> result3 = (List<String>) processAddressMethod.invoke(service, input3);

        System.out.println("输入: " + input3);
        System.out.println("输出: " + result3);

        assertEquals(3, result3.size());
        assertEquals("新村路170号", result3.get(0));
        assertEquals("宜川路413号", result3.get(1));
        assertEquals("宜川路451弄", result3.get(2));
        
        // 测试用例4: 新乐路7、9、17、19、21号、44弄4.5号、56弄3号、136-140号、142弄1、192-196（双）
        String input4 = "新乐路7、9、17、19、21号、44弄4.5号、56弄3号、136-140号、142弄1、192-196（双）";
        @SuppressWarnings("unchecked")
        List<String> result4 = (List<String>) processAddressMethod.invoke(service, input4);

        System.out.println("输入: " + input4);
        System.out.println("输出: " + result4);

        // 验证关键结果
        assertTrue(result4.contains("新乐路7号"));
        assertTrue(result4.contains("新乐路9号"));
        assertTrue(result4.contains("新乐路17号"));
        assertTrue(result4.contains("新乐路19号"));
        assertTrue(result4.contains("新乐路21号"));
        assertTrue(result4.contains("新乐路44弄4号"));
        assertTrue(result4.contains("新乐路44弄5号"));
        assertTrue(result4.contains("新乐路56弄3号"));
        assertTrue(result4.contains("新乐路136号"));
        assertTrue(result4.contains("新乐路140号"));
        assertTrue(result4.contains("新乐路142弄1号"));
        assertTrue(result4.contains("新乐路192号"));
        assertTrue(result4.contains("新乐路194号"));
        assertTrue(result4.contains("新乐路196号"));
    }
}
