SET SESSION FOREIGN_KEY_CHECKS = 0;

/* Drop Tables */
SET GROUP_CONCAT_MAX_LEN = 32768;

SET @tables = NULL;
SELECT GROUP_CONCAT('`', table_name, '`')
INTO @tables
FROM information_schema.tables
WHERE table_schema = (SELECT DATABASE())
  AND TABLE_TYPE = 'BASE TABLE';
-- AND TABLE_NAME NOT LIKE 'T_%';

SELECT IFNULL(@tables, 'dummy')
INTO @tables;

SET @tables = CONCAT('DROP TABLE IF EXISTS ', @tables);
PREPARE stmt FROM @tables;
EXECUTE stmt;
SET FOREIGN_KEY_CHECKS = 1;

/* Create Tables */
-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
CREATE TABLE `sys_user`
(
    `user_id`             varchar(48)  NOT NULL COMMENT '用户ID',
    `user_name`           varchar(64)  NOT NULL COMMENT '用户姓名',
    `user_email`          varchar(64)  NULL COMMENT '用户邮箱',
    `password`            varchar(64)  NOT NULL COMMENT '密码',
    `password_status`     char(1)      NOT NULL default '0' COMMENT '用户状态', -- 0:无效 1:正常 2:锁定
    `password_updated_at` timestamp             DEFAULT CURRENT_TIMESTAMP COMMENT '密码变更时间',
    `current_project`     char(5)      NULL     DEFAULT NULL COMMENT '当前项目',
    `token`               varchar(128) NULL     DEFAULT NULL COMMENT 'URL TOKEN',
    `login_wrong_count`   int                   DEFAULT 0 NOT NULL COMMENT '认证失败回数',
    `user_type`           char(1)      NULL COMMENT '用户区分', -- 0:超级管理员 1:市管理员 2:区管理员 3:街道管理员
    `prov`                varchar(48)  NULL COMMENT '省',
    `city`                varchar(48)  NULL COMMENT '市',
    `district`            varchar(48)  NULL COMMENT '区',
    `town`                varchar(128) NULL COMMENT '镇',
    `road`                varchar(256) NULL COMMENT '街道',
    `remark`              varchar(128) NULL     DEFAULT '' COMMENT '备注',
    `login_time`          varchar(36)  NULL     DEFAULT NULL COMMENT '最后登录时间',
    `login_ip`            varchar(36)  NULL     DEFAULT NULL COMMENT '最后登录IP',
    `deleted`             tinyint(1)   NOT NULL DEFAULT 0 COMMENT '删除标志:0：未删除，1：删除',
    `created_at`          timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    `created_by`          varchar(30)  NOT NULL DEFAULT 'System' COMMENT '做成者',
    `updated_at`          timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `updated_by`          varchar(30)  NOT NULL DEFAULT 'System' COMMENT '更新者',
    PRIMARY KEY (`user_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '用户表'
  ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for sys_solid_user
-- ----------------------------
CREATE TABLE `sys_solid_user`
(
    `user_id`             varchar(48)  NOT NULL COMMENT '用户ID',
    `user_name`           varchar(64)  NOT NULL COMMENT '用户姓名',
    `user_email`          varchar(64)  NULL     DEFAULT NULL COMMENT '用户邮箱',
    `password`            varchar(64)  NOT NULL COMMENT '密码',
    `password_status`     char(1)      NOT NULL default '1' COMMENT '密码状态',
    `password_updated_at` timestamp             DEFAULT CURRENT_TIMESTAMP COMMENT '密码变更时间',
    `current_project`     char(5)      NULL     DEFAULT NULL COMMENT '当前项目',
    `token`               varchar(128) NULL     DEFAULT NULL COMMENT 'URLトークン',
    `login_wrong_count`   int                   DEFAULT 0 NOT NULL COMMENT '认证失败回数',
    `remark`              varchar(128) NULL     DEFAULT '' COMMENT '备注',
    `login_time`          varchar(36)  NULL     DEFAULT NULL COMMENT '最后登录时间',
    `login_ip`            varchar(36)  NULL     DEFAULT NULL COMMENT '最后登录IP',
    `deleted`             tinyint(1)   NOT NULL DEFAULT 0 COMMENT '是否删除',
    `created_at`          timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    `created_by`          varchar(30)  NOT NULL DEFAULT 'System' COMMENT '做成者',
    `updated_at`          timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `updated_by`          varchar(30)  NOT NULL DEFAULT 'System' COMMENT '更新者',
    PRIMARY KEY (`user_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '固化用户表'
  ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
CREATE TABLE `sys_role`
(
    `id`            int         NOT NULL AUTO_INCREMENT COMMENT '主键',
    `role_id`       varchar(64) NOT NULL UNIQUE COMMENT '角色ID',
    `role_name`     varchar(12) NOT NULL UNIQUE COMMENT '角色名称',
    `role_type`     char(1)     NOT NULL DEFAULT '1' COMMENT '角色类型：1:个人用；2：组织用',
    `show_sequence` smallint    NOT NULL COMMENT '显示顺序',
    `deleted`       tinyint(1)  NOT NULL DEFAULT 0 COMMENT '是否删除',
    `created_at`    timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    `created_by`    varchar(30) NOT NULL DEFAULT 'System' COMMENT '做成者',
    `updated_at`    timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `updated_by`    varchar(30) NOT NULL DEFAULT 'System' COMMENT '更新者',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '角色表'
  ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for sys_auth
-- ----------------------------
CREATE TABLE `sys_auth`
(
    `id`            int         NOT NULL AUTO_INCREMENT COMMENT '主键',
    `auth_id`       varchar(50) NOT NULL UNIQUE COMMENT '权限ID',
    `auth_name`     varchar(12) NOT NULL UNIQUE COMMENT '权限名称',
    `auth_group_id` varchar(50) NOT NULL COMMENT '所属的权限分组ID',
    `show_sequence` smallint    NOT NULL COMMENT '组内显示顺序',
    `deleted`       tinyint(1)  NOT NULL DEFAULT 0 COMMENT '是否删除',
    `created_at`    timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    `created_by`    varchar(30) NOT NULL DEFAULT 'System' COMMENT '做成者',
    `updated_at`    timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `updated_by`    varchar(30) NOT NULL DEFAULT 'System' COMMENT '更新者',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '权限表'
  ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for sys_auth_group
-- ----------------------------
CREATE TABLE `sys_auth_group`
(
    `id`              int         NOT NULL AUTO_INCREMENT COMMENT '主键',
    `auth_group_id`   varchar(50) NOT NULL UNIQUE COMMENT '权限分组ID',
    `auth_group_name` varchar(50) NOT NULL UNIQUE COMMENT '权限分组名称',
    `show_sequence`   smallint    NOT NULL COMMENT '权限分组显示顺序',
    `deleted`         tinyint(1)  NOT NULL DEFAULT 0 COMMENT '是否删除',
    `created_at`      timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    `created_by`      varchar(30) NOT NULL DEFAULT 'System' COMMENT '做成者',
    `updated_at`      timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `updated_by`      varchar(30) NOT NULL DEFAULT 'System' COMMENT '更新者',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '权限分组表'
  ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for sys_role_auth
-- ----------------------------
CREATE TABLE `sys_role_auth`
(
    `id`         int         NOT NULL AUTO_INCREMENT COMMENT '主键',
    `role_id`    varchar(64) NOT NULL COMMENT '角色ID',
    `auth_id`    varchar(50) NOT NULL COMMENT '权限ID',
    `deleted`    tinyint(1)  NOT NULL DEFAULT 0 COMMENT '是否删除',
    `created_at` timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    `created_by` varchar(30) NOT NULL DEFAULT 'System' COMMENT '做成者',
    `updated_at` timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `updated_by` varchar(30) NOT NULL DEFAULT 'System' COMMENT '更新者',
    PRIMARY KEY (`id`),
    UNIQUE (`role_id`, `auth_id`)
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '角色权限关系表'
  ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
CREATE TABLE `sys_user_role`
(
    `id`         int         NOT NULL AUTO_INCREMENT COMMENT '主键',
    `user_id`    varchar(48) NOT NULL COMMENT '用户ID',
    `role_id`    varchar(64) NOT NULL COMMENT '角色ID',
    `deleted`    tinyint(1)  NOT NULL DEFAULT 0 COMMENT '是否删除',
    `created_at` timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    `created_by` varchar(30) NOT NULL DEFAULT 'System' COMMENT '做成者',
    `updated_at` timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `updated_by` varchar(30) NOT NULL DEFAULT 'System' COMMENT '更新者',
    PRIMARY KEY (`id`),
    UNIQUE (`user_id`, `role_id`)
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '用户角色关系表'
  ROW_FORMAT = Compact;

CREATE TABLE `sys_area_shanghai`
(
    `id`         int         NOT NULL AUTO_INCREMENT COMMENT '主键',
    `prov`       varchar(48)  NULL COMMENT '省',
    `city`       varchar(48)  NULL COMMENT '市',
    `district`   varchar(48)  NULL COMMENT '区',
    `town`       varchar(128) NULL COMMENT '镇/街道',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '上海区划表'
  ROW_FORMAT = Compact;


-- ----------------------------
-- VIEW structure for view_sys_use_auth
-- ----------------------------
CREATE OR REPLACE VIEW `view_sys_use_auth` AS
SELECT a.user_id,
       a.user_name,
       a.password,
       a.password_status,
       a.password_updated_at,
       a.current_project,
       a.login_wrong_count,
       a.user_email,
       '0' as user_type,
       '上海市' as city,
       '' as district,
       '' as town,
       true      as solid_user
FROM sys_solid_user a
UNION
SELECT su.user_id,
       su.user_name,
       su.password,
       su.password_status,
       su.password_updated_at,
       su.current_project,
       su.login_wrong_count,
       su.user_email,
       su.user_type,
       '上海市' as city,
       su.district,
       su.town,
       false                               as solid_user
FROM `sys_user` su
WHERE su.deleted = 0
GROUP BY su.user_id;


-- ----------------------------
-- Table structure for m_project
-- ----------------------------
CREATE TABLE `m_project`
(
    `project_id`   char(5)      NOT NULL COMMENT '项目ID',
    `project_name` varchar(48)  NOT NULL COMMENT '项目名',
    `remark`       varchar(128) NULL COMMENT '描述',
    `deleted`      tinyint(1)   NOT NULL DEFAULT 0 COMMENT '是否删除',
    `created_at`   timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    `created_by`   varchar(30)  NOT NULL DEFAULT 'System' COMMENT '做成者',
    `updated_at`   timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `updated_by`   varchar(30)  NOT NULL DEFAULT 'System' COMMENT '更新者',
    PRIMARY KEY (`project_id`)
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '项目管理表'
  ROW_FORMAT = Compact;


-- ----------------------------
-- Table structure for m_table_meta
-- ----------------------------
CREATE TABLE `m_table_meta`
(
    `table_id`     char(10)     NOT NULL COMMENT '主键',
    `table_name`   varchar(48)  NOT NULL COMMENT '表名',
    `project_id`   char(5)      NOT NULL COMMENT '所属项目ID',
    `table_meta`   json         NOT NULL COMMENT '数据表Meta',
    `match_status` char(1)      NOT NULL DEFAULT '0' COMMENT '地区匹配状态 0:完成 1:进行中 2:失败',
    `remark`       varchar(128) NULL COMMENT '描述',
    `deleted`      tinyint(1)   NOT NULL DEFAULT 0 COMMENT '是否删除',
    `created_at`   timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    `created_by`   varchar(30)  NOT NULL DEFAULT 'System' COMMENT '做成者',
    `updated_at`   timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `updated_by`   varchar(30)  NOT NULL DEFAULT 'System' COMMENT '更新者',
    PRIMARY KEY (`table_id`)
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '数据表Meta信息'
  ROW_FORMAT = Compact;

-- ALTER TABLE data_analytics.m_table_meta ADD match_status char(1) NOT NULL COMMENT '地区匹配状态 0:完成 1:进行中 2:失败';
-- ALTER TABLE data_analytics.m_table_meta CHANGE match_status match_status char(1) NOT NULL COMMENT '地区匹配状态 0:完成 1:进行中 2:失败' AFTER table_meta;

-- ----------------------------
-- Table structure for m_data_insert_history
-- ----------------------------
CREATE TABLE `m_data_insert_history`
(
    `id`          int         NOT NULL AUTO_INCREMENT COMMENT '主键',
    `table_id`    char(10)     NOT NULL COMMENT '数据表ID',
    `import_key`  char(5)     NOT NULL COMMENT 'Key',
    `import_type` int         NOT NULL COMMENT '导入类型 1：文件导入 2：手动录入',
    `import_time` timestamp   NOT NULL,
    `import_meta` json        NULL COMMENT 'Tags, etc.',
    `created_at`  timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    `created_by`  varchar(30) NOT NULL DEFAULT 'System' COMMENT '做成者',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '数据录入历史'
  ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for m_import_export_history
-- ----------------------------
CREATE TABLE `m_import_export_history`
(
  `file_id`     varchar(10)  NOT NULL COMMENT '文件ID',
  `table_id`    char(10)      NOT NULL COMMENT '源数据表ID',
  `table_mode`  char(1)      NOT NULL COMMENT '模式 0：原始数据 1：算分数据',
  `type`        char(1)      NOT NULL COMMENT '类型 0：导入 1：导出',
  `status`      char(1)      NOT NULL COMMENT '状态 0：进行中 1：完成 2：失败',
  `export_file` varchar(128) NOT NULL COMMENT '文件名',
  `data_count`  int          NOT NULL COMMENT '数据件数',
  `extra_spec`  varchar(100) NULL COMMENT '额外信息',
  `deleted`     tinyint(1)   NOT NULL DEFAULT 0 COMMENT '是否删除',
  `created_at`  timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
  `created_by`  varchar(30)  NOT NULL DEFAULT 'System' COMMENT '做成者',
  `updated_at`  timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `updated_by`  varchar(30)  NOT NULL DEFAULT 'System' COMMENT '更新者',
  PRIMARY KEY (`file_id`)
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '导入导出履历表'
  ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for m_model_meta
-- ----------------------------
CREATE TABLE `m_model_meta`
(
    `model_id`     char(8)      NOT NULL COMMENT '主键',
    `model_name`   varchar(48)  NOT NULL COMMENT '模型名称',
    `model_type`   char(1)      NOT NULL COMMENT '模型类型',
    `project_id`   char(5)      NOT NULL COMMENT '所属项目ID',
    `model_meta`   json         NOT NULL COMMENT '模型Meta',
    `ai_model_id`  varchar(10)  NULL COMMENT 'Ai模型ID',
    `status`       char(1)      NOT NULL DEFAULT '0' COMMENT '状态 0:正常，1:异常',
    `remark`       varchar(128) NULL COMMENT '描述',
    `deleted`      tinyint(1)   NOT NULL DEFAULT 0 COMMENT '是否删除',
    `version`      int          NOT NULL COMMENT '版本',
    `created_at`   timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    `created_by`   varchar(30)  NOT NULL DEFAULT 'System' COMMENT '做成者',
    `updated_at`   timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `updated_by`   varchar(30)  NOT NULL DEFAULT 'System' COMMENT '更新者',
    PRIMARY KEY (`model_id`)
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '数据模型Meta信息'
  ROW_FORMAT = Compact;

# ALTER TABLE m_model_meta ADD ai_model_id varchar(10) NULL COMMENT 'Ai模型ID';
# ALTER TABLE m_model_meta CHANGE ai_model_id ai_model_id varchar(10) NULL COMMENT 'Ai模型ID' AFTER model_meta;
# ALTER TABLE m_model_meta ADD status char(1) DEFAULT '0' NOT NULL;
# ALTER TABLE m_model_meta CHANGE status status char(1) DEFAULT '0' NOT NULL AFTER ai_model_id;

-- ----------------------------
-- Table structure for m_display_meta
-- ----------------------------
CREATE TABLE `m_display_meta`
(
    `display_id`      char(10)     NOT NULL COMMENT '主键',
    `display_name`    varchar(48)  NOT NULL COMMENT '展示名',
    `table_id`        char(10)     NOT NULL COMMENT '原始表名',
    `project_id`      char(5)      NOT NULL COMMENT '所属项目ID',
    `model_type`      char(1)      NOT NULL DEFAULT '1' COMMENT '模型类型',
    `show_existing`   tinyint(1)   NOT NULL DEFAULT 0 COMMENT '显示新建存量',
    `display_meta`    json         NOT NULL COMMENT '展示Meta',
    `status`          varchar(16)  NULL COMMENT '状态',
    `analytics_time`  timestamp    NULL COMMENT '智能分析时刻',
    `analytics_count` bigint       NULL COMMENT '智能分析件数',
    `remark`          varchar(128) NULL COMMENT '描述',
    `deleted`         tinyint(1)   NOT NULL DEFAULT 0 COMMENT '是否删除',
    `created_at`      timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    `created_by`      varchar(30)  NOT NULL DEFAULT 'System' COMMENT '做成者',
    `updated_at`      timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `updated_by`      varchar(30)  NOT NULL DEFAULT 'System' COMMENT '更新者',
    PRIMARY KEY (`display_id`)
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '数据展示Meta信息'
  ROW_FORMAT = Compact;

# ALTER TABLE m_display_meta ADD model_type char(1) DEFAULT '1' NOT NULL COMMENT '模型类型';
# ALTER TABLE m_display_meta CHANGE model_type model_type char(1) DEFAULT '1' NOT NULL COMMENT '模型类型' AFTER project_id;
# ALTER TABLE m_display_meta ADD show_existing tinyint(1) DEFAULT 0 NOT NULL COMMENT '显示新建存量';
# ALTER TABLE m_display_meta CHANGE show_existing show_existing tinyint(1) DEFAULT 0 NOT NULL COMMENT '显示新建存量' AFTER model_type;

-- ----------------------------
-- Table structure for m_area
-- ----------------------------
CREATE TABLE `m_area`
(
    `area_id`    varchar(32)  NOT NULL COMMENT '主键',
    `address`    varchar(256) NOT NULL COMMENT '地址',
    `prov`       varchar(48)  NULL COMMENT '省',
    `city`       varchar(48)  NULL COMMENT '市',
    `district`   varchar(48)  NULL COMMENT '区',
    `town`       varchar(128) NULL COMMENT '镇',
    `road`       varchar(256) NULL COMMENT '街道',
    `roads`      varchar(256) NULL     DEFAULT 0 COMMENT '路',
    `subroad`    varchar(256) NULL COMMENT '子路',
    `created_at` timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    `created_by` varchar(30)  NOT NULL DEFAULT 'System' COMMENT '做成者',
    `updated_at` timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `updated_by` varchar(30)  NOT NULL DEFAULT 'System' COMMENT '更新者',
    PRIMARY KEY (`area_id`)
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '区域管理表'
  ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for m_ai_model
-- ----------------------------
CREATE TABLE `m_ai_model`
(
    `model_id`   varchar(10)  NOT NULL COMMENT '主键',
    `name`       varchar(48)  NULL COMMENT '名称',
    `p_id`       varchar(10)  NOT NULL COMMENT 'PID(调用识别接口用)',
    `k_id`       varchar(48)  NULL COMMENT '知识库ID(调用创建文档接口用)',
    `d_id`       varchar(256) NULL COMMENT 'Doc ID(创建文档接口返回)',
    `created_at` timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    `created_by` varchar(30)  NOT NULL DEFAULT 'System' COMMENT '做成者',
    `updated_at` timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `updated_by` varchar(30)  NOT NULL DEFAULT 'System' COMMENT '更新者',
    PRIMARY KEY (`model_id`)
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = 'AI模型库字典'
  ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for m_ai_category
-- ----------------------------
CREATE TABLE `m_ai_category`
(
    `id`         int          AUTO_INCREMENT COMMENT '主键',
    `category`   varchar(100) NOT NULL COMMENT '上级分类',
    `text`       varchar(100) NOT NULL COMMENT '分类',
    `created_at` timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    `created_by` varchar(30)  NOT NULL DEFAULT 'System' COMMENT '做成者',
    `updated_at` timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `updated_by` varchar(30)  NOT NULL DEFAULT 'System' COMMENT '更新者',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = 'AI分类字典项'
  ROW_FORMAT = Compact;

-- ------------------------------------
-- //////////////dp_slave//////////////
-- ------------------------------------


-- ----------------------------
-- Table structure for t_${tableId}
-- ----------------------------
-- CREATE TABLE `t_tableId`
-- (
--     `id`           int         NOT NULL AUTO_INCREMENT,
--     `import_key`   char(5)     NOT NULL,
--     `md5`          varchar(32) NOT NULL,
--     `area_id`      varchar(32) NOT NULL,
--     `content`      json,
--     `deleted`      tinyint(1)  NOT NULL DEFAULT 0,
--     `created_by`   varchar(32) NOT NULL,
--     `updated_by`   varchar(32) NOT NULL,
--     `updated_time` timestamp   NOT NULL,
--     PRIMARY KEY (`id`),
--     index (`md5`)
-- ) ENGINE = InnoDB
--   CHARACTER SET = utf8mb4
--   COLLATE = utf8mb4_general_ci
--   ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for v_${displayId}
-- ----------------------------
-- CREATE TABLE `v_displayId`
-- (
--     `data_id`          int         NOT NULL,
--     `content`          json,
--     `correct_content`  json,
--     `debug_content`    json,
--     `updated_time`     timestamp   NOT NULL,
--     PRIMARY KEY (`data_id`),
-- ) ENGINE = InnoDB
--   CHARACTER SET = utf8mb4
--   COLLATE = utf8mb4_general_ci
--   ROW_FORMAT = Compact;
