<template>
  <div class="elx-split-screen-container">
    <div :style="{'width': leftWidth}" class="elx-split-screen-left">
      <slot name="left"/>
    </div>
    <div :style="{'width': 'calc(100vw - ' + leftWidth + ' - 165px)'}" class="elx-split-screen-right">
      <slot name="right"/>
    </div>
  </div>
</template>

<script>
import {defineComponent, h, computed, watch} from 'vue'

export default defineComponent({
  props: {
    leftWidth: {
      type: String,
      default: () => '200px'
    }
  },
  setup(props) {

  }
})

</script>

<style>
.elx-split-screen-container {
  display: block;
  height: auto;
}
.elx-split-screen-left {
  display: inline-block;
  float: left;
  height: auto;
  padding: 0;
}
.elx-split-screen-right {
  display: inline-block;
  float: right;
  height: auto;
  padding: 0;
}
</style>
