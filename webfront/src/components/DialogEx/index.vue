<template>
  <el-dialog v-model="dialogVisible" :title="title" :width="width" :close-on-click-modal="false" :close-on-press-escape="false" :top="top" append-to-body destroy-on-close draggable>
    <slot/>
    <template #footer>
      <div v-if="buttonAreaShow" class="dialog-footer">
        <slot name="customButton"/>
        <el-button v-if="cancelButton" class="ml-2" @click="handleClose">取消</el-button>
        <slot name="customButton2"/>
        <elx-loading-button v-if="okButton" :disabled="confirmButtonDisabled" type="primary" @click="done => handleConfirm(done)">
          <slot name="okButtonIcon"/>
          {{confirmText}}
        </elx-loading-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>

import {ref, defineComponent, watch} from 'vue'

export default defineComponent({
  props: {
    title: {
      type: String,
      required: true
    },
    width: {
      type: String,
      default: () => '900px'
    },
    buttonAlign: {
      type: String,
      default: () => 'right'
    },
    cancelButton: {
      type: Boolean,
      default: () => true
    },
    okButton: {
      type: Boolean,
      default: () => true
    },
    confirmText: {
      type: String,
      default: () => '确认'
    },
    confirmButtonDisabled: {
      type: Boolean,
      default: () => false
    },
    buttonAreaShow: {
      type: Boolean,
      default: () => true
    },
    top: {
      type: String,
      default: () => '15vh'
    }
  },
  emits: ['after-close'],
  setup(props, context) {
    const dialogVisible = ref(false)
    const closeCallback = ref()

    const open = (close) => {
      closeCallback.value = close
      dialogVisible.value = true
    }

    const openWithInit = (init, close) => {
      closeCallback.value = close
      dialogVisible.value = true
      init()
    }

    const close = () => {
      handleClose()
    }

    const handleConfirm = (done) => {
      closeCallback.value(done, () => {
        dialogVisible.value = false
        done()
      })
    }

    const handleClose = () => {
      dialogVisible.value = false
    }

    watch(() => dialogVisible.value, (nv) => {
      if (nv === false) {
        context.emit('after-close')
      }
    })

    return {
      dialogVisible,
      handleConfirm,
      handleClose,
      open,
      openWithInit,
      close
    }
  }
})
</script>
