<template>
  <div style="display: inline-block">
    <el-button ref="buttonRef" :disabled="disabled" v-click-outside="onClickOutside" icon="Menu"/>
    <el-popover ref="popoverRef" :virtual-ref="buttonRef" :width="180" trigger="click" title="列显示" virtual-triggering>
      <div>
        <div class="column-filter__content" style="max-height: 300px; overflow-y: scroll">
          <el-checkbox-group v-model="checkedColumns">
            <el-checkbox v-for="(column, index) in columns" :key="index" :label="column.id">
              {{ (index + 1) + ' ' + column.name + (score && column.score ? '(算分)' : '') }}
            </el-checkbox>
          </el-checkbox-group>
        </div>
        <div class="el-table-filter__bottom">
          <el-button type="" @click="handleAll(false)">全显示</el-button>
          <el-button type="" @click="handleAll(true)">全隐藏</el-button>
        </div>
        <div class="el-table-filter__bottom">
          <el-button v-if="score" type="" @click="handleOriginColumn(true)">隐藏原始数据列</el-button>
        </div>
      </div>
    </el-popover>
    <el-button ref="fixedButtonRef" :disabled="disabled" v-click-outside="onClickFixedOutside" icon="CaretBottom" class="w-4" style="margin-left: 1px !important; border-left: 0 !important;"/>
    <el-popover ref="fixedPopoverRef" :virtual-ref="fixedButtonRef" :width="180" trigger="click" title="列固定" virtual-triggering>
      <div>
        <div class="column-filter__content" style="max-height: 300px; overflow-y: scroll">
          <el-checkbox-group v-model="fixedColumns">
            <el-checkbox v-for="(column, index) in columns.filter(e => !e.score && e.hidden !== true)" :key="index" :label="index" @click="handleClick(index)">
              {{ (index + 1) + ' ' + column.name + (score && column.score ? '(算分)' : '') }}
            </el-checkbox>
          </el-checkbox-group>
        </div>
        <div class="el-table-filter__bottom">
          <el-button type="" @click="handleClearFixed()">取消固定</el-button>
        </div>
      </div>
    </el-popover>
  </div>
</template>

<script>
import { defineComponent, nextTick, ref, unref, computed, watch} from 'vue'
import { ClickOutside } from 'element-plus'

export default defineComponent({
  props: {
    columns: {
      type: Array,
      required: true
    },
    score: {
      type: Boolean,
      default: () => false
    },
    fixed: {
      type: Number,
      default: () => 0
    },
    disabled: {
      type: Boolean,
      default: () => false
    }
  },
  directives: {'click-outside': ClickOutside},
  setup(props, context) {


    const buttonRef = ref()
    const popoverRef = ref()
    const checkedColumns = computed({
      get() {
        return props.columns.filter(e => e.hidden !== true).map(e => e.id)
      },
      set(value) {
        props.columns.forEach(e => {
          e.hidden = value.indexOf(e.id) === -1
        })
      }
    })

    const onClickOutside = () => {
      unref(popoverRef).popperRef?.delayHide?.()
    }

    const handleAll = (hidden) => {
      props.columns.forEach(e => e.hidden = hidden)
    }

    const handleOriginColumn = () => {
      props.columns.forEach(e => {
        if (!e.score) {
          e.hidden = true
        }
      })
    }

    const fixedButtonRef = ref()
    const fixedPopoverRef = ref()

    const onClickFixedOutside = () => {
      unref(fixedPopoverRef).popperRef?.delayHide?.()
    }

    const fixedColumnIndex = ref(props.fixed)
    const fixedColumns = computed({
      get() {
        return props.columns.filter((e, index) => index < fixedColumnIndex.value).map((e, index) => index)
      },
      set(value) {
      }
    })

    const handleClick = (index) => {
      fixedColumnIndex.value = index + 1
    }

    const handleClearFixed = () => {
      fixedColumnIndex.value = -1
    }

    watch(() => fixedColumnIndex.value, (value) => {
      context.emit('update-fixed', value)
    })

    return {
      buttonRef,
      popoverRef,
      checkedColumns,
      fixedButtonRef,
      fixedPopoverRef,
      fixedColumns,
      fixedColumnIndex,
      handleOriginColumn,
      onClickOutside,
      handleAll,
      handleClick,
      onClickFixedOutside,
      handleClearFixed
    }
  }
})
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.column-filter__content {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px
  }
  &::-webkit-scrollbar-thumb {
    background-color: #0003;
    border-radius: 10px;
    transition: all .2s ease-in-out;
  }
  &::-webkit-scrollbar-track {
    border-radius: 10px;
  }
}
:deep(.el-checkbox__label) {
  min-width: 100px;
}
</style>
