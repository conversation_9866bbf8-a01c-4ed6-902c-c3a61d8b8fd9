<template>
  <el-card v-loading="searchLoading" class="table-panel-container" shadow="never">
    <template #header v-if="pageHeader">
      <el-page-header :class="{'header-has-back': !hasBack}" class="card-header" @back="goBack">
        <template #content>
          <svg-icon v-if="$route?.meta.icon" :name="$route?.meta.icon" style="height: 13px; width: 14px; margin-right: 10px;"/>
          <span class="font-bold-14 mr-3"> {{$route?.meta.title}} </span>
        </template>
        <template #extra>
          <slot name="extra"/>
        </template>
      </el-page-header>
    </template>

    <elx-button-bar style="display: inline-block; height: 40px; width: auto; float: left">
      <slot name="button-area-left"/>
    </elx-button-bar>
    <elx-button-bar style="display: inline-block; height: 40px; width: auto; float: right; text-align: right">
      <slot name="button-area-right"/>
    </elx-button-bar>
    <!-- <div style="display: inline-block; height: 40px; width: 35%; float: right">
      <el-input v-model="searchInput" placeholder="请输入内容" >
        <el-button slot="append" icon="el-icon-search"/>
      </el-input>
    </div>-->
    <el-table
        ref="tableRef"
        :data="dataList"
        :height="height"
        :row-class-name="rowClassName"
        fit
        :highlight-current-row="false"
        scrollbar-always-on
        :row-key="rowKey"
        :border="border"
        :indent="6"
        :row-style="rowStyle"
        :cell-style="cellStyle"
        :stripe="stripe"
        :show-overflow-tooltip="showOverflowTooltip"
        tooltip-effect="light"
        size="small"
        default-expand-all
        header-row-class-name="table-header"
        @row-dblclick="toggleRowExpansion"
        @sort-change="c => sortChange(c.column)"
        @selection-change="handleSelectionChange">
      <!-- @mousedown="startDrag" @mousemove="drag" @mouseup="stopDrag" @mouseleave="stopDrag" -->
      <slot/>
    </el-table>
    <div class="table-tips">
      <slot name="table-tips"/>
    </div>
    <el-pagination
        v-if="pagination !== null && pagination.totalCount && pagination.totalCount > 0"
        :current-page="pagination.pageNumber"
        :page-size="pagination.pageSize"
        :total="pagination.totalCount"
        :page-sizes="[20, 50, 100]"
        small
        background
        layout="total, sizes, prev, pager, next, jumper"
        style="float: right; margin: 10px 0"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"/>
    <div v-else style="height: 33px; display: block"/>
    <div>
      <slot name="extend"/>
    </div>
  </el-card>
</template>

<script>
import {computed, defineComponent, reactive, ref, toRefs, onMounted} from 'vue'
import {useRouter} from 'vue-router'
import {ElMessage} from 'element-plus'
import SvgIcon from '@/common/icons/index.vue';
export default defineComponent({
  components: {SvgIcon},
  props: {
    data: {
      type: Array,
      required: true
    },
    pagination: {
      type: Object,
      required: true
    },
    searchFunction: {
      type: Function,
      required: true
    },
    selectionCondition: {
      type: Function,
      default: () => (row) => false
    },
    searchLoading: {
      type: Boolean,
      required: true
    },
    pageHeader: {
      type: Boolean,
      default: () => false
    },
    hasBack: {
      type: Boolean,
      default: () => true
    },
    sortColumn: {
      type: Object,
      default: () => {}
    },
    height: {
      type: String,
      default: () => 'calc(100vh - 380px)'
    },
    rowClassName: {
      type: Function,
      default: () => ({ row, rowIndex }) => null
    },
    stripe: {
      type: Boolean,
      default: () => true
    },
    showOverflowTooltip: {
      type: Boolean,
      default: () => true
    },
    rowStyle: Function,
    cellStyle: Function,
    rowKey: [String, Function],
    border: Boolean
  },
  setup(props, context) {
    const dataList = computed(() => {
      props.data.forEach(d => { d.$selection = false })
      return props.data
    })
    const router = useRouter()
    const tableRef = ref()

    const state = reactive({
      toggleRowExpansion: (row) => {
        tableRef.value.toggleRowSelection(row, undefined)
      },
      handleSelectionChange: (rows) => {
        props.data.forEach(d => {
          if (props.selectionCondition(d) === false) {
            d.$selection = false
            tableRef.value.toggleRowSelection(d, false)
          } else {
            d.$selection = rows.indexOf(d) !== -1
          }
        })
      },
      handleCurrentChange: async(pageNumber) => {
        props.pagination.pageNumber = pageNumber
        context.emit('update:pagination', props.pagination)
        context.emit('update:searchLoading', true)
        await props.searchFunction(() => {})
        context.emit('update:searchLoading', false)
      },
      handleSizeChange: async(pageSize) => {
        props.pagination.pageSize = pageSize
        context.emit('update:pagination', props.pagination)
        context.emit('update:searchLoading', true)
        await props.searchFunction()
        context.emit('update:searchLoading', false)
      },
      sortChange: async(column) => {
        context.emit('update:sortColumn', column)
        props.pagination.pageNumber = 1
        context.emit('update:searchLoading', true)
        await props.searchFunction(() => {})
        context.emit('update:searchLoading', false)
      },
      goBack: () => {
        router.go(-1)
      },
      getSelectionRows: (isSingle, operate) => {
        return new Promise((resolve, reject) => {
          const selected = props.data.filter(e => e.$selection === true)
          if (selected.length === 0) {
            const message = isSingle === true ? '必须选择一条数据' : '请勾选数据。'
            ElMessage.error(message)
            reject()
          } else if (isSingle === true && selected.length > 1) {
            let opt = '操作'
            if (operate) {
              opt = operate
            }
            ElMessage.error('一次只能' + opt + '一条数据。')
            reject()
          } else {
            const data = isSingle === true ? selected[0] : selected
            resolve(data)
          }
        });
      }
    })

    // const scrollBarLeft = ref(0)
    // onMounted(() => {
    //   const tableEl = tableRef.value.$el.querySelector('.el-scrollbar__wrap')
    //   tableEl.addEventListener('scroll', () => {
    //     const scrollLeft = tableEl.scrollLeft
    //
    //     scrollBarLeft.value = scrollLeft
    //     context.emit('scroll-bar-left', scrollLeft)
    //   })
    // })
    //
    // let isDragging = false;
    // let dragX;
    // const startDrag = (e) => {
    //   const parent = e.target.closest('.draggable-box');
    //   if (parent && parent.classList.contains('drag')) return
    //   isDragging = true;
    //   dragX = e.pageX;
    //   const tableEl = tableRef.value.$el.querySelector('.el-scrollbar__wrap')
    //   scrollBarLeft.value = tableEl.scrollLeft;
    //   document.body.style.cursor = 'grabbing';
    // };
    //
    // const stopDrag = () => {
    //   isDragging = false;
    //   document.body.style.cursor = 'auto';
    // };
    //
    // const drag = (e) => {
    //   if (!isDragging) return;
    //   e.preventDefault();
    //   const tableEl = tableRef.value.$el.querySelector('.el-scrollbar__wrap')
    //   const dragWidth = tableRef.value.$el.querySelector('.el-scrollbar__view').clientWidth
    //   const x = e.pageX;
    //   const dx = x - dragX;
    //   dragX = e.pageX;
    //   tableEl.scrollLeft = scrollBarLeft.value - dx * dragWidth / window.innerWidth;
    // };

    return {
      dataList,
      tableRef,
      ...toRefs(state)
      // scrollBarLeft,
      // startDrag,
      // stopDrag,
      // drag
    }
  }
})

</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.table-panel-container {
  .card-header {
    margin-top: -5px !important;
    margin-bottom: -5px !important;
  }
}
.table-header {
  background: #99a9bf;
}
.table-tips {
  display: inline-block;
  min-height: 30px;
  float: left;
}
</style>

<style rel="stylesheet/scss" lang="scss">
.header-has-back {
  .el-page-header__back {
    display: none !important;
  }
  .el-divider {
    display: none !important;
  }
}
</style>
