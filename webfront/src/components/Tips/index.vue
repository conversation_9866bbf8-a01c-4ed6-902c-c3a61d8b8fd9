<template>
  <div class="introduction-container">
    <div v-for="(text, index) in helps" :key="index">
      <span>{{text}}</span>
    </div>
    <slot/>
  </div>
</template>

<script lang='ts' setup>
const props = defineProps<{
  helps?: Array<string>
}>();
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.introduction-container {
  display: block;
  margin-top: 4px;
  color: #939ea9;
  font-size: 12px;
  line-height: 20px;
}
</style>
