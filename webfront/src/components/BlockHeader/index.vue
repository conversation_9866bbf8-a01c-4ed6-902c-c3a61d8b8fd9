<template>
  <div class="block-header-container">
    <div class="block-header">
      <div class="block-header-title">
        {{title}}
        <slot name="title-extra"/>
      </div>
      <div class="block-header-extra">
        <slot name="extra"/>
      </div>
    </div>
    <slot/>
  </div>
</template>

<script lang='ts' setup>
const props = defineProps<{
  title: string
}>();

</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.block-header-container {

}
.block-header {
  margin: 0 0 15px 0;
  border-bottom: 1px solid var(--el-border-color-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.block-header-title {
  display: inline-block;
  width: auto;
  height: 32px;
  line-height: 30px;
  padding: 0 10px;
  border-bottom: 3px solid #324558;
  font-size: 14px;
  font-weight: 600;
  color: #6C757D;
}
.block-header-extra {
  display: inline-block;
  width: auto;
  height: 32px;
  line-height: 30px;
  padding: 0 10px;
}
</style>
