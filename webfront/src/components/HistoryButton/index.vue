<template>
  <div class="history-button-container mr-1.5">
    <el-button-group>
      <el-button :disabled="disabled" :icon="isImport ? 'Upload' : 'Download'" type="info" @click="handleAction">{{ isImport ? '导入' : '导出'}}</el-button>
      <el-dropdown v-if="type === 'export'" ref="dropdown" :disabled="disabled" trigger="click" @command="handleCommand">
        <el-button :disabled="disabled" type="info" icon="CaretBottom" class="w-4"></el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="history" icon="Tickets">{{ isImport ? '导入历史' : '导出历史'}}</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </el-button-group>
  </div>
</template>

<script setup>
import {ref, reactive, computed} from 'vue'

const props = defineProps({
  type: {
    type: String,
    required: true,
    validator(value) {
      return value === 'import' || value === 'export'
    }
  },
  disabled: {
    type: Boolean,
    default: () => false
  }
})

const isImport = computed(() => {
  return props.type === 'import'
})

const emit = defineEmits(['click','history'])
const handleAction = () => {
  emit('click')
}

const handleCommand = () => {
  emit('history')
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.history-button-container {
  display: inline-block;
  .el-button + .el-button {
    margin-left: 1px !important;
  }
}
</style>
