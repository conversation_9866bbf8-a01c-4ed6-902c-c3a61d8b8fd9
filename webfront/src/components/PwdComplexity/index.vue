<template>
  <section>
    <div class="input_span">
      <span id="one" ref="oneRef"/>
      <span id="two" ref="twoRef"/>
      <span id="three" ref="threeRef"/>
    </div>
    <div id="font">
      <span>Low</span>
      <span>Medium</span>
      <span>High</span>
    </div>
  </section>
</template>

<script>
import { ref, defineComponent, watch} from 'vue'

export default defineComponent({
  props: {
    password: {
      type: String,
      required: true,
      default: function() {
        return ''
      }
    }
  },
  setup(props) {
    const msgText = ref('')

    const oneRef = ref()
    const twoRef = ref()
    const threeRef = ref()

    watch(() => props.password, (newValue) => {
      msgText.value = checkStrong(newValue)
      if (msgText.value === 1) {
        oneRef.value.style.background = 'red'
      } else {
        oneRef.value.style.background = '#eee'
      }
      if (msgText.value === 2) {
        twoRef.value.style.background = 'orange'
      } else {
        twoRef.value.style.background = '#eee'
      }
      if (msgText.value === 3) {
        threeRef.value.style.background = '#00D1B2'
      } else {
        threeRef.value.style.background = '#eee'
      }
    })

    const checkStrong = (sValue) => {
      let modes = 0
      if (sValue.length < 1) return modes
      if (/\d/.test(sValue)) modes++
      if (/[a-z]/.test(sValue)) modes++
      if (/[A-Z]/.test(sValue)) modes++
      if (/[`~!@#$%^&*()_\-+=<>?:"{}|,.\/;'\[\]·]/.test(sValue)) modes++

      if (modes < 2 || sValue.length < 8) {
        return 1
      } else if (modes === 2 && sValue.length >= 8 && sValue.length <= 10) { return 2 } else {
        return 3
      }
    }

    return {
      msgText,
      oneRef,
      twoRef,
      threeRef
    }
  }
})
</script>

<style scoped>

.input_span span {
  display: inline-block;
  width: 30%;
  height: 10px;
  background: #eee;
  line-height: 20px;
}

#one {
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
  border-right: 0px solid;
  margin-left: 0px;
  margin-right: 3px;
}

#two {
  border-left: 0px solid;
  border-right: 0px solid;
  margin-right: 3px;
}

#three {
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
  border-left: 0px solid;
}

#font span {
  display: inline-block;
  width: 30%;
  text-align: center;
  font-size: small;
}
#font span:nth-child(1){
  color:red;
}
#font span:nth-child(2){
  color:orange;
}
#font span:nth-child(3){
  color:#00D1B2;
}
</style>
