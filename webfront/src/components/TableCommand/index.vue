<template>
  <el-dropdown v-if="permissionOperation.length > 0" trigger="hover" class="ml-2 elx-table-command-outer" style="vertical-align: -2px" @command="handleOperation($event)">
    <el-link class="more-icon" icon="More"> {{ buttonText }}</el-link>
    <template #dropdown>
      <el-dropdown-item
          v-for="(opt, index) in permissionOperation"
          :key="index"
          :command="opt.name"
          :divided="opt.divided"
          :icon="opt.icon"
          :disabled="opt.disabled && opt.disabled(row)">
        <div>{{ opt.name }}</div>
      </el-dropdown-item>
    </template>
  </el-dropdown>
</template>

<script>
import {defineComponent, h, computed, watch} from 'vue'
import { hasPermission } from '@/common/utils/permission'

export default defineComponent({
  props: {
    operationHandler: {
      type: Array,
      required: true
    },
    buttonText: {
      type: String,
      default: () => '更多操作'
    },
    row: {
      type: Object,
      required: true
    },
    rowIndex: {
      type: Number,
      default: () => 0
    }
  },
  setup(props) {
    const handleOperation = (command) => {
      const operation = props.operationHandler.filter(opt => opt.name === command)[0]
      operation.handler(props.row, props.rowIndex)
    }

    const show = computed(() => {

      return true
    })

    const permissionOperation = computed(() => {
      const permissionOperation = []
      props.operationHandler.forEach(e => {
        if (e.permission) {
          const permission = []
          if (e.permission instanceof Array) {
            permission.push(...e.permission)
          } else if (e.permission instanceof Function) {
            const p = e.permission()
            permission.push(...p)
          }
          if (hasPermission(permission)) {
            permissionOperation.push(e)
          }
        } else {
          permissionOperation.push(e)
        }
      })
      return permissionOperation
    })

    return {
      show,
      handleOperation,
      permissionOperation
    }
  }
})
</script>

<style rel="stylesheet/scss" lang="scss">
.elx-table-command-outer {
  .more-icon {
    .el-icon svg {
      transform: rotate(90deg)
    }
  }
}
.elx-table-command {
  .el-dropdown-menu__item {
    padding: 0;

    .dropdown-menu-item {
      padding: 0 20px 0 20px;
      width: 100%;
      user-select: none;

      &.danger-menu-item {
        color: #F56C6C;

        &:hover {
          color: #fff;
          background-color: #F56C6C;
        }
      }

      &.normal-menu-item {
        background-color: transparent;
      }
    }
  }
}
</style>
