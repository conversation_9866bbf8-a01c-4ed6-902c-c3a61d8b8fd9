<template>
  <elx-dialog-ex ref="FilterHistoryRef" :ok-button="false" title="历史过滤" width="1000px">
    <el-table v-loading="loading" :data="dataList" height="500px">
      <el-table-column prop="filterName" label="名称" show-overflow-tooltip width="150px"/>
      <el-table-column prop="filterText" label="条件" show-overflow-tooltip>
        <template #default="scope">
          <span>{{scope.row.filters.filterText}}</span>
        </template>
      </el-table-column>
      <el-table-column width="155px" align="right">
        <template #default="scope">
          <elx-loading-button type="danger" size="small" plain @click="done => handleDelete(scope.row, done)">删除</elx-loading-button>
          <el-button type="primary" size="small" plain @click="handleSelect(scope.row)">选择</el-button>
        </template>
      </el-table-column>
    </el-table>
  </elx-dialog-ex>
</template>

<script>
import {computed, defineComponent, reactive, watch, ref} from 'vue';
import {filter_history_api, delete_filter_api} from '@/api/workbench/table'
import { ElMessageBox } from 'element-plus'

export default defineComponent({

  setup(props) {
    const loading = ref(false)
    let tableId = ''
    let callback = null
    const dataList = ref([])

    const FilterHistoryRef = ref()
    const open = (_tableId, _callback) => {
      tableId = _tableId
      callback = _callback
      FilterHistoryRef.value.open()
      search()
    }

    const search = () => {
      loading.value = true
      filter_history_api(tableId).then(res => {
        dataList.value = res.data || []
      }).catch(e => {
      }).finally(() => {
        loading.value = false
      })
    }

    const handleSelect = (row) => {
      callback(row.filters)
      FilterHistoryRef.value.close()
    }

    const handleDelete = (row, done) => {
      ElMessageBox.confirm('确定要删除过滤条件【' + row.filterName + '】？', '确认', { type: 'warning' }).then(() => {
        delete_filter_api(tableId, row.id).then(res => {
          search()
        }).catch(e => {
        }).finally(() => {
          done()
        })
      }).catch(e => {
        done()
      })
    }

    return {
      loading,
      dataList,
      FilterHistoryRef,
      open,
      handleSelect,
      handleDelete
    }
  }
})
</script>
