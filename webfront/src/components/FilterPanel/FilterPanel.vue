<template>
  <div :class="{'filter-panel-border': border}" style="margin-top: -10px;">
    <el-form :model="filters" label-position="left" inline>
      <elx-block-header v-if="!hiddenTagFilter" title="条件过滤">
        <el-form-item v-if="isAdmin" label="所属区划">
          <el-cascader v-model="filters.area" :options="areaOptionList" :props="{checkStrictly: true}" style="width: 260px" clearable/>
        </el-form-item>
        <el-form-item v-if="!hiddenImportDate" label="导入时间">
          <el-date-picker
              v-model="filters.dateRange"
              type="daterange"
              range-separator="到"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              style="width: 280px"
          />
        </el-form-item>
        <!-- <el-form-item label="标签">
          <el-select-v2 v-model="filters.tags" :options="tagsOptions" multiple class="w-60" filterable clearable/>
        </el-form-item>-->
      </elx-block-header>
      <elx-block-header title="项目过滤编辑">
        <!-- <template #extra>
          <el-switch
              v-model="filters.itemFilterMode"
              inline-prompt
              inactive-value="1"
              active-value="2"
              inactive-text="普通模式"
              active-text="专业模式"
              style="&#45;&#45;el-switch-off-color: #15a675; &#45;&#45;el-switch-on-color: #141f29"
          />
        </template>-->
      </elx-block-header>
      <div v-if="filters.itemFilterMode === '1'" style="padding: 0 20px; min-height: 200px">
        <div v-for="(cond, index) in filters.itemFilters" :key="index">
          <div v-if="index !== 0" class="filter-relation">
            {{cond.relation === 'or' ? '或者' : '并且'}}
          </div>
          <el-card class="condition-card" shadow="never" style="margin-bottom: 10px;">
            <div>
              <el-tag disable-transitions style="margin-bottom: 5px"><b>条件{{index + 1}}: {{ cond.field.name }}</b></el-tag>
              <el-button type="danger" size="small" plain class="condition-delete-button" @click="handleRemove(index)">删除</el-button>
            </div>
            <hr class="hr-line"/>
            <div v-for="(row, i) in cond.items" :key="i">
              <el-switch
                  v-show="i !== 0"
                  v-model="row.operator"
                  inline-prompt
                  inactive-value="and"
                  active-value="or"
                  inactive-text="并且"
                  active-text="或者"
                  size="small"
                  style="--el-switch-off-color: #15a675; --el-switch-on-color: #15a675; margin-left: 30px"
              />
              <el-row :gutter="10">
                <el-col :span="1">
                </el-col>
                <el-col :span="5">
                  <el-select-v2 v-if="!cond.field.matchColumn" v-model="row.comparison" allow-create :options="originComparisonOptions" @change="comparisonChange(row)"/>
                  <el-select-v2 v-else v-model="row.comparison" allow-create :options="matchComparisonOptions" @change="comparisonChange(row)"/>
                </el-col>
                <el-col :span="14">
                  <elx-tag-input v-if="row.comparison === 'in' || row.comparison === 'not in'" v-model="row.values" placeholder="输入值"/>
                  <el-switch
                      v-else-if="row.comparison === 'work_day'"
                      v-model="row.value"
                      inline-prompt
                      inactive-value="0"
                      active-value="1"
                      inactive-text="否（周末）"
                      active-text="是（工作日）"
                      style="--el-switch-off-color: #141f29; --el-switch-on-color: #15a675"/>
                  <el-date-picker
                      v-else-if="row.comparison === 'date_range'"
                      v-model="row.values"
                      type="daterange"
                      value-format="YYYY-MM-DD"
                      range-separator="到"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"/>
                  <el-time-picker
                      v-else-if="row.comparison === 'time_range'"
                      v-model="row.values"
                      is-range
                      value-format="HH:mm:ss"
                      range-separator="到"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"/>
                  <el-input v-else-if="!cond.field.matchColumn" v-model="row.value" placeholder="输入值"/>
                  <el-select-v2 v-else v-model="row.value" :options="cond.field.matchOptions || []" clearable style="width: 400px"/>
                </el-col>
                <el-col :span="4" style="text-align: right; padding-top: 5px">
                  <el-button v-if="i !== 0 || cond.items.length > 1" size="small" type="danger" plain icon="Close" @click="handleRemoveItem(cond.items, i)"></el-button>
                  <el-button size="small" type="primary" plain icon="Plus" @click="handlePlusItem(cond.items, i)"></el-button>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </div>
        <el-popover :visible="popoverVisible" placement="right" trigger="click" width="280px">
          <template #reference>
            <el-button icon="Plus" size="small" type="primary" style="margin-top: 5px" @click="popoverVisible = true">添加条件</el-button>
          </template>
          <el-form :modal="fieldForm">
            <el-form-item label="数据列" prop="field">
              <el-select-v2 v-model="fieldForm.field" :options="itemOptions" size="small" class="w-200px"/>
            </el-form-item>
            <el-form-item v-if="filters.itemFilters.length > 0" label="关系" prop="relation">
              <el-switch
                  v-model="fieldForm.relation"
                  inline-prompt
                  inactive-value="and"
                  active-value="or"
                  inactive-text="并且"
                  active-text="或者"
                  style="--el-switch-off-color: #15a675; --el-switch-on-color: #15a675"/>
            </el-form-item>
          </el-form>
          <hr class="hr-line"/>
          <div style="float: right">
            <el-button size="small" @click="fieldForm.field = ''; popoverVisible = false">取消</el-button>
            <el-button size="small" :disabled="!fieldForm.field" type="primary" @click="handlePlus">确定</el-button>
          </div>
        </el-popover>
      </div>
      <div v-if="filters.itemFilterMode === '2'">
        <el-input type="textarea" :rows="9"></el-input>
      </div>
    </el-form>
    <!-- <elx-dialog-ex ref="fieldDialogRef" top="20vh" title="选择数据列" width="300px">
      <el-form :modal="fieldForm">
        <el-form-item label="数据列" prop="field">
          <el-select-v2 v-model="fieldForm.field" :options="itemOptions"/>
        </el-form-item>
      </el-form>
    </elx-dialog-ex>-->
    <filter-history-dialog ref="FilterHistoryDialogRef"/>
  </div>
</template>

<script>
import {computed, defineComponent, reactive, watch, ref} from 'vue'
import FilterHistoryDialog from './FilterHistoryDialog.vue'
import {useUserStore} from '@/common/store/modules/user'

export default defineComponent({
  props: {
    tableId: {
      type: String,
      default: () => ''
    },
    columns: {
      type: Array,
      required: true
    },
    filters: {
      type: Object,
      required: true
    },
    tagsOptions: {
      type: Array,
      required: true
    },
    hiddenTagFilter: {
      type: Boolean,
      default: () => false
    },
    hiddenImportDate: {
      type: Boolean,
      default: () => false
    },
    history: {
      type: Boolean,
      default: () => true
    },
    border: {
      type: Boolean,
      default: () => false
    },
    areaOptions: {
      type: Array,
      required: true
    }
  },
  components: {FilterHistoryDialog},
  setup(props, context) {

    const originComparisonOptions = [
      {label: '等于', value: '='}, {label: '不等于', value: '<>'},
      {label: '大于', value: '>'}, {label: '小于', value: '<'},
      {label: '大于等于', value: '>='}, {label: '小于等于', value: '<='},
      {label: '开头是', value: 'like _%'}, {label: '结尾是', value: 'like %_'},
      {label: '内容包含', value: 'like %_%'}, {label: '内容不包含', value: 'not like %_%'},
      {label: '包含', value: 'in'}, {label: '不包含', value: 'not in'},
      {label: '工作日', value: 'work_day'},
      {label: '日期区间', value: 'date_range'}, {label: '时间区间', value: 'time_range'}
    ]

    const matchComparisonOptions = [
      {label: '等于', value: '='},
      {label: '不等于', value: '<>'}
    ]

    const comparisonOptionsMap = {}
    originComparisonOptions.forEach(e => {
      comparisonOptionsMap[e.value] = e.label
    })

    const comparisonOptions = computed(() => (cond) => {
      if (cond.field.matchColumn === true) {
        return [
          {label: '等于', value: '='},
          {label: '不等于', value: '<>'}
        ]
      }
      return originComparisonOptions
    })

    props.filters.itemFilterMode = '1'

    watch(() => props.filters, (nv) => {
      const filterArray = []
      const itemFilterArray = []
      const filters = props.filters
      if (filters.itemFilterMode === '1') {
        //
        if (filters.dateRange && filters.dateRange.length > 0) {
          filterArray.push('导入时间: [' + filters.dateRange.join(' : ') + ']')
        }
        if (filters.tags && filters.tags.length > 0) {
          filterArray.push('标签: ' + filters.tags)
        }
        if (filters.area && filters.area.length > 0) {
          filterArray.push('所属区划: [' + filters.area.join(' / ') + ']')
        }
        if (filters.itemFilters && filters.itemFilters.length > 0) {
          const length = filters.itemFilters.length

          filters.itemFilters.forEach((f, index) => {
            let item = ''
            if (index !== 0) {
              item += f.relation === 'or' ? ' 或者 ' : ' 并且 '
            }
            if (length > 1) {
              item += '('
            }
            item += (f.field.name + ': ')
            f.items.forEach((ff, i) => {
              if (i !== 0) {
                item += ff.operator === 'and' ? ' 并且 ' : ' 或者 '
              }
              item += comparisonOptionsMap[ff.comparison]
              if (ff.comparison === 'in' || ff.comparison === 'not in') {
                item += ('[' + ff.values.join(',') + ']')
              } else if (ff.comparison === 'work_day') {
                item += ('[' + (ff.value === '1' ? '是' : '否') + ']')
              } else if (ff.comparison === 'date_range' || ff.comparison === 'time_range') {
                item += ('[' + ff.values.join(' 到 ') + ']')
              } else {
                item += ('"' + ff.value + '"')
              }
            })
            if (length > 1) {
              item += ')'
            }
            if (item !== '') {
              itemFilterArray.push(item)
            }
          })
        }
      } else {
        // TODO
      }
      let filterText = ''
      if (filterArray.length > 0) {
        filterText = filterArray.join(' 并且 ')
        if (itemFilterArray.length > 0) {
          filterText += ' 并且 ('
        }
      }
      if (itemFilterArray.length > 0) {
        filterText += itemFilterArray.join(' ')
      }
      if (filterArray.length > 0 && itemFilterArray.length > 0) {
        filterText += ')'
      }
      filters.filterText = filterText
      context.emit('update:filters', filters)
    }, {immediate: true, deep: true})

    const itemOptions = computed(() => {
      return props.columns.map(e => {
        return {label: e.name, value: e.id}
      })
    })

    const popoverVisible = ref(false)
    const handlePlus = () => {
      const field = fieldForm.field

      const column = props.columns.filter(e => e.id === field)[0]
      props.filters.itemFilters.push({
        field: {
          id: field,
          name: column.name,
          matchColumn: column.match,
          matchOptions: column.match ? column.matchOptions : []
        },
        relation: fieldForm.relation,
        items: [
          {
            operator: '1',
            comparison: '=',
            value: '',
            values: []
          }
        ]
      })
      fieldForm.field = ''
      fieldForm.relation = 'and'
      popoverVisible.value = false
    }

    const handlePlusItem = (items, index) => {
      items.splice(index + 1, 0, {
        operator: '1',
        comparison: '=',
        value: '',
        values: []
      })
    }

    const comparisonChange = (row) => {
      row.value = ''
      row.values.length = 0
      row.values = []
    }

    const handleRemove = (index) => {
      props.filters.itemFilters.splice(index, 1)
    }

    const handleRemoveItem = (items, index) => {
      items.splice(index, 1)
    }

    const fieldForm = reactive({
      field: '',
      relation: ''
    })

    const FilterHistoryDialogRef = ref()
    const handleFilterHistory = () => {
      FilterHistoryDialogRef.value.open(props.tableId, (filters) => {
        Object.assign(props.filters, filters)
      })
    }

    const areaOptionList = computed(() => {
      return [{label: '空白', value: '空白'}].concat(props.areaOptions)
    })

    const store = useUserStore()
    const isAdmin = computed(() => {
      return store.userType === '0' ||  store.userType === '1'// admin
    })
    return {
      itemOptions,
      comparisonOptions,
      matchComparisonOptions,
      originComparisonOptions,
      areaOptionList,
      isAdmin,
      fieldForm,
      popoverVisible,
      FilterHistoryDialogRef,
      comparisonChange,
      handlePlus,
      handlePlusItem,
      handleRemove,
      handleRemoveItem,
      handleFilterHistory
    }
  }
})
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
:deep(.el-card__body) {
  padding: 10px !important;
}
.condition-delete-button {
  float: right;
  visibility: hidden;
}
.condition-card:hover {
  .condition-delete-button {
    visibility: visible;
  }
}
.filter-relation {
  display: block;
  width: 35px;
  height: 22px;
  line-height: 22px;
  border: 1px solid #e4e7ed;
  margin: -18px -20px;
  background: #e8f6f1;
  float: left;
  text-align: center;
  color: #15a675;
  font-weight: bold;
  border-radius: 4px;
}
.filter-panel-border {
  border: 1px solid var(--border-color);
  padding: 10px;
}
</style>
