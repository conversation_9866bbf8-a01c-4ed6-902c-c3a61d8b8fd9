<template>
  <div class="el-input">
    <div class="el-input__wrapper elx-tag-input__wrapper thin-scroll-bar">
      <div class="elx-tag-input">
        <el-tag
            v-for="tag in dynamicTags"
            :key="tag"
            class="mx-1"
            closable
            :disable-transitions="false"
            @close="handleClose(tag)"
        >
          {{ tag }}
        </el-tag>
        <el-input
            v-show="inputVisible"
            ref="TagInputRef"
            v-model="inputValue"
            class="ml-1 w-30"
            size="small"
            @keyup.enter="handleInputConfirm"
            @blur="handleInputConfirm"
        />
        <el-button v-show="!inputVisible && readonly === false" class="button-new-tag ml-1" size="small" style="vertical-align: 0.5px" @click="showInput">
          + 添加
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { nextTick, ref, defineComponent, watch, computed} from 'vue'
import { useFormItem } from 'element-plus'

export default defineComponent({
  props: {
    modelValue: {
      type: Array
    },
    maxNumber: {
      type: Number,
      default: () => -1
    },
    readonly: {
      type: Boolean,
      default: () => false
    },
    allowRepeat: {
      type: Boolean,
      default: () => true
    }
  },
  setup(props, context) {

    const inputValue = ref('')

    const dynamicTags = computed({
      get() {
        return props.modelValue
      },
      set(value) {
      }
    })

    const { formItem } = useFormItem()
    watch(() => dynamicTags, (value) => {
      context.emit('change', value)
      context.emit('update:modelValue', value)
      context.emit('input', value)
      if (formItem) {
        formItem.validate('change', value).catch(() => {})
      }
    }, {deep: true})

    const inputVisible = ref(false)
    const TagInputRef = ref()


    const handleClose = (tag) => {
      dynamicTags.value.splice(dynamicTags.value.indexOf(tag), 1)
    }

    const showInput = () => {
      inputVisible.value = true
      nextTick(() => {
        if (TagInputRef.value) {
          TagInputRef.value.input.focus()
        }
      })
    }

    const handleInputConfirm = () => {
      if (inputValue.value) {
        dynamicTags.value.push(inputValue.value)
      }
      inputVisible.value = false
      inputValue.value = ''
    }

    return {
      TagInputRef,
      dynamicTags,
      inputValue,
      inputVisible,
      handleClose,
      showInput,
      handleInputConfirm
    }
  }
});
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.elx-tag-input__wrapper {
  padding: 0 3px 1px 3px !important;
  min-height: 28px;
  max-height: 122px;
  overflow-y: auto;
}
.elx-tag-input {
  width: 100%;
  //height: 30px;
  //line-height: 30px;
  //:deep(.el-button) {
  //
  //}
}
</style>
