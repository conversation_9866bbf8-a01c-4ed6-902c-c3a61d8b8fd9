<template>
  <el-card class="page-card-container" shadow="never">
    <template #header>
      <div class="card-header">
        <el-page-header @back="goBack">
          <template #content>
            <span class="font-600 mr-3" style="font-size: 14px; color: #6C757D"> {{$route?.meta.title}} </span>
          </template>
          <template #extra>
            <el-affix :offset="60">
              <slot name="extra"/>
              <elx-loading-button v-if="saveButton && savePermission" type="primary" :plain="saveButtonPlain" icon="Select" style="margin: 0 10px" @click="(done) => handleSave(done)">保存</elx-loading-button>
              <slot name="extra-button"/>
            </el-affix>
          </template>
        </el-page-header>
      </div>
    </template>
    <slot/>
  </el-card>
</template>

<script setup>

import {useRouter} from "vue-router"
const $emit = defineEmits(['save','cancel'])
const props = defineProps({
  saveButton: {
    type: Boolean,
    default: () => true
  },
  saveButtonPlain: {
    type: Boolean,
    default: () => false
  },
  savePermission: {
    type: Boolean,
    default: () => true
  }
})
const router = useRouter()

const goBack = () => {
  router.go(-1)
}

const handleSave = (done) => {
  $emit('save', done)
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.page-card-container {
  .card-header {
    margin-top: -5px !important;
    margin-bottom: -5px !important;
  }
}
</style>
