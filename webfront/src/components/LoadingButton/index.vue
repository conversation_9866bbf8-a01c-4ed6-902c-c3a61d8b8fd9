<template>
  <el-button :type="type" :icon="icon" :circle="circle" :plain="plain" :autofocus="autofocus" :disabled="disabled" :loading="loading || innerLoading" :size="size" @click="handleChick">
    <span v-if="$slots.default && !(loadingNoText && (loading || innerLoading))">
      <slot/>
    </span>
  </el-button>
</template>

<script>
import { defineComponent, ref, watch } from 'vue'
export default defineComponent({
  props: {
    type: {
      type: String,
      default: function() {
        return 'primary'
      }
    },
    icon: {
      type: String,
      default: function() {
        return ''
      }
    },
    disabled: {
      type: <PERSON><PERSON><PERSON>,
      default: function() {
        return false
      }
    },
    circle: {
      type: Boolean,
      default: function() {
        return false
      }
    },
    size: {
      type: String,
      default: function() {
        return ''
      }
    },
    autofocus: {
      type: <PERSON>olean,
      default: function() {
        return false
      }
    },
    plain: {
      type: Boolean,
      default: function() {
        return false
      }
    },
    loadingNoText: {
      type: <PERSON><PERSON><PERSON>,
      default: function() {
        return false
      }
    },
    loading: {
      type: Boolean,
      default: function() {
        return false
      }
    }
  },
  emits: ['click'],
  setup(props, context) {
    const innerLoading = ref(false)

    const handleChick = (evt) => {
      innerLoading.value = true
      context.emit('click', function() {
        setTimeout(function() {
          innerLoading.value = false
        }, 300)
      }, evt)
    }

    watch(() => props.loading, (nv) => {
      if (nv === false) {
        innerLoading.value = false
      }
    })

    return {
      innerLoading,
      handleChick
    }
  }
})
</script>
