<template>
  <el-upload
      ref="uploadControl"
      action=""
      :show-file-list="false"
      :accept="accept"
      :http-request="f => uploadFunc(f.file)"
      :on-success="() => {}"
      :on-error="() => {}"
      :before-upload="() => {}"
      style="display: inline-block; vertical-align: -3px;">
    <el-button :size="size" :type="type" :plain="plain" :icon="icon">
      <slot/>
    </el-button>
  </el-upload>
</template>

<script>

import {defineComponent} from 'vue';

export default defineComponent({
  name: 'ElxUploadButton',
  props: {
    uploadFunc: {
      type: Function,
      required: true
    },
    accept: {
      type: String,
      default: () => ''
    },
    type: {
      type: String,
      default: () => ''
    },
    plain: {
      type: Boolean,
      default: () => false
    },
    icon: {
      type: String,
      default: () => ''
    },
    size: {
      type: String,
      default: () => ''
    }
  },
  setup() {

  }
})
</script>
