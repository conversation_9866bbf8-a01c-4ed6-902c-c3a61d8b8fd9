<template>
  <div class="filter-button-container mr-1.5">
    <el-button-group>
      <elx-loading-button :disabled="disabled" type="info" icon="Refresh" class="w-10" @click="handleSearch"></elx-loading-button>
      <el-dropdown ref="dropdown" :disabled="disabled" trigger="click" @command="handleCommand">
        <el-button type="info" :disabled="disabled" :icon="filterAction === true ? 'Filter' : 'CaretBottom'" class="w-4"></el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="filter" icon="Filter">过滤条件</el-dropdown-item>
            <el-dropdown-item command="clear" icon="Close">清除过滤</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </el-button-group>
    <elx-dialog-ex ref="filterDialogRef" title="数据筛选" confirm-text="过滤" width="800px">
      <elx-filter-panel :table-id="tableId" :columns="columns" :filters.sync="filters" :tags-options="tagsOptions" :area-options="areaOptions" :hidden-import-date="hiddenImportDate" :hidden-tag-filter="hiddenTagFilter"/>
      <template #customButton2>
        <el-button @click="handleFilterCancel()">
          <svg-icon name="Nofilter" class="item-icon"/>
          取消过滤
        </el-button>
      </template>
      <template #okButtonIcon>
        <svg-icon name="filter" class="item-icon" style="vertical-align: -3px; width: 15px; height: 15px"/>
      </template>
    </elx-dialog-ex>
    <elx-dialog-ex ref="saveFilterDialog" title="保存过滤条件" confirm-text="保存" width="500px">
      <el-form ref="saveFilterFormRef" :model="saveFilterForm" label-width="80px" label-position="top">
        <el-form-item label="名称" prop="filterName" :rules="[{required: true, message: '请输入名称', trigger: 'blur'}]" >
          <el-input v-model="saveFilterForm.filterName"/>
        </el-form-item>
        <el-form-item label="过滤条件">
          <div v-html="getFilterText()" class="filter-text-div thin-scroll-bar">
          </div>
        </el-form-item>
      </el-form>
    </elx-dialog-ex>
  </div>
</template>

<script setup>
import {save_filter_api} from '@/api/workbench/table'
import {ref, reactive} from 'vue'

const props = defineProps({
  tableId: {
    type: String,
    required: true
  },
  columns: {
    type: Array,
    required: true
  },
  filters: {
    type: Object,
    required: true
  },
  tagsOptions: {
    type: Array,
    default: () => []
  },
  areaOptions: {
    type: Array,
    required: true
  },
  hiddenTagFilter: {
    type: Boolean,
    default: () => false
  },
  hiddenImportDate: {
    type: Boolean,
    default: () => false
  },
  disabled: {
    type: Boolean,
    default: () => false
  }
})

const filterAction = ref(false)

const filterDialogRef = ref()
const handleCommand = (action) => {
  if (action === 'filter') {
    filterDialogRef.value.open((done, close) => {
      handleFilter(true, false)
      done()
      close()
    })
  } else if (action === 'clear') {
    handleFilter(false, false)
  }
}

const handleFilterCancel = () => {
  handleFilter(false, false)
  filterDialogRef.value.close()
}

const emit = defineEmits(['filter','cancel'])

const handleFilter = (_filterAction) => {
  filterAction.value = _filterAction
  handleSearch(() => {})
}

const handleSearch = (done) => {
  emit('filter', done, filterAction.value)
}

const saveFilterForm = reactive({
  filterName: ''
})

const saveFilterDialog = ref()
const saveFilterFormRef = ref()
const handleSaveFilter = (tableId) => {
  saveFilterForm.filterName = ''
  if (props.filters.filterText !== '') {
    saveFilterDialog.value.open((done, close) => {
      saveFilterFormRef.value.validate(valid => {
        if (valid) {
          save_filter_api(tableId, saveFilterForm.filterName, props.filters).then(res => {
            close()
          }).catch(e => {
            done()
          })
        } else {
          done()
        }
      })
    })
  }
}
const getFilterText = () => {
  if (props.filters.filterText) {
    return props.filters.filterText.split('|').map((e, i) => (i + 1) + '. ' + e).join('<br/>')
  }
  return ''
}

defineExpose({
  handleCommand,
  handleSaveFilter
})
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.filter-button-container {
  display: inline-block;
  .el-button + .el-button {
    margin-left: 1px !important;
  }
}
.item-icon {
  height: 18px;
  width: 18px;
}
.filter-text-div {
  border: 1px solid var(--border-color);
  padding: 5px;
  min-height: 100px;
  max-height: 150px;
  overflow: auto;
  background-color: #ebeeef;
  div {
    word-break: keep-all;
  }
}
</style>
