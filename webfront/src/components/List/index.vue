<template>
  <div class="elx-list-panel">
    <div v-if="title" class="elx-list-panel__header">
      <div style="display: inline-block">
        <svg-icon v-if="icon !== ''" :name="icon" style="vertical-align: -5px; width: 12px; height: 12px; margin-right: 5px"/>
        <span>{{title}}</span>
        <span class="elx-list-title__describe">{{describe ? '(' + describe + ')' : ''}}</span>
      </div>
      <div style="display: inline-block; padding-right: 5px">
        <slot name="title-toolbar"/>
      </div>
    </div>
    <el-input v-model="filterText" v-if="filter" size="small" prefix-icon="Search" style="margin: 10px; width: calc(100% - 20px)"/>
    <div class="elx-list-panel__body">
      <div :style="{'height': filter ? 'calc(100% - 80px)' : 'calc(100% - 35px)'}"
           draggable="false" class="elx-list-panel__list"
           @dragstart="handleParentDragStart"
           @dragenter="handleParentEnter"
           @dragover.prevent="e => handleParentDragOver(e, -1)"
           @drop.prevent="e => handleParentDrop(e)">
        <div v-for="(item, index) in data" :key="index" :draggable="drag" :style="{'display': displayFilter(item, filterText) ? '' : 'none'}" class="elx-list_item"
             @dragstart="e => handleDragStart(e, index)"
             @drop.prevent="e => handleDrop(e, index)"
             @dragover.prevent="e => handleDragOver(e, index)"
             @dragenter="e => handleEnter(e, index)">
          <slot name="list-item" :item="item" :size="data.length" :index="index"/>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, ref, computed } from 'vue'
import SvgIcon from "@/common/icons/index.vue"
export default defineComponent({
  props: {
    data: {
      type: Array,
      required: true
    },
    title: {
      type: String,
      default: () => ''
    },
    describe: {
      type: String,
      default: () => ''
    },
    filter: {
      type: Boolean,
      default: () => false
    },
    filterFunc: {
      type: Function,
      default: () => (e) => {return true}
    },
    icon: {
      type: String,
      default: () => ''
    },
    dragKey: {
      type: String,
      default: () => '',
    },
    drag: {
      type: String,
      default: () => 'false'
    },
    dragSort: {
      type: Boolean,
      default: () => true
    }
  },
  components: { SvgIcon },
  emits: ['dragChange'],
  setup(props, context) {

    const key = '_key_'

    const filterText = ref('')

    const displayFilter = computed(() => (item, filterText) => {
      if (!props.filter) {
        return true
      }
      return props.filterFunc(item, filterText)
    })

    const handleDragStart = (e, index) => {
      e.dataTransfer.setData(key, JSON.stringify({key: props.dragKey, index: index}));
    };

    const handleEnter = () => {
    }

    const handleDragOver = (e, index) => {
      e.preventDefault();
    };

    const handleDrop = (e, index) => {
      if (/*props.drag === 'false' || */props.dragSort === false) {
        e.stopPropagation()
        return
      }
      const text = e.dataTransfer.getData(key)
      const data = JSON.parse(text)
      if (props.dragKey === data.key) {
        const changeItem = props.data.splice(data.index, 1)[0];
        props.data.splice(index, 0, changeItem);
      } else {
        context.emit('dragChange', {
          fromKey: data.key, key: props.dragKey, fromIndex: data.index, index: index
        })
      }
      e.stopPropagation()
    };

    const handleParentDragStart = (e) => {
    }

    const handleParentEnter = (e) => {
    }

    const handleParentDragOver = (e) => {
      e.preventDefault();
    }

    const handleParentDrop = (e) => {
      const text = e.dataTransfer.getData(key)
      const data = JSON.parse(text)
      if (props.dragKey === data.key) {
        const changeItem = props.data.splice(data.index, 1)[0];
        props.data.push(changeItem)
      } else {
        context.emit('dragChange', {
          fromKey: data.key, key: props.dragKey, fromIndex: data.index, index: -1
        })
      }
      e.stopPropagation()
    }

    return {
      handleDrop,
      handleDragStart,
      handleDragOver,
      handleEnter,
      handleParentDragStart,
      handleParentEnter,
      handleParentDragOver,
      handleParentDrop,
      filterText,
      displayFilter
    }
  }
})
</script>

<style rel="stylesheet/scss" lang="scss" scoped>

.elx-list-panel {
  overflow: hidden;
  background: var(--el-bg-color-overlay);
  display: inline-block;
  text-align: left;
  vertical-align: middle;
  max-height: 100%;
  min-height: 150px;
  min-width: 160px;
  box-sizing: border-box;
  position: relative;
  border: 1px solid #dcdfe6;
}

.elx-list-panel__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 30px;
  background: #f5f7fa;
  margin: 0;
  box-sizing: border-box;
  font-size: 14px;
  color: #6C757D;
  font-weight: bold;
  padding-left: 10px;
}
.elx-list-title__describe {
  margin-left: 15px;
  font-size: 10px;
  color: #6C757D;
  font-weight: normal;
}
.elx-list-panel__body {
  height: 100%;
  overflow: hidden;
}
.elx-list-panel__list {
  margin: 5px 0 0 0;
  list-style: none;
  overflow-y: auto;
  box-sizing: border-box;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px
  }
  &::-webkit-scrollbar-thumb {
    background-color: #0003;
    border-radius: 10px;
    transition: all .2s ease-in-out;
  }
  &::-webkit-scrollbar-track {
    border-radius: 10px;
  }
}
.elx-list_item {
  min-height: 27px;
  //line-height: 27px;
  width: calc(100% - 10px);
  padding-left: 5px;
  display: block;
}
.elx-list_item:hover {
  color: var(--el-color-primary);
  background-color: #ecf5ff;
}
</style>
