// import dark theme
// @use "element-plus/theme-chalk/src/dark/css-vars.scss" as *;

// :root {
//   --ep-color-primary: red;
// }
:root {
  $--el-border-radius-base: 0px !default;
  --border-color: #e4e7ed;
  --primary-color: #409eff;
}

body {
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB",
    "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
}

a {
  color: var(--ep-color-primary);
  text-decoration: none;
}

code {
  border-radius: 2px;
  padding: 2px 4px;
  background-color: var(--ep-color-primary-light-9);
  color: var(--ep-color-primary);
}

.thin-scroll-bar {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px
  }
  &::-webkit-scrollbar-thumb {
    background-color: #0003;
    border-radius: 10px;
    transition: all .2s ease-in-out;
  }
  &::-webkit-scrollbar-track {
    border-radius: 10px;
  }
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}
input[type="number"]{
  -moz-appearance: textfield;
}

input:-webkit-autofill {
  box-shadow: 0 0 0 1000px #FFF inset !important;
}
input:-webkit-autofill:focus {
  box-shadow: 0 0 0 1000px #FFF inset !important;
}
