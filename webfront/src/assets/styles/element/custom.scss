// only scss variables
.el-select-v2__wrapper,
.el-textarea__inner,
.el-input__wrapper {
  border-radius: 0 !important;
}

//.el-input__wrapper {
//  padding-top: 0 !important;
//  padding-bottom: 0 !important;
//}

@mixin el-table__header {
  .el-table__header {

    th {
      background-color: #f5f7fa !important;
      color: #666 !important;
      background-size: cover;
    }
  }
}

.el-table__header-wrapper {
  .complex-header-column.el-table__cell {
    border-bottom: 0 !important;

    &:before {
      content: "";
      position: absolute;
      left: 0;
      bottom: 0;
      right: 15px;
      height: 1px !important;
      background-color: #ccc;
    }
  }

  @include el-table__header
}

.el-page-header__title {
  font-size: 13px;
  margin-top: 4px;
  color: #6C757D;
}
.el-table__fixed-header-wrapper {
  @include el-table__header
}

.el-form-item__label {
  font-weight: bold;
  color: #324558;
}

.el-form-item__content {
  display: block !important;
}

.el-form-item.is-error {
  .el-select-v2__wrapper {
    border: 0 !important;
  }
}

.el-table-item__error, .el-span__error {
  color: var(--el-color-danger);
  font-size: 12px;
  line-height: 1;
  padding-top: 2px;
  left: 0;
}

.table-item__error-input {
  .el-input__wrapper {
    box-shadow: 0 0 0 1px var(--el-color-danger) inset;
  }
}

.el-table__empty-block {
  //justify-content: start !important;
  .el-table__empty-text {
    width: auto !important;
    //margin-left: 10px !important;
  }
  padding-top: 10px !important;
  height: 30px !important;
}

.font-bold-14 {
  font-size: 14px;
  color: #6C757D;
  font-weight: bold;
}

.font-bold-12 {
  font-size: 12px;
  color: #6C757D;
  font-weight: bold;
}

.font-remark {
  font-size: 12px;
  line-height: 1.5;
  margin-top: 8px;
  height: 68px;
  overflow: hidden;
  word-wrap: break-word;
  color: #939ea9;
}

.el-message-box {
  .el-message-box__header {
    background: #f5f7fa;
    height: 18px;
    line-height: 18px;
    box-shadow: 0 1px 1px rgba(0,21,41,.08);
  }
  .el-message-box__title {
    font-size: 16px;
  }
  .el-message-box__message{
    word-break:break-all;
    word-wrap:break-word;
  }
}

.el-dialog {
  transform: none;
  //left: 0;
  //position: relative;
  //margin: 0 auto;
  .el-dialog__header {
    height: 30px;
    line-height: 30px;
    padding-top: 5px;
    background: #f5f7fa;
    margin-right: 0;
    box-shadow: 0 1px 1px rgba(0,21,41,.08);
  }
  .el-dialog__headerbtn {
    padding-top: 0;
    top: 1px;
  }
  .el-dialog__title {
    font-size: 16px;
    vertical-align: -5px;
  }
  .el-dialog__body {
    padding: 20px 20px;
    color: #606266;
    font-size: 12px;
  }
  .el-dialog__footer {
    border-top: 1px solid var(--el-border-color-light);
    padding: 8px 20px !important;
  }
}

.el-button {
  border-radius: 0 !important;
  &.is-round {
    border-radius: 20px !important;
  }
}

.el-input-group__append {
  border-radius: 0 !important;
}

.el-tag {
  border-radius: 0 !important;
}

.hr-line {
  background-color: var(--border-color);
  height:1px;
  border:none;
}

.popper-no-arrow {
  .el-popper__arrow {
    display: none !important;
  }
  .el-dropdown-menu__item {
    line-height: 20px !important;
    padding: 2px 10px !important;
  }
  .el-dropdown-menu__item--divided {
    margin: 3px 0;
  }
}

.score-column {
  .cell {
    color: #21ba45 !important;
  }
  color: #21ba45 !important;
}
.summary-column {
  .cell {
    color: #f2711c !important;
  }
  color: #f2711c !important;
}

.search-button-append {
  .el-input-group__append:hover {
    background-color: var(--primary-color) !important;
  }

  .el-input-group__append {
    background-color: var(--primary-color) !important;
    color: #FFF;
    border: 1px solid var(--primary-color) !important;
    padding: 0 20px !important;
  }
}

// 去除下拉框焦点的黑框
.custom-dropdown:focus-visible {
  outline: unset;
}

.table-column-notice {
  vertical-align: -3px;
  margin-left: 3px;
}

.score_formula__title {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 10px;
}

.el-table {
  .el-popper {
    max-width: 800px !important;
    border-color: #000;

    .el-popper__arrow::before {
      border-bottom-color: #000 !important;
      border-right-color: #000 !important;
      right: 0;
    }
  }
}

.el-descriptions__content {
  max-width: 300px !important;
}

.match-label-column {
  .cell {
    color: #21ba45 !important;
  }
}

.match-column {
  &.cell-item, .cell {
    color: #f2711c !important;
  }
}

.debug-match-column {
  vertical-align: top !important;
}

.fix-column {
  &.cell-item, .cell {
    color: #21ba45 !important;
  }
}
