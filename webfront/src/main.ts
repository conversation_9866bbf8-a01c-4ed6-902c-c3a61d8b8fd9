import { createApp } from 'vue'
import type { Directive } from 'vue'

import App from './App.vue'
// import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// import "element-plus/dist/index.css"
import "@/assets/styles/index.scss"
// import "@/assets/styles/element/index.scss"

// import locale from 'element-plus/lib/locale/lang/zh-cn'
import 'virtual:svg-icons-register'

import svgIcon from '@/common/icons/index.vue'

import store from '@/common/store'
import router from '@/common/router'
import '@/common/router/interceptor'
import * as directives from '@/common/directive'
import { hasPermission } from '@/common/utils/permission'
import "uno.css"

import * as CustomComponentVue from '@/components'

const app = createApp(App)

app.use(store)
app.use(router)
// app.use(ElementPlus, { locale: locale, zIndex: 3000 })

for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

for (const [key, component] of Object.entries(CustomComponentVue)) {
  app.component(key, component)
}
app.component('svg-icon', svgIcon)

Object.keys(directives).forEach(key => {
  app.directive(key, (directives as { [key: string ]: Directive })[key])
})

app.config.globalProperties.hasPermission = hasPermission

router.isReady().then(() => {
  app.mount("#app")
})
