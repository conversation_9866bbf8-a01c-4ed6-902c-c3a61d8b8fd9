<template>
  <el-config-provider :locale="locale" :zIndex="3000">
    <router-view v-if="isRouterAlive"/>
  </el-config-provider>
</template>

<script>
import { defineComponent, ref, provide, nextTick } from 'vue'
import zhCn from 'element-plus/lib/locale/lang/zh-cn'

export default defineComponent({
  setup() {
    const isRouterAlive = ref(true)

    const reload = () => {
      isRouterAlive.value = false
      nextTick(() => {
        isRouterAlive.value = true
      })
    }
    provide('router-reload', reload)
    return {
      reload,
      isRouterAlive,
      locale: zhCn,
    }
  },
})
</script>

<style rel="stylesheet/scss" lang="scss">
body {
  margin: 0;
}
</style>
