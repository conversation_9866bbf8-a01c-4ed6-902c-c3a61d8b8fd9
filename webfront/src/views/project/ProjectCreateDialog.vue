<template>
  <elx-dialog-ex ref="dialog" title="创建项目" confirm-text="保存" width="600px">
    <div v-loading="loading">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" label-position="left">
        <el-form-item label="项目名称" prop="projectName">
          <el-input v-model="form.projectName"/>
        </el-form-item>
        <el-form-item label="项目描述(选填)">
          <el-input v-model="form.remark" type="textarea"/>
        </el-form-item>
      </el-form>
    </div>
  </elx-dialog-ex>
</template>

<script setup>
import {project_create_api} from '@/api/project'
import {reactive, ref} from 'vue'
import {clone} from '@/common/utils'

let form = reactive({
  projectId: '',
  projectName: '',
  remark: ''
})

const rules = ref({
  projectName: [{required: true, trigger: 'blur', message: '必须输入项目名称'}],
})


const loading = ref(false)
const dialog = ref()
const formRef = ref()
const open = (opt, project, callback) => {

  if (opt === 'edit') {
    form.projectId = project.projectId
    form.projectName = project.projectName
    form.remark = project.remark || ''
  } else {
    form.projectId = ''
    form.projectName = ''
    form.remark = ''
  }

  dialog.value.open((done, close) => {
    formRef.value.validate((valid) => {
      if (valid) {
        loading.value = true
        project_create_api(opt, form).then(res => {
          close()
          callback(res.data || {})
        }).catch(e => {
        }).finally(() => {
          done()
          loading.value = false
        })
      } else {
        done()
      }
    })
  })
}

defineExpose({
  open
})
</script>
