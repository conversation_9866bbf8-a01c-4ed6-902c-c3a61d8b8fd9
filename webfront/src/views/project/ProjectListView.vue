<template>
  <div v-loading="loading" class="project-list-container">
    <el-row>
      <el-col :span="24">
        <el-button v-permission="['A']" type="primary" icon="Plus" style="margin-right: 10px" @click="handleCreate">创建</el-button>
        <div class="search-input">
          <el-input v-model="searchInput" placeholder="输入关键字进行搜索 ..." class="search-button-append">
            <template #append>
              <elx-loading-button icon="Search" @click="handleSearch"/>
            </template>
          </el-input>
        </div>
      </el-col>
    </el-row>
    <div class="project-div">
      <el-card v-for="(p, index) in dataList" :key="index" shadow="hover" class="project-card w-350px mr-2">
        <div class="project-title">
          <div class="font-bold-14">{{ p.projectName }} <el-tag v-if="currentProject === p.projectId" type="danger" class="project-selected">已选择</el-tag></div>
          <div class="project-switch">
            <elx-loading-button v-if="currentProject !== p.projectId" plain size="small" icon="Select" type="primary" @click="done => handleSwitch(done, p.projectId)">选择</elx-loading-button>
            <el-dropdown v-if="hasPermission(['A'])" :teleported="false" trigger="hover" class="ml-2 more-options" style="vertical-align: -2px" @command="handleOperation($event, p)">
              <el-link class="more-icon" icon="More">操作</el-link>
              <template #dropdown>
                <el-dropdown-item v-if="hasPermission(['A'])" command="edit">
                  <div>修改</div>
                </el-dropdown-item>
                <el-dropdown-item v-if="hasPermission(['A'])" command="delete">
                  <div>删除</div>
                </el-dropdown-item>
              </template>
            </el-dropdown>
          </div>
        </div>
        <div class="font-remark"> {{ p.remark || '未添加项目描述' }}</div>
        <div class="item-create-time">
          <span>创建于 {{ p.createDateTime }}</span>
          <el-button v-if="currentProject === p.projectId" plain size="small" type="primary" @click="handleHome()">
            进入<el-icon class="el-icon--right"><ArrowRight /></el-icon>
          </el-button>
        </div>
      </el-card>
    </div>
    <project-create-dialog ref="dialogRef"/>
  </div>
</template>

<script setup>
import {useRouter} from 'vue-router'
import {project_search_api, delete_project_api} from '@/api/project'
import {useUserStore} from '@/common/store/modules/user'
import {ref, computed} from 'vue'
import ProjectCreateDialog from './ProjectCreateDialog.vue'
import {ElMessageBox} from 'element-plus';

const loading = ref(false)
const searchInput = ref('')
const dialogRef = ref()
const userStore = useUserStore()
const router = useRouter()

const dataList = ref([])

const handleCreate = () => {
  dialogRef.value.open('add', null, (p) => {
    userStore.UpdateProject('add', p)
    handleSearch()
  })
}

const handleOperation = (command, p) => {
  if (command === 'edit') {
    dialogRef.value.open('edit', p, (np) => {
      userStore.UpdateProject('edit', np)
      handleSearch()
    })
  } else {
    ElMessageBox.confirm('确定要删除项目【' + p.projectName + '】？如果删除，该项目下面的所有数据表及数据集等将全部删除。', '确认', { type: 'warning' }).then(() => {
      loading.value = true
      delete_project_api(p.projectId).then(res => {
        userStore.UpdateProject('delete', {projectId: p.projectId})
        handleSearch()
      }).catch(e => {
      }).finally(() => {
        loading.value = false
      })
    }).catch(e => {
    })
  }
}

const handleSwitch = (done, projectId) => {

  userStore.SwitchProject(projectId).then(res => {
    router.push('/')
  }).catch(err => {
  }).finally(() => {
    done()
  })
}

const handleHome = () => {
  router.push('/')
}

const currentProject = computed(() => {
  return userStore.currentProject.projectId
})

const handleSearch = (done) => {

  loading.value = true
  project_search_api().then(res => {
    dataList.value = res.data || []
  }).catch(e => {
  }).finally(() => {
    loading.value = false
    done && done()
  })
}

handleSearch()
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.project-list-container {
  padding: 10px 40px;
}
.search-input {
  display: inline-block;
  width: 400px;
}
.project-div {
  margin-top: 10px;
  display: flex;
  flex-wrap: wrap;
}
.item-create-time {
  bottom: 16px;
  left: 20px;
  line-height: 21px;
  font-size: 12px;
  color: #697886;
  display: flex;
  justify-content: space-between;
}
.project-title {
  display: flex;
  height: 25px;
  justify-content: space-between;
}
.project-card {
  margin-top: 10px;
}
.project-selected {
}
</style>

<style rel="stylesheet/scss" lang="scss">
.more-options {
  .more-icon {
    .el-icon svg {
      transform: rotate(90deg)
    }
  }
}
</style>
