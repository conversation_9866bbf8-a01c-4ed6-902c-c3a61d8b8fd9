<template>
  <elx-dialog-ex ref="dialog" :cancel-button="false" title="数据查看" width="1200px">
    <elx-block-header :title="'数据表名：' + tableName" style="margin-top: -10px"/>
    <div style="display: block; max-height: 550px; overflow-y: auto" class="thin-scroll-bar">
      <el-descriptions :column="tableMeta.length > 30 ? 3 : 2" border>
        <el-descriptions-item v-for="(meta, index) in tableMeta" :key="index">
          <template #label>
            <div :class="meta.match ? 'match-column': ''" class="cell-item">
              {{meta.name}}
            </div>
          </template>
          <div v-html="data[meta.id]" style="word-break:break-all;"/>
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </elx-dialog-ex>
</template>

<script setup>
import {ref} from 'vue'
let tableName = ''
let tableMeta = []
let data = {}

const dialog = ref()
const open = (name, meta, dataMap) => {
  tableName = name
  tableMeta = meta
  data = dataMap
  dialog.value.open((done, close) => {
    done()
    close()
  })
}

defineExpose({
  open
})
</script>
