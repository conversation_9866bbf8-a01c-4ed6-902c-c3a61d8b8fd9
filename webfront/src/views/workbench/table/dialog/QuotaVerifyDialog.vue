<template>
  <elx-dialog-ex ref="dialog" :button-area-show="false" title="配额核对" width="900px">
    <el-form ref="formRef" :model="form" :rules="validRules" inline>
      <div>
        <el-form-item label="数据列" prop="column">
          <el-select-v2 v-model="form.column" :options="columnOptions"></el-select-v2>
        </el-form-item>
        <el-form-item label="数据" prop="datas" style="margin-right: 0">
          <el-popover :visible="propVisible" placement="bottom" :show-arrow="false" :width="500" trigger="click">
            <template #reference>
              <elx-tag-input ref="tagInput" v-model="form.datas" readonly style="width: 500px" @click="propVisible = true"/>
            </template>
            <el-input v-model="form.dataText" :validate-event="false" type="textarea" :rows="10"></el-input>
            <el-button style="float: right; margin-top: 10px" @click="handleInputValue">确定</el-button>
          </el-popover>
        </el-form-item>
      </div>
      <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
        <el-switch
            v-model="form.filter"
            inactive-value="0"
            active-value="1"
            inactive-text="核对全部数据"
            active-text="自定义核对范围"
        />
        <elx-loading-button v-if="form.filter === '0'" type="primary" icon="DocumentChecked" @click="handleVerify">核对</elx-loading-button>
      </div>
      <div v-if="form.filter === '1'">
        <el-form-item label="导入时间">
          <el-date-picker
              v-model="filters.dateRange"
              type="daterange"
              range-separator="到"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              style="width: 300px"
          />
        </el-form-item>
        <el-form-item label="标签">
          <el-select-v2 v-model="filters.tags" :options="tagsOptions" multiple class="w-60" filterable clearable/>
        </el-form-item>
        <elx-loading-button v-if="form.filter === '1'" type="primary" icon="DocumentChecked" style="vertical-align: 5px; float: right;" @click="handleVerify">核对</elx-loading-button>
      </div>
    </el-form>
    <el-table :data="dataList" height="400px">
      <el-table-column type="index" label="#" width="60px"/>
      <el-table-column :filters="[ { text: '存在', value: '0' },  { text: '不存在', value: '1' }]"  :filter-method="filterHandler" prop="data" label="数据"/>
      <el-table-column prop="count" label="件数"/>
    </el-table>
  </elx-dialog-ex>
</template>

<script setup>
import {table_quota_verify_api} from '@/api/workbench/table'
import {reactive, ref, computed} from 'vue'

const props = defineProps({
  columns: {
    type: Array,
    required: true
  },
  tagsOptions: {
    type: Array,
    required: true
  }
})

const propVisible = ref(false)

let tableId = ''
const form = reactive({
  column: '',
  datas: [],
  dataText: '',
  filter: '0'
})
const validRules = {
  column: [
    { required: true, message: '必须选择数据列', trigger: ['change'] }
  ],
  datas: [
    { required: true, message: '必须输入数据', trigger: ['change'] }
  ]
}

const filters = reactive({
  dateRange: [],
  tags: []
})

const columnOptions = computed(() => {
  return props.columns.map(e => {
    return {label: e.name, value: e.id}
  })
})

const dataList = ref([])

const dialog = ref()
const open = (_tableId) => {
  tableId = _tableId
  form.column = ''
  form.filter = '0'
  form.datas.length = 0
  form.dataText = ''
  filters.dateRange.length = 0
  filters.tags.length = 0
  dialog.value.open()
}

const tagInput = ref()
const handleInputValue = () => {
  form.datas.length = 0
  if (form.dataText.trim() !== '') {
    let text = form.dataText.trim()
    text = text.replaceAll(/\r\n/g, '\n').replaceAll(/\n/g, ',').replaceAll(/\t/g, ',')
    const dataArray = text.split(',')
    dataArray.forEach(e => {
      const v = e.trim()
      if (v) {
        form.datas.push(v)
      }
    })
  }
  propVisible.value = false

}

const filterHandler = (value, row) => {
  if (value === '0') {
    return row['count'] !== 0
  }
  return row['count'] === 0
}

const formRef = ref()
const handleVerify = (done) => {
  formRef.value.validate((valid) => {
    if (valid) {
      table_quota_verify_api(tableId, form, filters).then(res => {
        dataList.value = res.data || []
      }).catch(e => {
      }).finally(() => {
        done()
      })
    } else {
      done()
    }
  })
}

defineExpose({
  open
})
</script>
