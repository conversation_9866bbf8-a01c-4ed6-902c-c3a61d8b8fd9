<template>
  <elx-dialog-ex ref="dialog" :cancel-button="false" title="数据修改历史查看" width="1200px">
    <div v-loading="loading">
      <elx-block-header :title="'数据表名：' + tableName" style="margin-top: -10px"/>
      <el-table ref="tablePanel" :data="dataList" style="height: 500px">
        <el-table-column label="修改人" prop="createdUserName" width="140px"/>
        <el-table-column label="修改时间" prop="createdTime" width="180px"/>
        <el-table-column label="修改内容">
          <template #default="scope">
            <el-tag v-for="(item, i) in editInfo(scope.row.editInfo)" :key="i" effect="plain" style="margin-right: 5px">
              [{{item.column}}]: <el-text tag="del" style="font-size: 12px; color: #716f6f">{{item.o}}</el-text> {{item.n}}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </elx-dialog-ex>
</template>

<script setup>
import {search_table_edit_history_api} from '@/api/workbench/table'

import {ref} from 'vue'
let tableName = ''
let columnMeta = {}
let tableId = ''
let recordId = ''

const loading = ref(false)
const dataList = ref([])

const dialog = ref()
const open = (name, meta, _tableId, _recordId) => {
  tableName = name
  meta.forEach(e => {
    columnMeta[e.id] = e.name
  })
  tableId = _tableId
  recordId = _recordId
  dialog.value.openWithInit(init, (done, close) => {
    done()
    close()
  })
}

const init = () => {
  loading.value = true
  search_table_edit_history_api(tableId, recordId).then(res => {
    dataList.value = res.data || []
  }).catch(e => {}).finally(() => {
    loading.value = false
  })
}

const editInfo = (infoText) => {
  const info = JSON.parse(infoText || '{}')
  const dataList = []
  Object.keys(info).forEach(e => {
    dataList.push({
      column: columnMeta[e],
      n: info[e].n,
      o: info[e].o || 'null'
    })
  })
  return dataList
}

const handleClick = () => {
}

defineExpose({
  open
})
</script>
