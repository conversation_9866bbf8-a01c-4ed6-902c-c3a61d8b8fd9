<template>
  <elx-dialog-ex ref="duplicationCheckDialogRef" title="查重处理" confirm-text="确认" width="600px">
    <el-form ref="formRef" :model="filters" :rules="validRules" label-width="100px" label-position="left">
      <el-form-item prop="fields" label="查重字段">
        <el-select-v2 v-model="filters.fields" :options="itemOptions" multiple class="w-400px" clearable filterable/>
        <elx-tips>
          根据所选字段，查找重复的数据。
        </elx-tips>
      </el-form-item>
      <el-form-item label="导入时间">
        <el-date-picker
            v-model="filters.dateRange"
            type="daterange"
            range-separator="到"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            style="width: 300px"
        />
      </el-form-item>
      <el-form-item label="标签">
        <el-select-v2 v-model="filters.tags" :options="tagsOptions" multiple class="w-60" style="width: 320px" filterable clearable/>
      </el-form-item>
    </el-form>
    <template #customButton2>
      <el-button @click="handleFilterCancel()">
        <svg-icon name="Nofilter" class="item-icon"/>
        取消查重
      </el-button>
    </template>
    <template #okButtonIcon>
      <svg-icon name="filter" class="item-icon" style="vertical-align: -3px; width: 15px; height: 15px"/>
    </template>
  </elx-dialog-ex>
</template>

<script setup>
import {ref, reactive, computed, watch} from 'vue'

const props = defineProps({
  tableId: {
    type: String,
    required: true
  },
  columns: {
    type: Array,
    required: true
  },
  tagsOptions: {
    type: Array,
    default: () => []
  },
  filters: {
    type: Object,
    required: true
  }
})
let callback = null

const formRef = ref()
const duplicationCheckDialogRef = ref()

const open = (_callback) => {
  callback = _callback
  duplicationCheckDialogRef.value.open((done, close) => {
    formRef.value.validate(valid => {
      if (valid) {
        callback(true)
        done()
        close()
      } else {
        done()
      }
    })
  })
}

const validRules = {
  fields: [
    { required: true, message: '必须选择查重字段', trigger: ['blur'] }
  ]
}

const columnMap = {}
const itemOptions = computed(() => {
  return props.columns.map(e => {
    columnMap[e.id] = e.name
    return {label: e.name, value: e.id}
  })
})

const emit = defineEmits(['update:filters'])

watch(() => props.filters, (nv) => {
  const filters = props.filters
  const filterArray = []
  if (filters.fields && filters.fields.length > 0) {
    filterArray.push('查重字段: ' + filters.fields.map(e => columnMap[e]))
  }
  if (filters.dateRange && filters.dateRange.length > 0) {
    filterArray.push('导入时间: [' + filters.dateRange.join(' : ') + ']')
  }
  if (filters.tags && filters.tags.length > 0) {
    filterArray.push('标签: ' + filters.tags)
  }
  if (filterArray.length > 0) {
    filters.filterText = filterArray.join(' 并且 ')
  } else {
    filters.filterText = ''
  }
  emit('update:filters', filters)
}, {immediate: true, deep: true})

const handleFilterCancel = () => {
  callback(false)
  duplicationCheckDialogRef.value.close()
}

defineExpose({
  open
})
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.item-icon {
  height: 18px;
  width: 18px;
}
</style>
