<template>
  <elx-dialog-ex ref="dialog" title="地区修改" confirm-text="修改" width="500px">
    <el-form :model="form" label-position="left" label-width="90px">
      <el-form-item :label="addressLabel">
        <el-tag effect="plain">
          {{ address }}
        </el-tag>
      </el-form-item>
      <el-form-item prop="area" :rules="[{required: true, message: '请选择所属区划'}]" label="所属区划">
        <el-cascader v-model="form.area" :options="areaOptions" :props="{checkStrictly: true}" style="width: 350px"/>
      </el-form-item>
      <el-form-item prop="road" label="所属道路">
        <el-input v-model="form.road" clearable style="width: 350px"/>
      </el-form-item>
    </el-form>
  </elx-dialog-ex>
</template>

<script setup>
import {ref, reactive} from 'vue'

const addressLabel = ref('')
const address = ref('')
const areaOptions = ref([])
const form = reactive({
  area: [],
  road: ''
})
const cache = []
let cacheRoad = ''

const dialog = ref()
const open = (_areaOptions, areaColumnMeta, row, callback) => {
  areaOptions.value = _areaOptions || []
  addressLabel.value = areaColumnMeta.name
  address.value = row[areaColumnMeta.id]

  form.area.length = 0
  cache.length = 0

  if (row.district) {
    form.area.push(row.district)
    cache.push(row.district)
  }
  if (row.town) {
    form.area.push(row.town)
    cache.push(row.town)
  }
  cacheRoad = row.road || ''
  form.road = row.road || ''

  dialog.value.open((done, close) => {
    if (diff()) {
      callback(form.area, form.road, address.value, () => {
        close()
      })
    } else {
      close()
    }
  })
}

const diff = () => {
  if (cache.length !== form.area.length || cacheRoad !== form.road) {
    return true
  }
  for (let i = 0; i < cache.length; i++) {
    if (cache[i] !== form.area[i]) {
      return true
    }
  }
  return false
}

defineExpose({
  open
})
</script>
