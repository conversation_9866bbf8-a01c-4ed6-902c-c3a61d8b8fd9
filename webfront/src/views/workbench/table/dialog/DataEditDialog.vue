<template>
  <elx-dialog-ex ref="dialog" title="数据编辑" confirm-text="保存" width="800px">
    <div v-loading="loading">
      <elx-block-header :title="'数据表名：' + tableName" style="margin-top: -10px"/>
      <el-form :model="form" label-position="top" class="edit-dialog-content thin-scroll-bar">
        <el-form-item v-for="(meta, index) in tableMeta" :key="index" :label="'#' + (index + 1) + ' ' + meta.name" :prop="meta.id">
          <el-input v-model="form[meta.id]"/>
        </el-form-item>
      </el-form>
    </div>
  </elx-dialog-ex>
</template>

<script setup>
import {edit_table_data_api} from '@/api/workbench/table'
import {reactive, ref} from 'vue'
import {clone} from '@/common/utils'

let tableId = ''
let tableName = ''
let tableMeta = []

const loading = ref(false)

const form = ref({})
const dialog = ref()
const open = (tId, tName, tMeta, dataMap, callback) => {
  tableId = tId
  tableName = tName
  tableMeta = tMeta
  form.value = clone(dataMap)

  dialog.value.open((done, close) => {
    loading.value = true
    edit_table_data_api(tableId, dataMap.id, form.value).then(res => {
      close()
      callback()
    }).catch(e => {
    }).finally(() => {
      done()
      loading.value = false
    })
  })
}

defineExpose({
  open
})
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.edit-dialog-content {
  max-height: 550px;
  overflow-y: auto;
}
</style>
