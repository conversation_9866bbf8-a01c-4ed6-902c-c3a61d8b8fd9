<template>
  <elx-dialog-ex ref="dialog" title="数据编辑(列)" confirm-text="修改" width="800px" @after-close="handleAfterClose">
    <div v-loading="loading">
      <el-table :data="dataList">
        <el-table-column label="数据列" width="250px">
          <template #default="scope">
            <el-select-v2 v-model="scope.row.column" :options="columnOptions" style="margin-left: -12px"/>
          </template>
        </el-table-column>
        <el-table-column label="修改值">
          <template #default="scope">
            <el-input v-model="scope.row.value"/>
          </template>
        </el-table-column>
        <el-table-column width="110px">
          <template #default="scope">
            <el-button size="small" type="primary" plain icon="Plus" @click="handleAddItem(scope.$index)"/>
            <el-button v-if="scope.$index !== 0 || dataList.length > 1" size="small" type="danger" plain icon="Close" @click="handleRemoveItem(scope.$index)"/>
          </template>
        </el-table-column>
      </el-table>
      <div class="el-table-item__error">
        {{ errorText }}
      </div>
      <el-radio-group v-model="mode" style="margin-top: 10px">
        <el-radio-button label="1">修改所有数据</el-radio-button>
        <el-radio-button label="2">手动指定条件</el-radio-button>
      </el-radio-group>
      <elx-tips style="margin: 5px 0 20px 5px">如果不指定条件，所有数据将会被更新，请慎重操作。</elx-tips>
      <elx-filter-panel v-if="mode === '2'" :table-id="tableId" :columns="columnMeta" :filters.sync="filterForm" :tags-options="tagsOptions" border/>
    </div>
  </elx-dialog-ex>
</template>

<script setup>
import {batch_edit_table_data_api} from '@/api/workbench/table'
import {reactive, ref} from 'vue'

let tableId = ''
let tableName = ''
let tagsOptions = []
const errorText = ref('')
const columnMeta = ref([])
const columnOptions = ref([])

const loading = ref(false)

const form = ref({})
const filterForm = reactive({
  dateRange: [],
  tags: [],
  itemFilterMode: '1',
  itemFilters: [],
  customFilter: ''
})

const mode = ref('1')
const dataList = ref([{
  column: '',
  value: ''
}])

const handleAddItem = (index) => {
  dataList.value.splice(index + 1, 0, {
    column: '',
    value: ''
  })
}

const handleRemoveItem = (index) => {
  dataList.value.splice(index, 1)
}

const handleAfterClose = () => {
  errorText.value = ''
  mode.value = '1'
  dataList.value.length = 0
  dataList.value = [{column: '', value: ''}]
}

const dialog = ref()
const open = (tId, tName, _columnMeta, _tagsOptions, callback) => {
  tableId = tId
  tableName = tName
  columnMeta.value = _columnMeta
  columnOptions.value = _columnMeta.map(e => {
    return {label: e.name, value: e.id}
  })
  tagsOptions = _tagsOptions

  dialog.value.open(async(done, close) => {
    const hasError = dataList.value.some(e => e.column === '' || e.value === '')
    if (hasError) {
      errorText.value = '上述“数据列”及“修改值”不能为空。'
      done()
      return
    }
    errorText.value = ''

    if (mode.value === '1') {
      const ok = await ElMessageBox.confirm(
          '您没有手动指定更新条件，此次操作<span style="color: red">全部数据将会被更新</span>，是否继续？',
          '确认',
          { type: 'warning', dangerouslyUseHTMLString: true }
      ).catch(e => {})
      if (ok !== 'confirm') {
        done()
        return
      }
    }
    loading.value = true
    batch_edit_table_data_api(tableId, dataList.value, mode.value === '1' ? null : filterForm).then(res => {
      close()
      callback()
    }).catch(e => {
    }).finally(() => {
      done()
      loading.value = false
    })
  })
}

defineExpose({
  open
})
</script>
