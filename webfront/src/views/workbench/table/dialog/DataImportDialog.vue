<template>
  <elx-dialog-ex ref="dialog" title="导入数据" confirm-text="导入" width="600px">
    <el-form ref="formRef" :model="form" :rules="validRules" label-width="100px" label-position="left">
      <el-form-item prop="file" label="文件">
        <el-upload
            action=""
            :accept="'.xlsx'"
            :http-request="f => handleAddFile(f.file)"
            :limit="1"
            :on-success="() => {}"
            :on-error="() => {}"
            :before-upload="() => {}"
            class="w-380px"
        >
          <el-button type="">选择文件</el-button>
          <!-- <template #tip>
            <div class="el-upload__tip">
              jpg/png files with a size less than 500kb
            </div>
          </template>-->
        </el-upload>
      </el-form-item>
      <!-- <el-form-item prop="tags" label="标签(选填)">
        <el-tag
            v-for="tag in dynamicTags"
            :key="tag"
            class="mx-1"
            closable
            :disable-transitions="false"
            @close="handleTagsRemove(tag)"
        >
          {{ tag }}
        </el-tag>
        <el-input
            v-if="inputVisible"
            ref="InputRef"
            v-model="inputValue"
            class="ml-1 w-40"
            size="small"
            @keyup.enter="handleAddTags"
            @blur="handleAddTags"
        />
        <el-button v-else-if="dynamicTags.length < 3" class="button-new-tag ml-1" size="small" @click="showTagsInput">
          + 添加
        </el-button>
        <elx-tips>
          用于方便筛选数据（最多支持三个标签）
        </elx-tips>
      </el-form-item>-->
    </el-form>
  </elx-dialog-ex>
</template>

<script setup>
import {nextTick, reactive, ref} from 'vue'
import {import_table_data_api} from '@/api/workbench/table'
const dialog = ref()


const form = reactive({
  tableId: '',
  file: null
})

const validRules = {
  file: [
    {required: true, validator: (rule, value, callback) => {
        if (value === null) {
          callback(new Error('请选择Excel文件'))
        } else {
          callback()
        }
      }, trigger: 'blur'}
  ]
}

const inputValue = ref('')
const dynamicTags = ref([])
const inputVisible = ref(false)
const InputRef = ref()

const handleTagsRemove = (tag) => {
  dynamicTags.value.splice(dynamicTags.value.indexOf(tag), 1)
}

const showTagsInput = () => {
  inputVisible.value = true
  nextTick(() => {
    InputRef.value.input.focus()
  })
}

const handleAddTags = () => {
  if (inputValue.value) {
    dynamicTags.value.push(inputValue.value)
  }
  inputVisible.value = false
  inputValue.value = ''
}

const handleAddFile = (file) => {
  form.file = file

  // loading.value = true
  // const key = route.params.key

}
const formRef = ref()
const open = (key, callback) => {
  form.tableId = key
  form.file = null
  dynamicTags.value.length = 0
  dialog.value.open((done, close) => {
    // TODO
    formRef.value.validate(valid => {
      if (valid) {
        import_table_data_api(form, dynamicTags.value).then(res => {
          close()
          callback()
        }).catch(e => {
        }).finally(() => {
          done()
        })
      } else {
        done()
      }
    })
  })
}

defineExpose({
  open
})
</script>
