<template>
  <elx-page-card v-loading="loading" save-button @save="handleSave">
    <elx-block-header title="数据表属性">
      <el-form ref="formRef" :model="form" :rules="validRules" :inline="true" label-width="100px">
        <el-row>
          <el-form-item label="数据表名" prop="tableName">
            <el-input v-model="form.tableName" class="w-300px"/>
            <elx-tips>
              <div>表名不能够重复。</div>
            </elx-tips>
          </el-form-item>
          <el-form-item label="描述(选填)" prop="remark">
            <!-- <el-select-v2 v-model="form.nameSpace" :options="nameSpaceOptions" placeholder="请选择命名空间" clearable/>-->
            <el-input v-model="form.remark" class="w-192"/>
          </el-form-item>
        </el-row>
      </el-form>
    </elx-block-header>

    <elx-block-header title="数据列属性" style="margin-top: -10px">
      <el-table :data="columnList" max-height="calc(100vh - 430px)" scrollbar-always-on>
        <el-table-column type="index" label="#"/>
        <el-table-column label="主键" width="100px" align="center">
          <template #header>
            <span>主键</span>
            <el-popover placement="top" :width="300">
              <template #reference>
                <el-icon style="vertical-align: -3px; margin-left: 5px" size="16"><QuestionFilled /></el-icon>
              </template>
              <div>
                创建数据表时，一般都要指定主键；插入数据时，勾选主键的列的数据不能重复。（主键的列的数据，必须唯一能够代表该条数据。）
              </div>
            </el-popover>
          </template>
          <template #default="scope">
            <el-checkbox v-model="scope.row.primaryKey"/>
          </template>
        </el-table-column>
        <el-table-column prop="columnName" label="列名" width="250px" column-class-name="is-error">
          <template #default="scope">
            <el-input v-model="scope.row.columnName" :class="{'table-item__error-input': scope.row.errors.columnName}"/>
            <span v-if="scope.row.errors.columnName" class="el-table-item__error">{{ scope.row.errors.columnName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="columnType" label="类型" width="200px">
          <template #default="scope">
            <el-select-v2 v-model="scope.row.columnType" :options="columnTypeOptions"/>
          </template>
        </el-table-column>
        <el-table-column prop="length" label="最大长度" width="150px" header-align="center">
          <template #default="scope">
            <el-input v-model="scope.row.length" type="number" style="width: 120px"/>
          </template>
        </el-table-column>
        <el-table-column prop="address" label="地址字段" width="150px" header-align="center" align="center">
          <template #header>
            <span>地址字段</span>
            <el-popover placement="top" :width="300">
              <template #reference>
                <el-icon style="vertical-align: -3px; margin-left: 5px" size="16"><QuestionFilled /></el-icon>
              </template>
              <div>
                必须指定并且只能指定一个事物【地址字段】，根据指定的【地址字段】，可以进行区域筛选。
              </div>
            </el-popover>
          </template>
          <template #default="scope">
            <el-switch v-model="scope.row.address" :active-value="1" :inactive-value="0"/>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="nullAble" label="不可为空" width="100px" align="center">
                <template #default="scope">
                  <el-checkbox v-model="scope.row.nullAble"/>
                </template>
              </el-table-column>-->
        <!-- <el-table-column prop="defaultValue" label="默认值" width="100px"/>-->
        <el-table-column label="" align="right" header-align="right" min-width="200px">
          <template  #header>
            <elx-upload-button size="small" type="info" plain icon="Document" class="mr-2" style="vertical-align: -3px" :upload-func="handleUpload">读取Excel</elx-upload-button>
            <el-button size="small" type="primary" icon="Plus" @click="handlePlus"></el-button>
          </template>
          <template #default="scope">
            <el-button :disabled="scope.$index === 0" size="small" type="" icon="Top" @click="handleMoveUp(scope.$index)"></el-button>
            <el-button :disabled="scope.$index === columnList.length - 1" size="small" type="" icon="Bottom" @click="handleMoveDown(scope.$index)"></el-button>
            <el-button size="small" type="" icon="Plus" @click="handleAdd(scope.$index)"></el-button>
            <el-button size="small" type="danger" icon="Close" @click="handleRemove(scope.$index)"></el-button>
          </template>
        </el-table-column>
      </el-table>
      <span v-if="columnListError" class="el-span__error">{{columnListError}}</span>
    </elx-block-header>
  </elx-page-card>
</template>

<script setup>
import {table_get_api, table_save_api} from '@/api/workbench/table'
import {reactive, ref} from 'vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import { readExcelHeader } from '@/common/utils/xlsxutils'
import {useRouter, useRoute} from 'vue-router'

const loading = ref(true)
const form = reactive({
  tableId: '',
  tableName: '',
  remark: '',
  primaryKey: []
})

const columnTypeOptions = [
  {label: '文本', value: 1},
  {label: '数字', value: 2},
  {label: '布尔型', value: 3},
  {label: '日期', value: 4}
]

const columnList = ref([])
const columnListError = ref('')
const router = useRouter()
const route = useRoute()

const optType = route.meta.optType
const tableId = route.params.key

const handleInit = () => {
  if (optType === 'edit') {
    table_get_api(tableId).then(res => {
      form.tableId = tableId
      form.tableName = res.data.tableName
      form.remark = res.data.remark
      columnList.value = res.data.columnList
    }).catch(e => {
      if (e.custom === true) {
        ElMessageBox.confirm(e.messages[0].message, '返回',
            {
              confirmButtonText: '确认',
              showCancelButton: false,
              closeOnClickModal: false,
              closeOnPressEscape: false,
              closeOnHashChange: false,
              showClose: false,
              type: 'error'
            }
        ).then(() => {
          router.replace('/workbench/table')
        })
      }
    }).finally(() => {
      loading.value = false
    })
  } else {
    loading.value = false
  }
}

const handlePlus = () => {
  columnList.value.push({
    columnType: 1,
    length: 100,
    address: 0,
    errors: {}
  })
}

const handleAdd = (index) => {
  const data = {
    columnType: 1,
    length: 50,
    address: 0,
    errors: {}
  }
  if (index === columnList.value.length - 1) {
    columnList.value.push(data)
  } else {
    columnList.value.splice(index + 1, 0, data)
  }
}

const handleRemove = (index) => {
  columnList.value.splice(index, 1)
}

const validRules = reactive({
  tableName: [
    {required: true, message: '请输入数据表名称', trigger: 'blur'}
  ]
})

const formRef = ref()

const checkColumnList = () => {
  let error = columnList.value.length === 0
  columnListError.value = columnList.value.length === 0 ? '请添加数据列。' : ''
  columnList.value.forEach(row => {
    if (row.columnName) {
      row.errors.columnName = ''
    } else {
      error = true
      row.errors.columnName = '请输入列名'
    }
  })
  if (!error) {
    const countMap = {}
    let hasAddressColumn = 0
    columnList.value.forEach((e, index) => {
      const c = countMap[e.columnName]
      if (c) {
        countMap[e.columnName].push(index)
      } else {
        countMap[e.columnName] = [index]
      }
      if (e.address === 1) {
        hasAddressColumn = hasAddressColumn + 1
      }
    })

    const errorFiled = []
    Object.keys(countMap).forEach(k => {
      const v = countMap[k]
      if (v.length > 1) {
        errorFiled.push(k)
      }
    })
    if (errorFiled.length > 0) {
      columnListError.value = '项目【' + errorFiled + '】重复，请确认后再保存。'
      error = true
    }

    if (!error) {
      if (hasAddressColumn === 0) {
        columnListError.value = '请指定地址字段后再保存。'
        error = true
      } else if (hasAddressColumn > 1) {
        columnListError.value = '只能指定一个地址字段，请确认后再保存。'
        error = true
      }
    }
  }
  return !error
}

const handleSave = async(done) => {
  const valid1 = await formRef.value.validate().catch(e => {})
  const valid2 = checkColumnList()
  if (valid1 && valid2) {
    table_save_api(form, optType, columnList.value).then(res => {
      router.replace('/workbench/table')
    }).catch(() => {
    }).finally(() => {
      done()
    })
  } else {
    ElMessage.error('页面项目输入不正确，请确认后再保存。')
    done()
  }
}

const handleUpload = (file) => {
  loading.value = true
  readExcelHeader(file).then(res => {
    columnList.value.length = 0
    columnList.value.push(...res.map(r => {
      return {
        columnName: r.n,
        columnType: r.t,
        length: 100,
        errors: {}
      }
    }))
  }).catch(e => {
  }).finally(() => {
    loading.value = false
  })
}

const handleMoveUp = (index, row) => {
  if (index > 0) {
    const upData = columnList.value[index - 1]
    columnList.value.splice(index - 1, 1)
    columnList.value.splice(index, 0, upData)
  }
}

const  handleMoveDown = (index, row) => {
  const downData = columnList.value[index + 1]
  columnList.value.splice(index + 1, 1)
  columnList.value.splice(index, 0, downData)
}

handleInit()

</script>
