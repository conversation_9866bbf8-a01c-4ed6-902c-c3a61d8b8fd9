<template>
  <elx-table-panel ref="tablePanel" :search-loading="loading" :data="dataList" :pagination="pagination"
                   :row-style="rowStyleFunc" :stripe="!needDuplicationCheckFilter"
                   height="calc(100vh - 260px)" border page-header :search-function="handleSearch">
    <template #extra>
      <span v-if="tableName !== ''" class="font-bold-14">数据表名：</span>
      <span class="font-bold-14">{{ tableName }}</span>
    </template>
    <template #button-area-left>
      <elx-filter-button
          ref="filterButtonRef"
          :table-id="route.params.key"
          :columns="columnMetas"
          :filters.sync="filterForm"
          :tags-options="tagsOptions"
          :area-options="areaOptions"
          @filter="handleFilter"/>
      <el-button type="primary" icon="Tickets" plain @click="handleView">查看</el-button>
      <el-button v-permission="['A', 'C']" type="primary" icon="Edit" plain @click="handleEdit">编辑</el-button>
      <elx-loading-button v-permission="['A', 'C']" type="danger" icon="Close" plain @click="done => handleDelete(done)">删除</elx-loading-button>
      <div style="display: inline-block; margin-left: 10px">
        <el-input v-if="needFilter" v-model="filterForm.filterText" prefix-icon="filter" readonly style="width: calc(100vw - 900px)" @dblclick="handleFilterInput('filter')"/>
      </div>
    </template>

    <template #button-area-right>
      <elx-history-button v-permission="['A', 'C']" type="export" style="margin-right: 5px" @click="handleExport" @history="handleExportHistory"/>
      <elx-table-column-selected :columns="columnMetas" @update-fixed="v => fixedIndex = v"/>
    </template>
    <el-table-column type="selection" align="center" fixed width="60px"/>
    <el-table-column fixed width="142px" label="导入时间" label-class-name="fix-column">
      <template #default="scope">
        {{history[scope.row.importKey]?.importTime}}
      </template>
    </el-table-column>
    <el-table-column prop="city" label="所属区划" fixed width="200px" label-class-name="fix-column">
      <template #default="scope">
        <div class="area-edit-dev">
          <span>
            {{ getArea(scope.row) }}
          </span>
          <el-link v-if="isAdmin" :underline="false" type="primary" plain size="small" icon="Edit" class="edit" @click="handleEditArea(scope.row)"/>
        </div>
      </template>
    </el-table-column>
    <!-- <el-table-column prop="rowRank" width="60px" label="Rank"/>
    <el-table-column prop="count" width="60px" label="Count"/>-->
    <!-- <el-table-column label="＜＜数据＞＞">
    </el-table-column>-->
    <el-table-column v-for="(c, index) in columnMetas.filter(e => e.hidden !== true)" show-overflow-tooltip :fixed="index < fixedIndex" :prop="c.id" :label="c.name" :key="index" :width="c.width || '150px'" resizable/>
    <el-table-column/>
  </elx-table-panel>
  <DataViewDialog ref="dataViewDialog"/>
  <DataEditDialog ref="dataEditDialog"/>
  <DataExportDialog ref="dataExportDialog"/>
  <DataEditHistoryDialog ref="dataEditHistoryDialog"/>
  <ImportExportHistory ref="importExportHistoryRef"/>
  <AreaEditDialog ref="AreaEditDialogRef"/>
</template>

<script setup>
import {search_table_data_api, delete_table_data_api, data_duplication_check_api, change_area_api, export_table_data_api} from '@/api/workbench/table'
import {computed, reactive, ref, unref, watch} from 'vue'
import {useRouter, useRoute} from 'vue-router'
import DataViewDialog from './dialog/DataViewDialog.vue'
import DataEditDialog from './dialog/DataEditDialog.vue'
import DataEditHistoryDialog from './dialog/DataEditHistoryDialog.vue'
import DataExportDialog from '../component/DataExportDialog.vue'
import ImportExportHistory from '../component/ImportExportHistory.vue'
import AreaEditDialog from './dialog/AreaEditDialog.vue'

import {ElMessageBox} from 'element-plus'
import {useUserStore} from '@/common/store/modules/user'

const loading = ref(true)
const dataList = ref([])
const columnMetas = ref([])
const tagsOptions = ref([])
const areaOptions = ref([])
const fixedIndex = ref(0)
const areaColumn = ref('')

const history = ref({})
let tableName = ''

let needFilter = false
const filterForm = reactive({
  dateRange: [],
  tags: [],
  area: [],
  itemFilterMode: '1',
  itemFilters: [],
  customFilter: '',
  filterText: ''
})

let needDuplicationCheckFilter = false
const duplicationCheckForm = reactive({
  fields: [],
  dateRange: [],
  tags: [],
  filterText: ''
})

const pagination = reactive({
  pageNumber: 1,
  pageSize: 20
})
const router = useRouter()
const route = useRoute()

const importExportHistoryRef = ref()
const handleImportHistory = () => {
  console.log('handleImportHistory')
}

const dataExportDialog = ref()
const handleExport = () => {
  const tableId = route.params.key
  dataExportDialog.value.open('1', tableId, tableName, columnMetas.value, tagsOptions.value, export_table_data_api, () => {
    importExportHistoryRef.value.open(tableId, 0, tableName)
  })
}

const handleExportHistory = () => {
  const tableId = route.params.key
  importExportHistoryRef.value.open(tableId, 0, tableName)
}

const dataViewDialog = ref()
const tablePanel = ref()
const handleView = () => {
  tablePanel.value.getSelectionRows(true, '查看').then(selected => {
    dataViewDialog.value.open(tableName, columnMetas, selected)
  }).catch(e => {
  })
}

const dataEditDialog = ref()
const handleEdit = () => {
  const tableId = route.params.key
  tablePanel.value.getSelectionRows(true, '编辑').then(selected => {
    dataEditDialog.value.open(tableId, tableName, columnMetas, selected, () => {
      handleSearch()
    })
  }).catch(e => {
  })
}

const handleDelete = (done) => {
  tablePanel.value.getSelectionRows().then(selected => {
    ElMessageBox.confirm('确定删除勾选数据？', '确认', { type: 'warning' }).then(() => {
      loading.value = true
      const tableId = route.params.key
      delete_table_data_api(tableId, selected.map(e => e.id)).then(res => {
        handleSearch()
      }).catch(e => {
      }).finally(() => {
        loading.value = false
        done()
      })
    }).catch(e => {
        done()
    })
  }).catch(() => {
    done()
  })
}

const handleFilter = (done, _needFilter, _needDuplicationCheckFilter) => {
  needFilter = _needFilter
  needDuplicationCheckFilter = _needDuplicationCheckFilter
  pagination.pageNumber = 1
  handleSearch(done)
}

const filterButtonRef = ref()
const handleFilterInput = (command) => {
  filterButtonRef.value.handleCommand(command)
}

const rowStyleFunc = ({row, rowIndex}) => {
  if (needDuplicationCheckFilter && row.rowRank !== 1) {
    return {
      'background': '#BBB !important',
      'color': 'red'
    }
  } else {
    return {}
  }
}

const handleMoreControl = (command) => {
  if (command === 'Download') { // 下载
    handleExport()
  }
}

const getArea = (row) => {
  if (row.district && row.town && row.road) {
    return row.district + ' / ' + row.town + ' / ' + row.road
  } else if (row.district && row.town) {
    return row.district + ' / ' + row.town
  } else if (row.district && row.road) {
    return row.district + ' / ' + row.road
  } else if (row.town && row.road) {
    return row.town + ' / ' + row.road
  } else if (row.district) {
    return row.district
  } else if (row.town) {
    return row.town
  } else if (row.road) {
    return row.road
  }
  return ''
}

const handleSearch = (done, init) => {
  const tableId = route.params.key

  if (needFilter || !needDuplicationCheckFilter) {
    normalFilter(tableId, init, done)
  } else if (needDuplicationCheckFilter) {
    duplicationFilter(tableId, done)
  } else {
    console.log('error.', needFilter, needDuplicationCheckFilter)
  }
}

const duplicationFilter = (tableId, done) => {
  loading.value = true
  data_duplication_check_api(tableId, duplicationCheckForm, pagination).then(res => {
    dataList.value = res.data || []
    Object.assign(pagination, res.pagination)
  }).catch(e => {
  }).finally(() => {
    loading.value = false
    done && done()
  })
}

const normalFilter = (tableId, init, done) => {
  loading.value = true
  search_table_data_api(init, tableId, needFilter ? filterForm : null, pagination).then(res => {
    if (init === true) {
      tableName = res.dataMap.tableName
      const meta = res.dataMap.meta
      columnMetas.value = JSON.parse(meta)
      areaOptions.value = res.dataMap.areaOptions || []
      const areaColumnMeta = columnMetas.value.find(e => e.address === 1)
      if (areaColumnMeta) {
        areaColumn.value = areaColumnMeta
      }
    }

    tagsOptions.value = res.dataMap.tagsOptions
    history.value = res.dataMap.history || {}

    dataList.value = res.data || []
    Object.assign(pagination, res.pagination)
  }).catch(e => {
    if (e.custom === true) {
      ElMessageBox.confirm(e.messages[0].message, '返回',
          {
            confirmButtonText: '确认',
            showCancelButton: false,
            closeOnClickModal: false,
            closeOnPressEscape: false,
            closeOnHashChange: false,
            showClose: false,
            type: 'error'
          }
      ).then(() => {
        router.replace('/workbench/table')
      })
    }
  }).finally(() => {
    loading.value = false
    done && done()
  })
}

handleSearch(() => {}, true)

const AreaEditDialogRef = ref()
const handleEditArea = (row) => {
  AreaEditDialogRef.value.open(areaOptions.value, areaColumn.value, row, (area, road, address, close) => {
    change_area_api(area, road, address).then(res => {
      if (area.length > 0) {
        row.district = area[0]
      }
      if (area.length > 1) {
        row.town = area[1]
      } else {
        row.town = ''
      }
      row.road = road || null
      close()
    }).catch(e => {
    })
  })
}

const store = useUserStore()
const isAdmin = computed(() => {
  return store.userType === '0' // admin
})

</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.area-edit-dev {
  display: flex;
  justify-content: space-between;
  //.edit {
  //  display: none;
  //}
}
//.area-edit-dev:hover {
//  .edit {
//    display: flex !important;
//  }
//}
</style>
