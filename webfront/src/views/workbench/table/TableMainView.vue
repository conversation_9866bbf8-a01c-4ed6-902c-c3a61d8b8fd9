<template>
  <elx-table-panel :search-loading="searchLoading" :data="dataList" :pagination="pagination" :search-function="handleSearch" :has-back="false" page-header height="calc(100vh - 260px)">
    <template #extra>
      <el-button v-permission="['A']" type="primary" icon="plus" @click="handleCreate">创建数据表</el-button>
    </template>
    <template #button-area-left>
      <div style="display: inline-block; width: 400px;">
        <el-input v-model="searchKey" placeholder="输入数据表名称进行检索..." clearable class="search-button-append" @keyup.enter.native="handleSearch()">
          <template #append>
            <el-button type="info" icon="search" @click="handleSearch"/>
          </template>
        </el-input>
      </div>
    </template>
    <el-table-column type="index" label="#" width="50px"/>
    <el-table-column show-overflow-tooltip prop="tableName" label="数据表名" width="300px"/>
    <!-- <el-table-column show-overflow-tooltip prop="nameSpace" label="命名空间" width="200px"/>-->
    <el-table-column show-overflow-tooltip prop="createUser" label="创建者" width="160px"/>
    <el-table-column show-overflow-tooltip prop="matchStatus" label="地区匹配状态" width="220px">
      <template #default="scope">
        <el-tag :type="getStatusType(scope.row.matchStatus)" disable-transitions>{{ scope.row.matchStatusName }}</el-tag>
      </template>
    </el-table-column>
    <el-table-column show-overflow-tooltip prop="createTime" label="创建时间" width="200px"/>
    <!-- <el-table-column show-overflow-tooltip prop="scoreTemplateName" label="算分模型" width="200px"/>-->
    <el-table-column show-overflow-tooltip prop="remark" label="描述"/>
    <el-table-column width="280px" align="right">
      <template #default="scope">
        <elx-loading-button type="primary" size="small" plain icon="Setting" @click="done => handleControl(scope.row, done)">数据操作</elx-loading-button>
        <elx-table-command :operation-handler="operationHandler" :row="scope.row" :row-index="scope.index"/>
        <!-- <el-button icon="Delete" size="small" plain>回收站</el-button>
        <elx-loading-button type="danger" size="small" plain icon="Delete" @click="done => handleDelete(scope.row, done)">删除</elx-loading-button>-->
      </template>
    </el-table-column>
  </elx-table-panel>
  <DataImportDialog ref="dataImportDialog"/>
</template>

<script setup>
import {search_table_api, delete_table_api, clear_table_data_api, match_area_api} from '@/api/workbench/table'
import {ref, reactive} from 'vue'
import {useRouter} from 'vue-router'
const router = useRouter()
import { ElMessageBox } from 'element-plus'
import DataImportDialog from './dialog/DataImportDialog.vue'

const searchKey = ref('')

const dataList = ref([])
const pagination = reactive({
  pageNumber: 1,
  pageSize: 20
})
const searchLoading = ref(false)
const operationHandler = ref([
  {
    name: '数据导入',
    icon: 'Upload',
    permission: ['A', 'C'],
    handler: (row, index) => handleImport(row)
  },
  {
    name: '地区匹配',
    icon: 'MapLocation',
    permission: ['A', 'C'],
    handler: (row, index) => handleMatch(row)
  },
  {
    name: '清空数据',
    divided: true,
    icon: 'CircleClose',
    permission: ['A', 'C'],
    handler: (row, index) => handleClearData(row)
  },
  {
    name: '修改表结构',
    icon: 'Edit',
    divided: true,
    permission: ['A'],
    handler: (row, index) => handleEdit(row)
  },
  {
    name: '删除',
    icon: 'Close',
    permission: ['A'],
    handler: (row, index) => handleDelete(row)
  }
])

const handleUpload = ref(() => {
})

const handleCreate = () => {
  router.push('/workbench/table/create')
}

const handleSearch = () => {
  searchLoading.value = true
  search_table_api(searchKey.value, pagination).then(res => {
    dataList.value = res.data || []
    Object.assign(pagination, res.pagination)
  }).catch(() => {
  }).finally(() => {
    searchLoading.value = false
  })
}
handleSearch()

const handleControl = (row, done) => {
  done()
  router.push('/workbench/table/data/' + row.tableId)
}

const handleEdit = (row, done) => {
  router.push('/workbench/table/edit/' + row.tableId)
}

const handleDelete = (row, done) => {
  ElMessageBox.confirm('确定删除该数据表吗，该表的“数据”及相关的数据展示等也将一并删除？', '确认', { type: 'warning' }).then(() => {
    searchLoading.value = true
    delete_table_api(row.tableId).then(res => {
      handleSearch()
    }).catch(() => {
    }).finally(() => {
      done && done()
      searchLoading.value = false
    })
  }).catch(e => {
    done && done()
  })
}

const handleClearData = (row, done) => {
  ElMessageBox.confirm(
      '确定清空数据表【' + row.tableName + '】吗？',
      '确认',
      { type: 'warning' }
  ).then(() => {
    searchLoading.value = true
    clear_table_data_api(row.tableId).then(res => {
      pagination.pageNumber = 1
      handleSearch()
    }).catch(() => {
    }).finally(() => {
      done && done()
      searchLoading.value = false
    })
  }).catch(e => {
    done && done()
  })
}

const dataImportDialog = ref()
const handleImport = (row) => {
  dataImportDialog.value.open(row.tableId, () => {
    handleSearch()
  })
}

const getStatusType = (status) => {
  return status === '1' ? 'warning' : status === '2' ? 'danger' : 'success'
}

const handleMatch = (row) => {
  match_area_api(row.tableId).then(res => {
    const data = res.data
    row.matchStatus = data.matchStatus
    row.matchStatusName = data.matchStatusName
  }).catch(e => {
  }).finally(() => {
  })
}
</script>

