<template>
  <div style="margin: 20px">
    <el-card shadow="hover" class="item-block" @click="handleRouter('/')">
      <svg-icon name="数据清洗" class="item-icon"/>
      <span class="item-title">数据清洗</span>
      <div class="item-switch">
        进入>>
      </div>
    </el-card>
    <el-card shadow="hover" class="item-block">
      <svg-icon name="逻辑核查" class="item-icon"/>
      <span class="item-title">逻辑核查</span>
      <div class="item-switch">
        进入>>
      </div>
    </el-card>
    <el-card shadow="hover" class="item-block">
      <svg-icon name="自定义规则" class="item-icon"/>
      <span class="item-title">自定义规则建库</span>
      <div class="item-switch">
        进入>>
      </div>
    </el-card>
    <el-card shadow="hover" class="item-block" @click="() => $router.push('/workbench/analysis/calculate/list')">
      <svg-icon name="计分详情" class="item-icon"/>
      <span class="item-title">算分模板</span>
      <div class="item-switch">
        进入>>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { useRouter} from 'vue-router'
const router = useRouter()
const handleRouter = (path) => {
  router.push(path)
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.item-block {
  display: inline-block;
  width: 260px;
  height: 140px;
  margin-right: 20px;
  cursor: pointer;
}
.item-icon {
  height: 40px;
  width: 40px;
  vertical-align: -20px;
}
.item-title {
  font-weight: bold;
  font-size: 16px;
  color: #6C757D;
  margin-left: 20px;
}
.item-switch {
  font-size: 10px;
  font-weight: bold;
  margin-left: 150px;
  margin-top: 30px;
  visibility: hidden;
  color: #6C757D;
}
.item-block:hover {
  .item-switch {
    visibility: visible !important;
  }
}
</style>
