<template>
  <elx-table-panel :search-loading="searchLoading" :data="dataList" :pagination="pagination" :search-function="handleSearch" :has-back="false" page-header height="calc(100vh - 260px)">
    <template #extra>
      <el-button v-permission="['A']" type="primary" icon="plus" @click="handleCreate">创建匹配模型</el-button>
      <el-button v-permission="['A', 'C', 'D']" type="primary" icon="Upload" class="ml-5px" @click="handleUpload">导入匹配模型</el-button>
    </template>
    <template #button-area-left>
      <div style="display: inline-block; width: 400px; margin-right: 10px">
        <el-input v-model="searchKey" placeholder="输入匹配模型名称进行检索..." clearable class="search-button-append" @keyup.enter.native="handleSearch()">
          <template #append>
            <el-button type="info" icon="search" @click="handleSearch"/>
          </template>
        </el-input>
      </div>
    </template>
    <el-table-column type="index" label="#" width="50px"/>
    <el-table-column show-overflow-tooltip prop="modelName" label="匹配模型名" width="260px"/>
    <el-table-column show-overflow-tooltip prop="modelType" label="模型类型" width="190px">
      <template #default="scope">
        <el-tag :type="scope.row.modelType === '1' ? '' : 'warning'" effect="plain" disable-transitions>{{ scope.row.modelTypeName }}</el-tag>
        <el-tag v-if="scope.row.modelType === '2'" type="info" effect="plain" disable-transitions class="ml-2px">({{ scope.row.aiModelName }})</el-tag>
      </template>
    </el-table-column>
    <el-table-column prop="status" align="center" label="状态" width="100px">
      <template #default="scope">
        <el-icon v-if="scope.row.status === '0'" color="green"><Select /></el-icon>
        <el-icon v-else color="red" title="模型保存成功，但是跟AI模型库同步失败，请确认后重新保存。"><CloseBold /></el-icon>
      </template>
    </el-table-column>
    <el-table-column show-overflow-tooltip prop="userName" label="创建者" width="160px"/>
    <el-table-column show-overflow-tooltip prop="createTime" label="创建时间" width="200px"/>
    <el-table-column show-overflow-tooltip prop="remark" label="描述"/>
    <el-table-column width="410px" align="right">
      <template #default="scope">
        <elx-loading-button v-if="hasPermission(['A', 'C', 'D']) && scope.row.modelType === '2'" type="" size="small" plain icon="Aim" @click="done => handleRefreshModel(scope.row, done)">模型训练</elx-loading-button>
        <elx-loading-button v-if="hasPermission(['A', 'C', 'D'])" type="primary" size="small" plain icon="Edit" @click="done => handleEdit(scope.row, done)">编辑</elx-loading-button>
        <elx-loading-button v-if="hasPermission(['A'])" type="primary" size="small" plain icon="Edit" @click="done => handleView(scope.row, done)">查看</elx-loading-button>
        <elx-loading-button v-if="hasPermission(['A'])" :disabled="scope.row.modelType !== '1'" type="" size="small" plain icon="Download" @click="done => handleExport(scope.row, done)">导出</elx-loading-button>
        <elx-loading-button v-if="hasPermission(['A', 'C', 'D'])" type="danger" size="small" plain icon="Delete" @click="done => handleDelete(scope.row, done)">删除</elx-loading-button>
      </template>
    </el-table-column>
  </elx-table-panel>
  <ModelImportDialog ref="ModelImportDialogRef"/>
</template>

<script setup>
import {search_model_api, delete_model_api, export_model_api, refresh_model_api} from '@/api/workbench/model'
import {ref, reactive} from 'vue'
import {useRouter} from 'vue-router'
const router = useRouter()
import { ElMessageBox } from 'element-plus'
import ModelImportDialog from './dialog/ModelImportDialog.vue'
import {Select, CloseBold} from '@element-plus/icons-vue'

const searchKey = ref('')

const dataList = ref([])
const pagination = reactive({
  pageNumber: 1,
  pageSize: 20
})
const searchLoading = ref(false)

const handleCreate = () => {
  router.push('/workbench/model/create')
}

const ModelImportDialogRef = ref()
const handleUpload = () => {
  ModelImportDialogRef.value.open(() => {
    handleSearch()
  })
}

const handleExport = (row, done) => {
  export_model_api(row.modelId).then(res => {
  }).catch(e => {
  }).finally(() => {
    done()
  })
}

const handleSearch = () => {
  searchLoading.value = true
  search_model_api(searchKey.value, pagination).then(res => {
    dataList.value = res.data || []
    Object.assign(pagination, res.pagination)
  }).catch(() => {
  }).finally(() => {
    searchLoading.value = false
  })
}
handleSearch()

const handleEdit = (row, done) => {
  router.push('/workbench/model/edit/' + row.modelId)
}

const handleRefreshModel = (row, done) => {
  refresh_model_api(row.modelId).then(res => {
    handleSearch()
  }).catch(() => {
  }).finally(() => {
    done && done()
  })
}

const handleView = (row, done) => {
  router.push('/workbench/model/view/' + row.modelId)
}

const handleDelete = (row, done) => {
  ElMessageBox.confirm('确定删除该匹配模型吗？', '确认', { type: 'warning' }).then(() => {
    searchLoading.value = true
    delete_model_api(row.modelId).then(res => {
      handleSearch()
    }).catch(() => {
    }).finally(() => {
      done && done()
      searchLoading.value = false
    })
  }).catch(e => {
    done && done()
  })
}
</script>
