<template>
  <elx-dialog-ex ref="dialog" title="导入模型" confirm-text="导入" width="600px">
    <el-form ref="formRef" :model="form" :rules="validRules" label-width="100px" label-position="left">
      <el-form-item prop="modeName" label="模型名称">
        <el-input v-model="form.modeName"/>
      </el-form-item>
      <el-form-item prop="file" label="文件">
        <el-upload
            action=""
            :accept="'.MOD'"
            :http-request="f => handleAddFile(f.file)"
            :limit="1"
            :on-success="() => {}"
            :on-error="() => {}"
            :before-upload="() => {}"
            class="w-380px"
        >
          <el-button type="">选择模型文件</el-button>
        </el-upload>
      </el-form-item>
    </el-form>
  </elx-dialog-ex>
</template>

<script setup>
import {nextTick, reactive, ref} from 'vue'
import {import_model_api} from '@/api/workbench/model'
const dialog = ref()

const form = reactive({
  modeName: '',
  file: null
})

const validRules = {
  modeName: [
    {required: true, message: '必须输入模型文件', trigger: 'blur'}
  ],
  file: [
    {required: true, validator: (rule, value, callback) => {
        if (value === null) {
          callback(new Error('请选择模型文件'))
        } else {
          callback()
        }
      }, trigger: 'blur'}
  ]
}

const handleAddFile = (file) => {
  form.file = file
}

const formRef = ref()
const open = (callback) => {
  form.file = null
  dialog.value.open((done, close) => {
    formRef.value.validate(valid => {
      if (valid) {
        import_model_api(form).then(res => {
          close()
          callback()
        }).catch(e => {
        }).finally(() => {
          done()
        })
      } else {
        done()
      }
    })
  })
}

defineExpose({
  open
})
</script>
