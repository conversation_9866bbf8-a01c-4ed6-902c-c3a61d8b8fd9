<template>
  <elx-page-card v-loading="loading" :save-button="false">
    <elx-block-header title="匹配模型属性">
      <el-form ref="formRef" :model="form" :inline="true" label-width="120px">
        <el-row>
          <el-form-item label="匹配模型名称" prop="modelName">
            <el-input v-model="form.modelName" readonly class="w-300px"/>
          </el-form-item>
          <el-form-item label="匹配模型类型" prop="modelName">
            <el-input :model-value="modelTypeOptions.find(e => e.value === form.modelType)?.label" readonly class="w-160px"/>
          </el-form-item>
          <el-form-item v-if="form.modelType === '2'" label="AI模型库" prop="aiModelId" label-width="80px">
            <el-input :model-value="aiModelOptions.find(e => e.value === form.aiModelId)?.label" readonly class="w-160px"/>
          </el-form-item>
          <el-form-item label="描述(选填)" prop="remark">
            <el-input v-model="form.remark" readonly class="w-500px"/>
          </el-form-item>
        </el-row>
      </el-form>
    </elx-block-header>

    <elx-block-header title="匹配模型属性" style="margin-top: -10px">
      <el-table v-if="form.modelType === '1'" :data="labelList" max-height="calc(100vh - 350px)" scrollbar-always-on>
        <el-table-column type="index" label="编码" width="60px"/>
        <el-table-column prop="labelText" label="标签名称" width="300px">
          <template #default="scope">
            <el-input v-model="scope.row.labelText" readonly/>
            <span v-if="scope.row.errors.labelText" class="el-table-item__error">{{ scope.row.errors.labelText }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="length" label="关键字/分值" width="650px">
          <template #header>
            <span>
              关键字/分值
            </span>
          </template>
          <template #default="scope">
            <div>
              <el-tag v-for="(item, i) in getLabelKeys(scope.row.labelKeyText)" :key="i" :type="item.type" effect="plain" class="mr-2px" disable-transitions>{{item.text}}</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="" label="基本权重" width="130px">
          <template #default="scope">
            <el-input v-model="scope.row.basicWeight" readonly style="width: 90px"/>
          </template>
        </el-table-column>
        <el-table-column/>
      </el-table>
      <el-row v-if="form.modelType === '2'" :gutter="20">
        <el-col :span="14">
          <el-table :data="aiLabelList" max-height="calc(100vh - 380px)" scrollbar-always-on>
            <el-table-column type="index" label="#" width="50px"/>
            <el-table-column label="上级分类" width="200px">
              <template #default="scope">
                <el-input :model-value="scope.row.category" readonly/>
              </template>
            </el-table-column>
            <el-table-column prop="labelText" label="标签名称" width="220px">
              <template #default="scope">
                <el-input v-model="scope.row.labelText" readonly/>
              </template>
            </el-table-column>
            <el-table-column prop="labelText" label="补充说明">
              <template #default="scope">
                <el-input v-model="scope.row.remarkText" readonly class="w-380px"/>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col :span="10">
          <el-table :data="aiRuleList" max-height="calc(100vh - 380px)" scrollbar-always-on>
            <el-table-column type="index" label="#" width="40px"/>
            <el-table-column label="规则">
              <template #default="scope">
                <el-input v-model="scope.row.ruleText" type="textarea" readonly class="w-500px"/>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
    </elx-block-header>
  </elx-page-card>
</template>

<script setup>
import {model_get_api, model_save_api} from '@/api/workbench/model'
import {reactive, ref} from 'vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import {useRouter, useRoute} from 'vue-router'

const loading = ref(true)
const form = reactive({
  modelId: '',
  modelName: '',
  modelType: '',
  aiModelId: '',
  remark: ''
})

const labelList = ref([])
const aiLabelList = ref([])
const aiRuleList = ref([])

const router = useRouter()
const route = useRoute()

const visible = ref(false)

const optType = route.meta.optType
const modelId = route.params.key
const modelTypeOptions = ref([])
const aiModelOptions = ref([])

const handleInit = () => {
  if (optType === 'edit' || optType === 'view') {
    model_get_api(modelId, false).then(res => {

      modelTypeOptions.value = res.dataMap.modelTypeOptions || []
      aiModelOptions.value = res.dataMap.aiModelOptions || []

      form.modelId = modelId
      form.modelName = res.data.modelName
      form.modelType = res.data.modelType
      form.aiModelId = res.data.aiModelId
      form.remark = res.data.remark

      labelList.value = res.data.labelList || []
      aiLabelList.value = res.data.aiLabelList || []
      aiRuleList.value = res.data.aiRuleList || []
    }).catch(e => {
      if (e.custom === true) {
        ElMessageBox.confirm(e.messages[0].message, '返回',
            {
              confirmButtonText: '确认',
              showCancelButton: false,
              closeOnClickModal: false,
              closeOnPressEscape: false,
              closeOnHashChange: false,
              showClose: false,
              type: 'error'
            }
        ).then(() => {
          router.replace('/workbench/model')
        })
      }
    }).finally(() => {
      loading.value = false
    })
  } else {
    loading.value = false
  }
}

const formRef = ref()

const getLabelKeys = (value) => {

  if (value) {
    const tt = value.trim().split(';')
    let keys = []
    for (let i = 0; i < tt.length; i++) {
      const text = tt[i].trim()
      keys.push({
        text: text,
        type: /.+?#[0-9.\-]+$/.test(text) ? '' : 'info'
      })
    }
    return keys
  }
  return []
}

handleInit()

</script>
