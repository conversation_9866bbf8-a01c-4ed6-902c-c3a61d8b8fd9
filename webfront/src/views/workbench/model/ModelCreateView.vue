<template>
  <elx-page-card v-loading="loading" save-button @save="handleSave">
    <elx-block-header title="匹配模型属性">
      <el-form ref="formRef" :model="form" :rules="validRules" :inline="true" label-width="120px">
        <el-row>
          <el-form-item label="匹配模型名称" prop="modelName">
            <el-input v-model="form.modelName" class="w-300px"/>
            <elx-tips>
              <div>匹配模型名称不能够重复。</div>
            </elx-tips>
          </el-form-item>
          <el-form-item label="匹配模型类型" prop="modelType">
            <el-select-v2 v-model="form.modelType" :options="modelTypeOptions" class="w-160px"/>
          </el-form-item>
          <el-form-item v-if="form.modelType === '2'" label="AI模型库" prop="aiModelId" label-width="80px">
            <el-select-v2 v-model="form.aiModelId" :options="aiModelOptions" class="w-160px"/>
          </el-form-item>
          <el-form-item label="描述(选填)" prop="remark">
            <el-input v-model="form.remark" class="w-400px"/>
          </el-form-item>
        </el-row>
      </el-form>
    </elx-block-header>

    <elx-block-header title="匹配模型属性" style="margin-top: -10px">
      <el-table v-if="form.modelType === '1'" :data="labelList" max-height="calc(100vh - 350px)" scrollbar-always-on>
        <el-table-column type="index" label="编码" width="60px"/>
        <el-table-column prop="labelText" label="标签名称" width="300px">
          <template #default="scope">
            <el-input v-model="scope.row.labelText"/>
            <span v-if="scope.row.errors.labelText" class="el-table-item__error">{{ scope.row.errors.labelText }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="length" label="关键字/分值" width="650px">
          <template #header>
            <span>
              关键字/分值
            </span>
            <elx-tips style="display: inline-block; margin-left: 10px">格式：【关键字1#分值1;关键字2#分值2;关键字3#分值3...;基本权重】</elx-tips>
          </template>
          <template #default="scope">
            <el-input v-if="scope.row.editMode" v-model="scope.row.labelKeyText" type="textarea" style="width: 620px"/>
            <div v-else>
              <el-tag v-for="(item, i) in getLabelKeys(scope.row.labelKeyText)" :key="i" :type="item.type" effect="plain" class="mr-2px" disable-transitions>{{item.text}}</el-tag>
            </div>
            <span v-if="scope.row.errors.labelKeyText" class="el-table-item__error">{{ scope.row.errors.labelKeyText }}</span>
          </template>
        </el-table-column>
        <el-table-column label="" width="50px">
          <template #default="scope">
            <el-button v-if="!scope.row.editMode" icon="Edit" size="small" @click="scope.row.editMode = true"/>
            <el-button v-else icon="Select" size="small" @click="scope.row.editMode = false"/>
          </template>
        </el-table-column>
        <el-table-column prop="" label="基本权重" width="130px">
          <template #default="scope">
            <el-input v-model="scope.row.basicWeight" type="number" style="width: 90px"/>
          </template>
        </el-table-column>
        <el-table-column label="" align="right" header-align="right" min-width="200px">
          <template #header>
            <el-popover :visible="visible" placement="bottom-end" :width="800" trigger="click">
              <template #reference>
                <el-button size="small" type="info" plain icon="Document" class="mr-2" style="vertical-align: -3px" @click="batchImportText = ''; visible = true">批量导入</el-button>
              </template>
              <div>
                <el-input v-model="batchImportText" type="textarea" :rows="12" placeholder="例： 阳光房;阳光房#0.815;天井#-0.157;围栏#-0.135;玻璃#0.072;0.029" style="overflow-x: auto"/>
                <el-button size="small" type="" icon="" plain style="margin: 10px 0 0 595px; width: 80px" @click="visible = false">取消</el-button>
                <el-button size="small" type="primary" icon="" plain style="margin: 10px 0 0 10px; width: 80px" @click="handleImport">导入</el-button>
              </div>
            </el-popover>
            <el-button size="small" type="primary" icon="Plus" @click="() => handlePlus(1)"></el-button>
          </template>
          <template #default="scope">
            <el-button :disabled="scope.$index === 0" size="small" type="" icon="Top" @click="handleMoveUp(labelList, scope.$index)"></el-button>
            <el-button :disabled="scope.$index === labelList.length - 1" size="small" type="" icon="Bottom" @click="handleMoveDown(labelList, scope.$index)"></el-button>
            <el-button size="small" type="" icon="Plus" @click="handleAdd(1, scope.$index)"></el-button>
            <el-button size="small" type="danger" icon="Close" @click="handleRemove(labelList, scope.$index)"></el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-row v-if="form.modelType === '2'" :gutter="20">
        <el-col :span="currentLabel ? 15 : 24">
          <el-table :data="aiLabelList" max-height="calc(100vh - 380px)" scrollbar-always-on>
            <el-table-column type="index" label="#" width="40px"/>
            <el-table-column label="上级分类" width="190px">
              <template #default="scope">
                <el-select-v2 v-model="scope.row.category" :options="aiCategoryOptions" filterable allow-create/>
                <span v-if="scope.row.errors.category" class="el-table-item__error">{{ scope.row.errors.category }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="labelText" label="标签名称" width="180px">
              <template #default="scope">
                <el-input v-model="scope.row.labelText"/>
                <span v-if="scope.row.errors.labelText" class="el-table-item__error">{{ scope.row.errors.labelText }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="labelText" label="补充说明（选填）">
              <template #default="scope">
                <el-input v-model="scope.row.remarkText" class="w-320px" clearable/>
              </template>
            </el-table-column>
            <el-table-column label="" align="right" header-align="right" width="290px">
              <template #header>
                <el-popover :visible="visible2" placement="bottom-end" :width="800" trigger="click">
                  <template #reference>
                    <el-button size="small" type="info" plain icon="Document" class="mr-2" style="vertical-align: -3px" @click="visible2 = true">批量导入</el-button>
                  </template>
                  <div>
                    <el-input v-model="modelEditText" type="textarea" :rows="12" placeholder="例： #上级分类##标签名称//补充说明" style="overflow-x: auto"/>
                    <el-button size="small" type="" icon="" plain style="margin: 10px 0 0 595px; width: 80px" @click="visible2 = false">取消</el-button>
                    <el-button size="small" type="primary" icon="" plain style="margin: 10px 0 0 10px; width: 80px" @click="handleAiLabelEdit">确认</el-button>
                  </div>
                </el-popover>
                <el-button size="small" type="primary" icon="Plus" @click="() => handlePlus(2)"></el-button>
              </template>
              <template #default="scope">
                <el-button size="small" type="primary" icon="Share" plain @click="showCaseList(aiLabelList, scope.$index)">案例</el-button>
                <el-button :disabled="scope.$index === 0" size="small" type="" icon="Top" @click="handleMoveUp(aiLabelList, scope.$index)"></el-button>
                <el-button :disabled="scope.$index === labelList.length - 1" size="small" type="" icon="Bottom" @click="handleMoveDown(aiLabelList, scope.$index)"></el-button>
                <el-button size="small" type="" icon="Plus" @click="handleAdd(2, scope.$index)"></el-button>
                <el-button size="small" type="danger" icon="Close" @click="handleRemove(aiLabelList, scope.$index)"></el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col :span="currentLabel ? 9 : 0">
          <el-table :data="caseList" max-height="calc(100vh - 380px)" scrollbar-always-on>
            <el-table-column type="index" label="#" width="33px"/>
            <el-table-column :label="'案例（#' + currentCategory + '## ' + currentLabel + '）'">
              <template #default="scope">
                <el-input v-model="scope.row.ct" type="textarea" class="w-500px"/>
                <span v-if="scope.row.errors" class="el-table-item__error">{{ scope.row.errors }}</span>
              </template>
            </el-table-column>
            <el-table-column label="" align="right" header-align="right" width="60px">
              <template #header>
                <el-button size="small" type="primary" icon="Plus" @click="() => handlePlus(3)"></el-button>
              </template>
              <template #default="scope">
                <el-button size="small" type="danger" icon="Close" @click="handleRemove(caseList, scope.$index)"></el-button>
              </template>
            </el-table-column>
          </el-table>
          <elx-tips style="margin-left: 55px">案例示例：消防通道被车辆占用，优先标签是“违规占用消防通道”，而不是“车辆无序停放”。</elx-tips>
        </el-col>
      </el-row>
      <span v-if="labelListError" class="el-span__error">{{labelListError}}</span>
    </elx-block-header>
  </elx-page-card>
</template>

<script setup>
import {model_get_api, model_save_api} from '@/api/workbench/model'
import {reactive, ref, onMounted, watch, computed} from 'vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import {useRouter, useRoute} from 'vue-router'

const loading = ref(true)
const form = reactive({
  modelId: '',
  modelName: '',
  modelType: '2',
  aiModelId: '',
  remark: '',
})

const labelList = ref([])
const aiLabelList = ref([])
const aiRuleList = ref([])

const labelListError = ref('')
const router = useRouter()
const route = useRoute()

const visible = ref(false)
const visible2 = ref(false)
const batchImportText = ref('')

const optType = route.meta.optType
const modelId = route.params.key
const modelTypeOptions = ref([])
const aiModelOptions = ref([])
const aiCategoryOptions = ref([])
const currentCategory = ref('')
const currentLabel = ref('')

const caseList = computed(() => {
  if (!currentLabel.value || aiLabelList.value.length === 0) {
    return []
  }
  const data = aiLabelList.value.filter(e => e.labelText === currentLabel.value)
  if (data.length > 0) {
    const cList = data[0].caseList || []
    cList.forEach(v => v.errors = '')
    return cList
  }
  return []
})

const handleInit = () => {

  model_get_api(modelId, optType !== 'edit').then(res => {
    modelTypeOptions.value = res.dataMap.modelTypeOptions || []
    aiModelOptions.value = res.dataMap.aiModelOptions || []
    aiCategoryOptions.value = res.dataMap.aiCategoryOptions || []
    if (optType === 'edit') {
      form.modelId = modelId
      form.modelName = res.data.modelName
      form.modelType = res.data.modelType
      form.aiModelId = res.data.aiModelId
      form.remark = res.data.remark
      labelList.value = res.data.labelList || []
      aiLabelList.value = res.data.aiLabelList || []
      aiRuleList.value = res.data.aiRuleList || []
    }
  }).catch(e => {
    if (e.custom === true) {
      ElMessageBox.confirm(e.messages[0].message, '返回',
          {
            confirmButtonText: '确认',
            showCancelButton: false,
            closeOnClickModal: false,
            closeOnPressEscape: false,
            closeOnHashChange: false,
            showClose: false,
            type: 'error'
          }
      ).then(() => {
        router.replace('/workbench/model')
      })
    }
  }).finally(() => {
    loading.value = false
  })
}

onMounted(() => {
  handleInit();
})

const modelEditText = ref('')
// watch(() => aiLabelList.value, (values) => {
//   modelEditText.value = values.map(e => {
//     if (e.remarkText) {
//       return `#${e.category || ''}##${e.labelText}//${e.remarkText}`
//     } else {
//       return `#${e.category || ''}##${e.labelText}`
//     }
//   }).join('\n')
// }, {immediate: true, deep: true})

const handleAiLabelEdit = () => {
  const values = modelEditText.value.trim().split('\n')
  for (let value of values) {
    if (value.trim().length > 0) {
      const match = value.trim().match(/^#(.*?)##(.*?)\s*(\/\/(.*))?$/);
      if (match) {
        aiLabelList.value.push({
          category: match[1] ? match[1].trim() : '',
          labelText: match[2] ? match[2].trim() : '',
          remarkText: match[4] ? match[4].trim() : '',
          caseList: [],
          errors: {}
        })
      }
    }
  }
  modelEditText.value = ''
  visible2.value = false
}

const handlePlus = (type) => {
  if (type === 1) {
    labelList.value.push({
      labelText: '',
      labelKeyText: '',
      basicWeight: 1.0,
      editMode: true,
      errors: {}
    })
  } else if (type === 2) {
    aiLabelList.value.push({
      labelText: '',
      remarkText: '',
      caseList: [],
      errors: {}
    })
  } else if (type === 3) {
    const current = aiLabelList.value.filter(e => e.labelText === currentLabel.value)
    current[0].caseList.push({
      ct: '',
      errors: ''
    })
  }
}

const handleAdd = (type, index) => {
  if (type === 1) {
    const data = {
      labelText: '',
      labelKeyText: '',
      basicWeight: 1.0,
      editMode: false,
      errors: {}
    }
    if (index === labelList.value.length - 1) {
      labelList.value.push(data)
    } else {
      labelList.value.splice(index + 1, 0, data)
    }
  } else if (type === 2) {
    const data = {
      labelText: '',
      remarkText: '',
      caseList: [],
      errors: {}
    }
    if (index === aiLabelList.value.length - 1) {
      aiLabelList.value.push(data)
    } else {
      aiLabelList.value.splice(index + 1, 0, data)
    }
  } else if (type === 3) {
    const data = {
      ruleText: '',
      errors: {}
    }
    if (index === aiRuleList.value.length - 1) {
      aiRuleList.value.push(data)
    } else {
      aiRuleList.value.splice(index + 1, 0, data)
    }
  }
}

const handleRemove = (dataList, index) => {
  dataList.splice(index, 1)
}

const validRules = reactive({
  modelName: [
    {required: true, message: '请输入匹配模型名称', trigger: 'blur'}
  ],
  modelType: [
    {required: true, message: '请选择匹配模型类型', trigger: 'blur'}
  ],
  aiModelId: [
    {required: true, validator: (rule, value, callback) => {
        if (form.modelType === '2' && !form.aiModelId) {
          callback(new Error('请选择AI模型库'))
        } else {
          callback()
        }
      }
    }
  ]
})

const formRef = ref()

const checkLabelList = () => {
  let error = labelList.value.length === 0
  labelListError.value = labelList.value.length === 0 ? '请添加标签数据。' : ''
  labelList.value.forEach(row => {
    if (row.labelText) {
      row.errors.labelText = ''
    } else {
      error = true
      row.errors.labelText = '请输入标签名称'
    }
    if (row.labelKeyText) {
      row.errors.labelKeyText = ''
    } else {
      error = true
      row.errors.labelKeyText = '请输入关键字/分值'
    }
  })
  return !error
}

const checkAiLabelList = () => {
  let error = aiLabelList.value.length === 0
  labelListError.value = aiLabelList.value.length === 0 ? '请添加标签数据。' : ''
  aiLabelList.value.forEach(row => {
    if (row.category) {
      row.errors.category = ''
    } else {
      error = true
      row.errors.category = '请选择上级分类'
    }
    if (row.labelText) {
      row.errors.labelText = ''
    } else {
      error = true
      row.errors.labelText = '请输入标签名称'
    }
  })
  return !error
}

const handleSave = async(done) => {
  const valid1 = await formRef.value.validate().catch(e => {})
  const valid2 = form.modelType === '1' ? checkLabelList() : true
  const valid3 = form.modelType === '2' ? checkAiLabelList() : true
  if (valid1 && valid2 && valid3) {
    model_save_api(form, optType, labelList.value, aiLabelList.value, aiRuleList.value).then(res => {
      router.replace('/workbench/model')
    }).catch(() => {
    }).finally(() => {
      done()
    })
  } else {
    ElMessage.error('页面项目输入不正确，请确认后再保存。')
    done()
  }
}

const handleImport = () => {
  const values = batchImportText.value.trim().split('\n')
  for (let value of values) {
    if (value.trim().length > 0) {
      const tt = value.trim().split(';')
      let text = ''
      let basic = ''
      let keys = []
      for (let i = 0; i < tt.length; i++) {
        if (i === 0) {
          text = tt[i].trim()
        } else if (i === tt.length - 1) {
          basic = tt[i].trim()
        } else {
          keys.push(tt[i].trim())
        }
      }

      labelList.value.push({
        labelText: text,
        labelKeyText: keys.join(';'),
        basicWeight: basic,
        errors: {}
      })
    }
  }
  visible.value = false
}


const getLabelKeys = (value) => {

  if (value) {
    const tt = value.trim().split(';')
    let keys = []
    for (let i = 0; i < tt.length; i++) {
      const text = tt[i].trim()
      keys.push({
        text: text,
        type: /.+?#[0-9.\-]+$/.test(text) ? '' : 'info'
      })
    }
    return keys
  }
  return []
}

const handleMoveUp = (dataList, index, row) => {
  if (index > 0) {
    const upData = dataList[index - 1]
    dataList.splice(index - 1, 1)
    dataList.splice(index, 0, upData)
  }
}

const  handleMoveDown = (dataList, index, row) => {
  const downData = dataList[index + 1]
  dataList.splice(index + 1, 1)
  dataList.splice(index, 0, downData)
}

const showCaseList = (dataList, index) => {
  const data = dataList[index]
  currentCategory.value = data.category
  currentLabel.value = data.labelText
}

</script>
