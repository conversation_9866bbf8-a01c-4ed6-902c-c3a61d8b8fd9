<template>
  <elx-dialog-ex ref="dialog" :confirm-button-disabled="step !== 2" title="导出数据" confirm-text="导出" width="800px">
    <div v-loading="loading" style="margin-top: -12px; min-height: 400px">
      <div v-if="step === 0">
        <elx-block-header title="基本信息"/>
        <el-form ref="step0Form" :model="form" :rules="step0Rules" label-width="110px" label-position="left">
          <el-form-item label="文件名" prop="fileName">
            <el-input v-model="form.fileName"/>
          </el-form-item>
          <elx-block-header title="数据格式"/>
          <el-form-item label="日期格式" prop="dateFormat">
            <el-input v-model="form.dateFormat"/>
          </el-form-item>
          <el-form-item label="时间格式" prop="timeFormat">
            <el-input v-model="form.timeFormat"/>
          </el-form-item>
          <el-form-item label="日期时间格式" prop="dateTimeFormat">
            <el-input v-model="form.dateTimeFormat"/>
          </el-form-item>
          <el-form-item label="数值格式" prop="numberFormat">
            <el-input v-model="form.numberFormat"/>
          </el-form-item>
        </el-form>
      </div>
      <div v-if="step === 1">
        <elx-block-header title="导出列"/>
        <el-radio-group v-model="form.columnType" style="margin-bottom: 10px">
          <el-radio-button label="1">导出所有列</el-radio-button>
          <el-radio-button :disabled="!props.filter" label="2">手动指定列</el-radio-button>
        </el-radio-group>
        <el-form ref="step1Form" :model="form" :rules="step1Rules">
          <el-form-item prop="exportColumns">
            <el-transfer v-if="form.columnType === '2'" v-model="form.exportColumns" :data="columnList" :titles="['所有列', '选定列']"/>
          </el-form-item>
        </el-form>
      </div>
      <div v-if="step === 2">
        <elx-block-header title="导出条件"/>
        <el-radio-group v-model="form.filterType" style="margin-bottom: 10px">
          <el-radio-button label="1">导出所有数据</el-radio-button>
          <el-radio-button :disabled="!props.filter" label="2">手动指定条件</el-radio-button>
        </el-radio-group>
        <div v-if="form.filterType === '2'" style="margin-top: 10px">
          <elx-filter-panel :table-id="tableId" :columns="tableMeta" :filters.sync="filterForm" :tags-options="tagsOptions" :hidden-tag-filter="form.tableType !== '1'" border/>
        </div>
      </div>
    </div>
    <template #customButton>
      <el-button-group>
        <el-button :disabled="step === 0" icon="ArrowLeft" @click="handlePre">上一步</el-button>
        <el-button :disabled="step === 2" @click="handleNext">下一步<el-icon class="el-icon--right"><ArrowRight /></el-icon></el-button>
      </el-button-group>
    </template>
  </elx-dialog-ex>
</template>

<script setup>
import {parseTime} from '@/common/utils'
import {reactive, ref} from 'vue'
import { ElMessageBox } from 'element-plus'

const props = defineProps({
  filter: {
    type: Boolean,
    default: () => true
  }
})

let tableId = ''
let tableMeta = []
const loading = ref(false)
const step = ref(0)
const tagsOptions = ref([])

const form = reactive({
  tableType: '',
  fileName: '',
  dateFormat: 'yyyy-MM-dd',
  timeFormat: 'hh24:mm:ss',
  dateTimeFormat: 'yyyy-MM-dd hh24:mm:ss',
  numberFormat: '#.###',
  columnType: '1',
  exportColumns: [],
  filterType: '1',
  filter: [],
  dateRange: [],
  tags: [],
  itemFilters: [],
  itemFilterMode: '1'
})

const filterForm = reactive({
  dateRange: [],
  tags: [],
  itemFilterMode: '1',
  itemFilters: [],
  customFilter: ''
})

const step0Rules = {
  fileName: [
    {required: true, message: '必须输入文件名', trigger: 'blur'}
  ]
}
const step1Rules = {
  exportColumns: [
    {required: true, message: '请选择要导出的列', trigger: 'blur'}
  ]
}

let columnList = []

const dialog = ref()
const open = (type, id, name, meta, tags, exportFunc_api, callback) => {
  form.tableType = type
  tableId = id
  tableMeta = meta
  step.value = 0
  columnList.length = 0
  form.fileName = name + '_' + parseTime(new Date(), '{y}{m}{d}{h}{i}{s}') + '.xlsx'
  form.columnType = '1'
  form.filterType = '1'
  form.exportColumns.length = 0
  form.dateRange.length = 0
  form.tags.length = 0
  tagsOptions.value = tags

  meta.forEach(m => {
    columnList.push({ key: m.id, label: m.name + (m.score === true ? '(算分)' : '') })
  })

  dialog.value.open((done, close) => {
    loading.value = true
    exportFunc_api(tableId, form, filterForm).then(res => {
      ElMessageBox.confirm('导出命令已经下发，请转到【数据导出历史】对话框，下载文件。', '确认',
          { type: 'success',
            showClose: false,
            showCancelButton: false,
            closeOnClickModal: false,
            closeOnPressEscape: false,
            confirmButtonText: '打开【数据导出历史】对话框'
          }
      ).then(res => {
        callback()
      }).catch(() => {
      })
      close()
    }).catch(e => {
    }).finally(() => {
      done()
      loading.value = false
    })
  })
}

const handlePre = () => {
  step.value = step.value - 1
}

const step0Form = ref()
const step1Form = ref()
const handleNext = () => {
  step.value === 0 && step0Form.value.validate(valid => {
    if (valid) {
      step.value = step.value + 1
    }
  })
  if (step.value === 1) {
    if (form.columnType === '2') {
      step1Form.value.validate(valid => {
        if (valid) {
          step.value = step.value + 1
        }
      })
    } else {
      step.value = step.value + 1
    }
  }
}

defineExpose({
  open
})
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
:deep(.el-transfer-panel) {
  width: 245px !important;
}
</style>
