<template>
  <elx-dialog-ex ref="dialog" :cancel-button="false" title="数据导出历史" width="1200px">
    <div v-loading="loading">
      <elx-block-header :title="(tableMode === 1 ? '视图名称：' : '数据表名：') + tableName" style="margin-top: -10px">
        <template #extra>
          <elx-loading-button type="primary" icon="Refresh" size="small" @click="search">刷新</elx-loading-button>
        </template>
      </elx-block-header>
      <el-table ref="tablePanel" :data="dataList" style="height: 500px">
        <el-table-column label="文件名称" prop="exportFile" width="380px"/>
        <el-table-column label="状态" prop="statusName" width="140px">
          <template #default="scope">
            <el-tag :type="statusType(scope.row.status)">{{scope.row.statusName}}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="导出者" prop="userName" width="140px"/>
        <el-table-column label="导出时间" prop="createdAt" width="160px"/>
        <el-table-column label="导出件数" prop="dataCount"/>
        <el-table-column width="120" align="right">
          <template #default="scope">
            <elx-loading-button :disabled="scope.row.status !== '1'" type="primary" size="small" icon="download" plain @click="done => handleDownload(scope.row, done)">
              下载
            </elx-loading-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </elx-dialog-ex>
</template>

<script setup>
import {search_export_history_list_api, download_file_api} from '@/api/workbench/table'

import {ref} from 'vue'
let tableName = ''
let tableId = ''
let tableMode = ''

const loading = ref(false)
const dataList = ref([])

const dialog = ref()
const open = (_tableId, _tableMode, name) => {
  tableName = name
  tableId = _tableId
  tableMode = _tableMode
  dialog.value.openWithInit(search, (done, close) => {
    done()
    close()
  })
}

const search = (done) => {
  loading.value = true
  search_export_history_list_api(tableId, tableMode).then(res => {
    dataList.value = res.data || []
  }).catch(e => {}).finally(() => {
    loading.value = false
    done && done()
  })
}

const statusType = (status) => {
  return status === '0' ? 'warning' : status === '1' ? 'success' : 'danger'
}

const handleDownload = (row, done) => {
  loading.value = true
  download_file_api(row.fileId).then(res => {
  }).catch(e => {
  }).finally(() => {
    done()
    loading.value = false
  })
}

defineExpose({
  open
})
</script>
