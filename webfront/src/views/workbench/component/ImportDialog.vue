<template>
  <elx-dialog-ex ref="copyDialogRef" title="数据模板上传" :ok-button="false" width="500px">
    <el-form label-width="120px" label-position="left">
      <el-form-item label="数据模板下载">
        <el-link type="primary" icon="Download" @click="handleDownLoad">{{templateName}}</el-link>
      </el-form-item>
      <el-form-item label="上传/批量生成">
        <elx-upload-button type="primary" icon="Upload" plain style="vertical-align: -2.3px" :upload-func="handleUpload">批量生成</elx-upload-button>
      </el-form-item>
      <elx-tips>备注：</elx-tips>
      <elx-tips>
        1、先下载模板，然后将数据填充后，再上传，批量生成。
      </elx-tips>
      <elx-tips v-if="tips">
        2、{{tips}}
      </elx-tips>
    </el-form>
  </elx-dialog-ex>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { template_download_api } from '@/api/workbench/table'
const props = defineProps({
  template: {
    type: String,
    required: true
  },
  templateName: {
    type: String,
    required: true
  },
  tips: {
    type: String,
    default: () => ''
  }
})

let callback = null
const copyDialogRef = ref()
const open = (_callback) => {
  callback = _callback
  copyDialogRef.value.open((done, close) => {
    done()
    close()
  })
}

const handleDownLoad = () => {
  template_download_api(props.template).then(res => {
  }).catch(r => {
  }).finally(() => {
  })
}

const handleUpload = (file) => {
  callback(file)
  copyDialogRef.value.close()
}

defineExpose({
  open
})
</script>
