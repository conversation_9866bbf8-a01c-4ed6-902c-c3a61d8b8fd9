<template>
  <div class="chat-wrapper">
    <div class="header">
      <div class="logo">AI 标签</div>
      <el-select-v2 v-model="modelType" :options="[{label : '模型库-00', value: '00'}]"
                    class="model-selector"/>
    </div>

    <div ref="chatContainerRef" class="main" id="chat-container">
      <div v-if="messageList.length === 0" class="message">
        <div class="avatar assistant-avatar">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none"
               xmlns="http://www.w3.org/2000/svg">
            <path
                d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM7 13.5C7 12.67 6.33 12 5.5 12C4.67 12 4 12.67 4 13.5C4 14.33 4.67 15 5.5 15C6.33 15 7 14.33 7 13.5ZM10.5 12C9.67 12 9 12.67 9 13.5C9 14.33 9.67 15 10.5 15C11.33 15 12 14.33 12 13.5C12 12.67 11.33 12 10.5 12ZM15.5 12C14.67 12 14 12.67 14 13.5C14 14.33 14.67 15 15.5 15C16.33 15 17 14.33 17 13.5C17 12.67 16.33 12 15.5 12Z"
                fill="currentColor"/>
          </svg>
        </div>
        <div class="content assistant-message">
          你好！我是AI Chat，有什么需要AI分析的吗？
        </div>
      </div>
      <div v-for="(m, index) in messageList" :key="index" class="message">
        <div v-if="m.type === '0'" class="avatar user-avatar">U</div>
        <div v-else class="avatar assistant-avatar">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none"
               xmlns="http://www.w3.org/2000/svg">
            <path
                d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM7 13.5C7 12.67 6.33 12 5.5 12C4.67 12 4 12.67 4 13.5C4 14.33 4.67 15 5.5 15C6.33 15 7 14.33 7 13.5ZM10.5 12C9.67 12 9 12.67 9 13.5C9 14.33 9.67 15 10.5 15C11.33 15 12 14.33 12 13.5C12 12.67 11.33 12 10.5 12ZM15.5 12C14.67 12 14 12.67 14 13.5C14 14.33 14.67 15 15.5 15C16.33 15 17 14.33 17 13.5C17 12.67 16.33 12 15.5 12Z"
                fill="currentColor"/>
          </svg>
        </div>
        <div :class="{'user-message': m.type === '0', 'assistant-message': m.type === '1'}"
             class="content">
          {{ m.text }}
        </div>
      </div>

      <div v-if="loading" class="message">
        <div class="avatar assistant-avatar">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none"
               xmlns="http://www.w3.org/2000/svg">
            <path
                d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM7 13.5C7 12.67 6.33 12 5.5 12C4.67 12 4 12.67 4 13.5C4 14.33 4.67 15 5.5 15C6.33 15 7 14.33 7 13.5ZM10.5 12C9.67 12 9 12.67 9 13.5C9 14.33 9.67 15 10.5 15C11.33 15 12 14.33 12 13.5C12 12.67 11.33 12 10.5 12ZM15.5 12C14.67 12 14 12.67 14 13.5C14 14.33 14.67 15 15.5 15C16.33 15 17 14.33 17 13.5C17 12.67 16.33 12 15.5 12Z"
                fill="currentColor"/>
          </svg>
        </div>
        <div class="content assistant-message">
          <div class="typing-indicator">
            <div class="typing-dot"></div>
            <div class="typing-dot"></div>
            <div class="typing-dot"></div>
          </div>
        </div>
      </div>
    </div>

    <div class="footer">
      <div class="input-container">
        <div :class="{ 'is-focused': isFocused }" class="input-box" @click="handleDivClick">
          <el-input
              ref="textareaInputRef"
              v-model="message"
              :rows="3"
              :readonly="loading"
              :placeholder="loading ? 'AI标签匹配中...' : '输入内容...'"
              type="textarea"
              @keydown="handleKeyDown"
              @focus.native="handleInputFocus"
              @blur.native="isFocused = false"/>
          <div style="display: flex; justify-content: space-between; margin-top: 5px">
            <div style="display: inline-block; width: 200px">
              <elx-tips>Shift+Enter 换行，Enter 提交</elx-tips>
            </div>
            <el-button :loading="loading" :disabled="!message" :icon="Top" plain round
                       color="#10a37f" class="send-button" @click="handleSend"/>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import {ref, reactive} from 'vue'
import {Top} from '@element-plus/icons-vue'
import {message_send_api} from '@/api/workbench/chat'

const modelType = ref('00')
const message = ref('')
const isFocused = ref(false)
const loading = ref(false)
const messageList = ref([])

const handleInputFocus = () => {
  isFocused.value = true
}

const textareaInputRef = ref()
const handleDivClick = () => {
  textareaInputRef.value.focus();
}

const chatContainerRef = ref()
const scrollBottom = () => {
  setTimeout(() => {
    chatContainerRef.value.scrollTop = chatContainerRef.value.scrollHeight
  }, 200)
}

const handleSend = () => {

  loading.value = true
  const messageText = message.value
  messageList.value.push({
    type: '0',
    text: messageText
  })
  message.value = ''
  scrollBottom()
  message_send_api('', messageText).then(res => {
    messageList.value.push({
      type: '1',
      text: res.data
    })
    scrollBottom()
  }).catch(e => {
    messageList.value.push({
      type: '1',
      text: '发生了异常'
    })
    scrollBottom()
  }).finally(() => {
    loading.value = false
  })
}
const handleKeyDown = (event) => {
  if (event.key === "Enter") {
    if (!event.shiftKey) {
      event.preventDefault();
      if (message.value && message.value.trim().length > 0) {
        handleSend()
      }
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.chat-wrapper {
  margin: -10px;
  padding: 0;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: #f7f7f8;
  color: #333;
  display: flex;
  flex-direction: column;
  height: 100%;

  .header {
    background-color: #ffffff;
    padding: 5px 20px;
    border-bottom: 1px solid #e5e5e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .logo {
    font-weight: bold;
    font-size: 18px;
    color: #333;
  }

  .main {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .message {
    max-width: 800px;
    margin: 0 auto;
    width: 100%;
    display: flex;
    gap: 15px;
  }

  .user-message {
    background-color: #ffffff;
    border-radius: 8px;
    padding: 15px;
  }

  .assistant-message {
    background-color: #f7f7f8;
    border-radius: 8px;
    padding: 15px;
  }

  .avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: #e5e5e6;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }

  .user-avatar {
    background-color: #10a37f;
    color: white;
  }

  .content {
    flex: 1;
    line-height: 1.6;
  }

  .footer {
    background-color: #ffffff;
    padding: 5px 20px;
    border-top: 1px solid #e5e5e6;
  }

  .input-container {
    max-width: 800px;
    margin: 0 auto;
    display: flex;
    gap: 10px;
  }

  .input-box {
    flex: 1;
    border: 1px solid #e5e5e6;
    border-radius: 8px;
    padding: 10px 15px 2px 15px;
    font-size: 16px;
    outline: none;
    resize: none;
    min-height: 50px;
    max-height: 200px;

    :deep(.el-textarea__inner) {
      border: none !important;
      box-shadow: none !important;
    }
  }

  .is-focused {
    border-color: #10a37f;
  }

  .send-button {
  }

  .typing-indicator {
    display: flex;
    gap: 5px;
    padding: 5px 0;
  }

  .typing-dot {
    width: 8px;
    height: 8px;
    background-color: #888;
    border-radius: 50%;
    animation: typingAnimation 1.4s infinite ease-in-out;
  }

  .typing-dot:nth-child(1) {
    animation-delay: 0s;
  }

  .typing-dot:nth-child(2) {
    animation-delay: 0.2s;
  }

  .typing-dot:nth-child(3) {
    animation-delay: 0.4s;
  }

  @keyframes typingAnimation {
    0%, 60%, 100% {
      transform: translateY(0);
    }
    30% {
      transform: translateY(-5px);
    }
  }

  .model-selector {
    padding: 3px 2px;
    border-radius: 6px;
    width: 120px;
    border: 1px solid #e5e5e6;
    background-color: white;
    font-size: 14px;

    :deep(.el-select-v2__wrapper) {
      border: 0 !important;
    }
  }
}
</style>
