<template>
  <elx-table-panel ref="tablePanel" :search-loading="loading" :data="getDataList" :pagination="getPagination" border :height="reMatching === '' ? 'calc(100vh - 260px)' : '0px'" page-header
                   :show-overflow-tooltip="false"
                   :search-function="handleSearch">
    <template #button-area-left>
      <elx-filter-button
          ref="filterButtonRef"
          :table-id="route.params.key || ''"
          :columns="columnMetas"
          :filters.sync="filterForm"
          :area-options="areaOptions"
          :disabled="reMatching !== ''"
          hidden-import-date
          @filter="handleFilter"/>
      <el-button :disabled="reMatching !== ''" type="primary" icon="View" @click="handleView">查看</el-button>
      <div style="display: inline-block; margin-left: 10px">
        <el-input v-if="needFilter" v-model="filterForm.filterText" prefix-icon="filter" readonly style="width: calc(100vw - 1000px)" @dblclick="handleFilterInput"/>
      </div>
    </template>
    <template #button-area-right>
      <el-button v-if="hasPermission(['A', 'C'])" :loading="reMatching === 'start'" type="primary" plain icon="Crop" @click="handleReMatch" class="mr-3px">重新匹配</el-button>
      <elx-history-button v-permission="['A', 'C']" :disabled="reMatching !== ''" type="export" style="margin-right: 5px" @click="handleExport" @history="handleExportHistory"/>
      <elx-table-column-selected :columns="columnMetas" :disabled="reMatching !== ''" @update-fixed="v => fixedIndex = v"/>
    </template>
    <template #extra>
      <span v-if="displayName !== ''" class="font-bold-14">视图名：</span>
      <span class="font-bold-14">{{ displayName }}</span>
    </template>

    <template v-if="!reMatching" #default>
      <el-table-column type="selection" align="center" fixed width="60px"/>
      <el-table-column prop="city" label="所属区划" fixed width="200px" label-class-name="fix-column">
        <template #default="scope">
          {{ getArea(scope.row) }}
        </template>
      </el-table-column>
      <el-table-column
          v-for="(c, index) in columnMetas.filter(e => e.hidden !== true)"
          :show-overflow-tooltip="modelType === '2' || debugColumnMetas.length === 0"
          :fixed="index < fixedIndex"
          :prop="c.id"
          :label="c.name"
          :key="index"
          :width="(c.width || '200') + 'px'"
          :label-class-name="c.match ? 'match-column' : ''"
          :class-name="c.match ? 'match-column' : ''"
      >
        <template #default="scope">
          <div v-if="!c.match" v-html="scope.row[c.id]"/>
          <div v-else>
            <div v-if="hasPermission(['A', 'C'])">
              <div v-if="scope.row.correct_content[c.id] && scope.row[c.id] !== scope.row.correct_content[c.id]">
                <div style="text-decoration: line-through; color: gray">{{scope.row[c.id]}}</div>
                <div>{{scope.row.correct_content[c.id]}}</div>
              </div>
              <div v-else>
                <div>{{scope.row[c.id]}}</div>
              </div>
            </div>
            <div v-else>
              <div v-if="scope.row.correct_content[c.id] && scope.row[c.id] !== scope.row.correct_content[c.id]">
                <div>{{scope.row.correct_content[c.id]}}</div>
              </div>
              <div v-else>{{scope.row[c.id]}}</div>
            </div>
            <div v-if="hasPermission(['A', 'C'])" style="display: flex; justify-content: space-between">
              <el-popover v-if="c.id !== 'exiting'" :visible="scope.row[c.id + 'Pv']" trigger="click" width="300px">
                <template #reference>
                  <el-link icon="Edit" @click="() => { scope.row[c.id + 'Pv'] = true }"/>
                </template>
                <template #default>
                  <div>
                    <b>正确选项：</b>
                  </div>
                  <div>
                    <el-select-v2 v-model="scope.row.tempCorrectContent[c.id]" :options="getMatchOptions(c.id)" filterable style="width: 270px"/>
                  </div>
                  <div style="text-align: right;margin-top: 10px">
                    <el-button plain size="small" @click="() => { scope.row[c.id + 'Pv'] = false }">
                      取消
                    </el-button>
                    <el-button plain type="primary" size="small" @click="handleMatch(scope.row, c.id)">
                      确定
                    </el-button>
                  </div>
                </template>
              </el-popover>
              <elx-loading-button v-if="scope.row.correct_content[c.id] && scope.row[c.id] !== scope.row.correct_content[c.id]" type="info" icon="Select" plain size="small" @click="done => handleSubmitModel(scope.row, c.id, done)">
                提交模型库
              </elx-loading-button>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
          v-if="modelType === '1'"
          v-for="(c, index) in debugColumnMetas"
          :label="c.name"
          :key="index"
          label-class-name="match-column"
          class-name="match-column"
      >
        <el-table-column
            v-for="(cc, ii) in c.columns"
            :key="ii"
            :label="cc.name"
            :width="(c.width || '300') + 'px'"
            label-class-name="match-column"
            class-name="match-column debug-match-column"
        >
          <template #default="scope">
            <div v-for="(f, iii) in getText(scope.row.debug_content, c.id, cc.id)" :key="iii">
              <div>{{ f.label }} ( {{ f.value.score }} )</div>
              <div v-for="(item, iiii) in f.value.items" :key="iiii" style="padding-left: 20px">
                * {{item.text }} ({{ item.score }})
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column
          v-if="modelType === '2'"
          v-for="(c, index) in debugColumnMetas"
          :label="c.name"
          :key="index"
          label-class-name="match-column"
          class-name="match-column"
          width="300"
      >
        <template #default="scope">
          {{ scope.row[c.id] }}
        </template>
      </el-table-column>
      <el-table-column/>
    </template>
    <template v-if="reMatching === 'start'" #extend>
      <div style="display: block; width: 400px; margin: 50px auto">
        <div class="mb-20px">
          <div><el-icon style="margin-right: 3px; vertical-align: -2px"><Download /></el-icon>导入公式:</div>
          <el-progress :percentage="modelPercentage" :stroke-width="15" :status="modelPercentage === 100 ? 'success' : ''">
          </el-progress>
        </div>
        <div class="mb-20px">
          <div><el-icon style="margin-right: 3px; vertical-align: -2px"><Connection /></el-icon>匹配数据:</div>
          <el-progress :percentage="percentage" :stroke-width="15" :status="analyticsStatus === '1' ? 'success' : ''" >
            <div v-if="analyticsStatus === '1'">已完成</div>
            <div v-else>{{percentage}}% ({{ completeCount + ' / ' + analyticsCount}})</div>
          </el-progress>
        </div>
        <div v-if="analyticsStatus === '1'">
          <div><el-icon style="margin-right: 3px; vertical-align: -2px"><Loading /></el-icon>数据加载中...</div>
        </div>
      </div>
    </template>
    <template v-if="reMatching === 'error'" #extend>
      <el-result icon="error" title="匹配错误" sub-title="匹配发生异常，请重新设置匹配模型后再进行匹配。"/>
    </template>
  </elx-table-panel>
  <DataViewDialog ref="dataViewDialog"/>
  <DataExportDialog ref="dataExportDialog" :filter="false"/>
  <ImportExportHistory ref="importExportHistoryRef"/>
</template>

<script setup>
import {
  select_display_data_api,
  export_display_data_api,
  edit_display_match_data_api,
  re_analysis_api, refresh_status_api,
  display_submit_model_api
} from '@/api/workbench/display'
import DataViewDialog from '../table/dialog/DataViewDialog.vue'
import { ref, reactive, computed } from 'vue'
import {useRouter, useRoute, onBeforeRouteLeave} from 'vue-router'
import DataExportDialog from '../component/DataExportDialog.vue';
import ImportExportHistory from '../component/ImportExportHistory.vue';
import {hasPermission} from '@/common/utils/permission';

const loading = ref(false)
const reMatching = ref('')
const router = useRouter()
const route = useRoute()

const areaOptions = ref([])
let displayName = ''
const modelType = ref('')
const columnMetas = ref([])
const debugColumnMetas = ref([])
const dataList = ref([])
const fixedIndex = ref(0)

const pagination = reactive({
  pageNumber: 1,
  pageSize: 20
})

let needFilter = false
const filterForm = reactive({
  itemFilterMode: '1',
  itemFilters: [],
  customFilter: '',
  filterText: ''
})

const dataViewDialog = ref()
const tablePanel = ref()
const handleView = () => {
  tablePanel.value.getSelectionRows(true, '查看').then(selected => {
    dataViewDialog.value.open(displayName, columnMetas, selected)
  }).catch(e => {
  })
}

const filterButtonRef = ref()
const handleFilterInput = () => {
  filterButtonRef.value.handleCommand('filter')
}

const handleSaveFilter = () => {
  const displayId = route.params.key
  filterButtonRef.value.handleSaveFilter(displayId)
}

const handleSearch = (done, init) => {
  const displayId = route.params.key
  loading.value = true
  select_display_data_api(displayId, needFilter ? filterForm : null, pagination, init).then(res => {
    if (init === true) {
      displayName = res.dataMap.displayName
      columnMetas.value = res.dataMap.meta
      modelType.value = res.dataMap.modelType
      debugColumnMetas.value = res.dataMap.debugMeta
      areaOptions.value = res.dataMap.areaOptions || []
    }
    if (res.dataMap.displayStatus === '0') {
      reMatching.value = 'start'
      setTimeout(() => {
        modelPercentage.value = 100
      }, 200)
      setTimeout(() => {
        startProcessTimer()
      }, 500)
    } else if (res.dataMap.displayStatus === '2') {
      reMatching.value = 'error'
      clearTimer()
    } else {
      dataList.value = res.data || []
      Object.assign(pagination, res.pagination)
    }
  }).catch(e => {
  }).finally(() => {
    loading.value = false
    done && done()
  })
}

const getDataList = computed(() => {
  return reMatching.value !== '' ? [{}] : dataList.value
})
const getPagination = computed(() => {
  return reMatching.value !== '' ? {} : pagination
})

const getArea = (row) => {
  if (row.district && row.town && row.road) {
    return row.district + ' / ' + row.town + ' / ' + row.road
  } else if (row.district && row.town) {
    return row.district + ' / ' + row.town
  } else if (row.district) {
    return row.district
  }
  return ''
}

const handleFilter = (done, need) => {
  needFilter = need
  handleSearch(done)
}

const getText = (text, column, field) => {
  if (!text || '{}' === text) {
    return []
  }
  const t = JSON.parse(text)
  const obj = t[column][field]
  if (!obj) {
    return []
  }
  const keys = Object.keys(obj)
  return keys.sort((k1, k2) => {
    const i1 = k1.split('.')[0]
    const i2 = k2.split('.')[0]
    return Number.parseInt(i1) - Number.parseInt(i2)
  }).map(k => {
    return {
      label: k,
      value: obj[k]
    }
  })
}

const handleMatch = (row, columnId) => {
  const displayId = route.params.key
  edit_display_match_data_api(displayId, row.dataId, columnId, row.tempCorrectContent[columnId]).then(res => {
    row.correct_content[columnId] = row.tempCorrectContent[columnId]
    row[columnId + 'Pv'] = false
  }).catch(e => {
  }).finally(() => {
  })
}

const handleSubmitModel = (row, columnId, done) => {
  const displayId = route.params.key
  display_submit_model_api(displayId, row.dataId, columnId, row.correct_content[columnId]).then(res => {
  }).catch(e => {

  }).finally(() => {
    done && done()
  })
}

const getMatchOptions = (id) => {
  return (columnMetas.value.find(e => e.id === id) || {}).matchOptions || []
}

const dataExportDialog = ref()
const importExportHistoryRef = ref()
const tags = ref([])
const handleExport = () => {
  const displayId = route.params.key
  dataExportDialog.value.open('1', displayId, displayName, columnMetas.value, tags.value, export_display_data_api, () => {
    importExportHistoryRef.value.open(displayId, 1, displayName)
  })
}

const handleExportHistory = () => {
  const displayId = route.params.key
  importExportHistoryRef.value.open(displayId, 1, displayName)
}

const modelPercentage = ref(0)

const handleReMatch = () => {

  const displayId = route.params.key
  reMatching.value = 'start'

  analyticsCount.value = 0
  completeCount.value = 0
  analyticsStatus.value = ''
  modelPercentage.value = 0
  setTimeout(() => {
    modelPercentage.value = 100
  }, 600)


  setTimeout(() => {
    re_analysis_api(displayId).then(res => {
      startProcessTimer()
    }).catch(() => {
      setTimeout(() => {
        reMatching.value = 'error'
      }, 1000)
    }).finally(() => {
    })
  }, 1000)
}

let timer = -1
const analyticsCount = ref(0)
const completeCount = ref(0)
const analyticsStatus = ref('')

const startProcessTimer = () => {
  const displayId = route.params.key
  timer = setInterval(() => {
    refresh_status_api(displayId).then(res => {
      const data = res.dataMap
      const status = data.status
      analyticsStatus.value = status
      if (status === '0') { // 智能匹配中
        analyticsCount.value = data.analyticsCount
        completeCount.value = data.completeCount
      } else if (status === '1') { // 智能匹配完成
        clearTimer()
        setTimeout(() => {
          reMatching.value = ''
          handleSearch(() => {}, false)
        }, 500)
      } else if (status === '2') { // 智能匹配失败
        reMatching.value = 'error'
        clearTimer()
      }
    }).catch(e => {
      reMatching.value = 'error'
      clearTimer()
    }).finally(() => {
    })
  }, 1000)
}

const clearTimer = () => {
  if (timer !== -1) {
    clearInterval(timer)
    timer = -1
  }
}

handleSearch(() => {}, true)

const percentage = computed(() => {
  if (analyticsStatus.value === '1') {
    return 100;
  }
  if (analyticsCount.value === 0 || completeCount.value === 0) {
    return 0;
  }
  return Number(completeCount.value / analyticsCount.value * 100).toFixed(0)
})

onBeforeRouteLeave(() => {
  clearTimer()
})
</script>
