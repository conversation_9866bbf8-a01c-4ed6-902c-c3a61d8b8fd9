<template>
  <elx-page-card v-loading="loading" save-button save-button-plain style="width: 1300px; overflow-x: auto" @save="handleSave">
    <elx-block-header title="基本信息" style="margin-top: -10px" class="w-1200px">
      <el-form ref="formRef" :model="form" :rules="validRules" :inline="true">
        <el-form-item label="展示视图名" prop="displayName">
          <el-input v-model="form.displayName" clearable class="w-300px"/>
        </el-form-item>
        <el-form-item label="源数据表" prop="tableId">
          <el-select-v2 v-model="form.tableId" :options="tableOptions" clearable class="w-310px"/>
        </el-form-item>
        <el-form-item label="描述(可选)" prop="remark">
          <el-input v-model="form.remark" class="w-720px"/>
        </el-form-item>
      </el-form>
    </elx-block-header>

    <el-card v-if="!!form.tableId" class="w-1200px">
      <elx-block-header title="显示数据列">
        <template #extra>
          <el-switch
              v-model="form.columnMode"
              :inactive-value="'1'"
              :active-value="'2'"
              inline-prompt
              inactive-text="所有列"
              active-text="自定义"
              style="--el-switch-off-color: #15a675; --el-switch-on-color: #141f29"
          />
        </template>
        <el-table v-if="form.columnMode === '2'" :data="form.columnList" scrollbar-always-on max-height="400px">
          <el-table-column label="#" type="index" width="50px"/>
          <el-table-column label="列名" width="200px">
            <template #default="scope">
              <el-select-v2 v-model="scope.row.column" :options="columnOptions"/>
            </template>
          </el-table-column>
          <el-table-column label="别名(可选)" prop="alias" width="180px">
            <template #default="scope">
              <el-input v-model="scope.row.alias"/>
            </template>
          </el-table-column>
          <el-table-column label="列显示宽度" prop="width" width="110px">
            <template #default="scope">
              <el-input v-model="scope.row.width" type="number"/>
            </template>
          </el-table-column>
          <el-table-column/>
          <el-table-column align="right" header-align="right" width="230px">
            <template  #header>
              <el-button size="small" type="primary" icon="Plus" @click="handleAddColumn"></el-button>
            </template>
            <template #default="scope">
              <el-button-group>
                <el-button :disabled="scope.$index === 0" size="small" type="" icon="Top" @click="handleMoveUp(form.columnList, scope.$index)"></el-button>
                <el-button :disabled="scope.$index === form.columnList.length - 1" size="small" type="" icon="Bottom" @click="handleMoveDown(form.columnList, scope.$index)"></el-button>
                <el-button size="small" type="" icon="CopyDocument" @click="handleCopyItem(form.columnList, scope.$index)"></el-button>
                <el-button size="small" type="" icon="Plus" @click="handleAddItem(form.columnList, scope.$index)"></el-button>
                <el-button size="small" type="danger" icon="Close" @click="handleRemove(form.columnList, scope.$index)"></el-button>
              </el-button-group>
            </template>
          </el-table-column>
        </el-table>
        <div v-else>
          <elx-tips>将显示所有列</elx-tips>
        </div>
        <span v-if="errors.columnErrors !== ''" class="el-table-item__error">{{ errors.columnErrors }}</span>
      </elx-block-header>

    </el-card>

    <el-card v-if="!!form.tableId" class="w-1200px mt-15px">
      <elx-block-header title="智能匹配列">
        <el-row class="mb-15px">
          <el-col>
            <span class="label-text">模型库类型：</span>
            <el-select-v2 v-model="form.modelType" :options="modelTypeOptions" class="w-160px ml-5px"/>
            <el-checkbox v-if="form.modelType === '2'" v-model="form.showExisting" label="显示新建存量" class="ml-20px"/>
          </el-col>
        </el-row>
        <el-table :data="form.matchColumnList" scrollbar-always-on max-height="400px">
          <el-table-column label="#" type="index" width="50px"/>
          <el-table-column label="匹配模型" width="230px">
            <template #default="scope">
              <el-select-v2 v-model="scope.row.modelId" :options="showModelOptions" clearable/>
            </template>
          </el-table-column>
          <el-table-column label="目标数据列" width="300px">
            <template #default="scope">
              <el-select-v2 v-model="scope.row.targetColumns" :options="columnOptions" multiple style="width: 270px" clearable/>
            </template>
          </el-table-column>
          <el-table-column label="别名(可选)" prop="alias" width="180px">
            <template #default="scope">
              <el-input v-model="scope.row.alias" clearable/>
            </template>
          </el-table-column>
          <el-table-column label="列显示宽度" prop="width" width="110px">
            <template #default="scope">
              <el-input v-model="scope.row.width" type="number"/>
            </template>
          </el-table-column>
          <el-table-column/>
          <el-table-column align="right" header-align="right" width="200px">
            <template  #header>
              <el-button size="small" type="primary" icon="Plus" @click="handleAddMatchField"></el-button>
            </template>
            <template #default="scope">
              <el-button-group>
                <el-button :disabled="scope.$index === 0" size="small" type="" icon="Top" @click="handleMoveUp(form.matchColumnList, scope.$index)"></el-button>
                <el-button :disabled="scope.$index === form.matchColumnList.length - 1" size="small" type="" icon="Bottom" @click="handleMoveDown(form.matchColumnList, scope.$index)"></el-button>
                <el-button size="small" type="" icon="Plus" @click="handleAddMatchItem(form.matchColumnList, scope.$index)"></el-button>
                <el-button size="small" type="danger" icon="Close" @click="handleRemove(form.matchColumnList, scope.$index)"></el-button>
              </el-button-group>
            </template>
          </el-table-column>
        </el-table>
        <span v-if="errors.matchColumnErrors !== ''" class="el-table-item__error">{{ errors.matchColumnErrors }}</span>
      </elx-block-header>

    </el-card>
  </elx-page-card>
</template>

<script setup>
import {display_init_api, display_save_api, get_table_columns_api} from '@/api/workbench/display'
import {reactive, ref, watch, nextTick, computed} from 'vue'
import {ElMessage} from 'element-plus'
import {useRouter, useRoute} from 'vue-router'
import {generateColumnId} from '@/common/utils'

const router = useRouter()
const currentRoute = useRoute()

const loading = ref(true)
const form = reactive({
  modelType: '',
  showExisting: true,
  tableId: '',
  displayName: '',
  columnMode: '1',
  columnList: [],
  matchColumnList: [],
  remark: ''
})

const tableOptions = ref([])
const columnMetasMap = {}

const columnMetas = ref([])
const columnOptions = ref([])
const modelOptions = ref([])
const modelTypeOptions = ref([])
const showModelOptions = computed(() => {
  return modelOptions.value.filter(e => e.data === form.modelType)
})

const errors = reactive({
  columnErrors: '',
  matchColumnErrors: ''
})

const resetForm = () => {
  form.columnMode = '1'
  form.columnList.length = 0
}

watch(() => form.modelType, (nv) => {
  if (form.matchColumnList) {
    form.matchColumnList.forEach(e => {
      e.modelId = ''
    })
  }
})

watch(() => form.tableId, (nv) => {
  resetForm()
  if (!form.tableId) {
    columnMetas.value = []
    columnOptions.value = []
    return
  }
  if (!columnMetasMap[nv]) {
    loading.value = true
    get_table_columns_api(nv).then(res => {
      const dataMap = res.dataMap
      columnMetasMap[nv] = JSON.parse(dataMap.columnMeta)

      columnMetas.value = columnMetasMap[nv]
      columnOptions.value = columnMetasMap[nv].map(e => {
        return {label: e.name, value: e.id}
      })
    }).catch(e => {
    }).finally(() => {
      loading.value = false
    })
  } else {
    columnMetas.value = columnMetasMap[nv]
    columnOptions.value = columnMetasMap[nv].map(e => {
      return {label: e.name, value: e.id}
    })
  }
})

const validRules = reactive({
  displayName: [
    {required: true, message: '请输入数据集名称', trigger: 'blur'}
  ],
  tableId: [
    {required: true, message: '请选择源数据表', trigger: 'blur'}
  ]
})

const handleAddColumn = () => {
  form.columnList.push({
    columnField: generateColumnId()
  })
}

const handleAddMatchField = () => {
  form.matchColumnList.push({
    columnField: generateColumnId(),
    modelId: null,
    targetColumns: [],
    width: 200
  })
}

const handleCopyItem = (list, index) => {
  const oldData = list[index]

  const data = Object.assign({}, oldData)
  data.columnField = generateColumnId()
  if (index === list.length - 1) {
    list.push(data)
  } else {
    list.splice(index + 1, 0, data)
  }
}

const handleAddItem = (list, index) => {
  const data = {
    columnField: generateColumnId()
  }
  if (index === list.length - 1) {
    list.push(data)
  } else {
    list.splice(index + 1, 0, data)
  }
}

const handleAddMatchItem = (list, index) => {
  const data = {
    columnField: generateColumnId(),
    modelId: null,
    targetColumns: [],
    width: 200
  }
  if (index === list.length - 1) {
    list.push(data)
  } else {
    list.splice(index + 1, 0, data)
  }
}

const handleRemove = (list, index) => {
  list.splice(index, 1)
}
const handleMoveUp = (list, index) => {
  if (index > 0) {
    const upData = list[index - 1]
    list.splice(index - 1, 1)
    list.splice(index, 0, upData)
  }
}
const handleMoveDown = (list, index) => {
  const downData = list[index + 1]
  list.splice(index + 1, 1)
  list.splice(index, 0, downData)
}

const checkColumnList = () => {
  errors.columnErrors = ''
  if (form.columnMode === '2' && form.columnList.length === 0) {
    errors.columnErrors = '显示数据列不能为空，请选择显示的列。'
    return false
  }

  try {
    form.columnList.forEach((c, index) => {
      if (!c.column || c.column === '') {
        errors.columnErrors = `#${index + 1} 行数据不整合，数据列名或者其它字段不完整。`
        throw new Error('LoopInterrupt')
      }
    })
  } catch (e) {
    return false
  }

  errors.matchColumnErrors = ''
  if (form.matchColumnList.length === 0) {
    errors.matchColumnErrors = '数据列不能为空，请选择显示的列。'
    return false
  }

  try {
    const modelIds = []
    form.matchColumnList.forEach((c, index) => {
      if (!c.modelId || c.modelId === '' || !c.targetColumns || c.targetColumns.length === 0) {
        errors.matchColumnErrors = `#${index + 1} 行数据不整合，匹配模型或者其它字段不完整。`
        throw new Error('LoopInterrupt')
      }
      if (modelIds.includes(c.modelId)) {
        errors.matchColumnErrors = `匹配模型 [${modelOptions.value.find(e => e.value === c.modelId).label}] 不能重复`
        throw new Error('LoopInterrupt')
      } else {
        modelIds.push(c.modelId)
      }
    })
  } catch (e) {
    return false
  }

  return true
}
const formRef = ref()

const handleSave = async(done) => {
  const valid1 = await formRef.value.validate().catch(e => {})
  if (!form.tableId) {
    errors.columnErrors = ''
    errors.matchColumnErrors = ''
  }
  let valid2 = !!form.tableId &&  checkColumnList()
  if (valid1 && valid2) {
    const optType = currentRoute.meta.optType
    const key = optType === 'edit' ? currentRoute.params.key : null
    display_save_api(optType, key, form).then(res => {
      const data = res.data
      if (data.editMeta) {
        router.replace('/workbench/display/view/' + data.displayId)
      } else {
        router.go(-1)
      }
    }).catch(() => {
    }).finally(() => {
      done()
    })
  } else {
    ElMessage.error('页面项目输入不正确，请确认后再保存。')
    done()
  }
}

const displayInit = () => {
  loading.value = true
  const optType = currentRoute.meta.optType
  const key = currentRoute.params.key
  display_init_api(optType, key).then(res => {
    const data = res.data
    tableOptions.value = data.tableOptions || []
    modelOptions.value = data.modelOptions || []
    modelTypeOptions.value = data.modelTypeOptions || []

    const display = data.display
    if (display) {
      form.tableId = display.tableId
    }
    if (optType === 'edit') {
      form.displayName = display.displayName
      form.remark = display.remark
      form.modelType = display.modelType
      form.showExisting = display.showExisting || false
      nextTick(() => {
        if (display) {
          form.columnMode = display.columnMode || '1'
          form.columnList = display.columnList || []
          form.matchColumnList = display.matchColumnList || []
        }
      })
    }
  }).catch(e => {
  }).finally(() => {
    loading.value = false
  })
}

displayInit()

</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.label-text {
  font-weight: bold;
  font-size: 12px;
  color: #324558;
}
.label-text::before {
  content: "*";
  color: var(--el-color-danger);
  margin-right: 4px;
}
</style>
