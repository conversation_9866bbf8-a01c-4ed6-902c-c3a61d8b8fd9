<template>
  <elx-table-panel :search-loading="searchLoading" :data="dataList" :pagination="pagination" :search-function="handleSearch" :has-back="false" page-header height="calc(100vh - 260px)">
    <template #extra>
      <el-button v-permission="['A', 'C']" type="primary" icon="plus" @click="handleCreate">创建展示视图</el-button>
    </template>
    <template #button-area-left>
      <div style="display: inline-block; width: 400px; margin-right: 10px">
        <el-input v-model="searchKey" placeholder="输入展示名称进行检索..." clearable class="search-button-append" @keyup.enter.native="handleSearch()">
          <template #append>
            <el-button type="info" icon="search" @click="handleSearch"/>
          </template>
        </el-input>
      </div>
    </template>
    <el-table-column type="index" label="#" width="50px"/>
    <el-table-column show-overflow-tooltip prop="displayName" label="展示视图名" width="280px"/>
    <el-table-column show-overflow-tooltip prop="modelType" label="匹配类型" width="120px">
      <template #default="scope">
        <el-tag :type="scope.row.modelType === '1' ? '' : 'warning'" effect="plain" disable-transitions>{{ scope.row.modelTypeName }}</el-tag>
      </template>
    </el-table-column>
    <el-table-column show-overflow-tooltip prop="tableName" label="原始表名" width="280px"/>
    <el-table-column show-overflow-tooltip prop="statusName" label="状态" width="230px" header-align="center" align="center">
      <template #default="scope">
        <el-tag v-if="scope.row.status === '0' && scope.row.progressRate" :type="getStatusType(scope.row.status)" disable-transitions>{{ scope.row.statusName }} 进度({{ scope.row.progressRate }})</el-tag>
        <el-tag v-else :type="getStatusType(scope.row.status)" disable-transitions>{{ scope.row.statusName }}</el-tag>
      </template>
    </el-table-column>
    <el-table-column show-overflow-tooltip prop="createUser" label="创建者" width="160px" header-align="center" align="center"/>
    <el-table-column show-overflow-tooltip prop="createTime" label="创建时间" width="200px"/>
    <el-table-column show-overflow-tooltip prop="remark" label="描述"/>
    <el-table-column width="280px" align="right">
      <template #default="scope">
        <elx-loading-button v-if="scope.row.status === '0'" size="small" icon="Refresh" type="warning" plain style="margin-left: 10px" @click="done => handleRefresh(scope.row, done)">状态刷新</elx-loading-button>
        <elx-loading-button v-if="scope.row.status === '1' || (scope.row.status === '2' && isAdmin)" type="primary" size="small" plain icon="Edit" @click="done => handleView(scope.row, done)">视图查看</elx-loading-button>
        <elx-table-command :operation-handler="operationHandler" :row="scope.row" :row-index="scope.index"/>
      </template>
    </el-table-column>
  </elx-table-panel>
</template>

<script setup>
import {search_display_api, delete_display_api, re_analysis_api, refresh_status_api} from '@/api/workbench/display'
import {ref, reactive, computed, unref} from 'vue'
import {useRouter} from 'vue-router'
import { ElMessageBox } from 'element-plus'
import {useUserStore} from '@/common/store/modules/user'

const router = useRouter()
const store = useUserStore()

const searchKey = ref('')

const dataList = ref([])
const pagination = reactive({
  pageNumber: 1,
  pageSize: 20
})
const operationHandler = [
  {
    name: '重新匹配',
    icon: 'Crop',
    permission: ['A', 'C'],
    handler: (row, index) => handleReAnalysis(row)
  },
  {
    name: '编辑',
    icon: 'Edit',
    divided: true,
    permission: ['A', 'C'],
    handler: (row, index) => handleEdit(row)
  },
  {
    name: '删除',
    icon: 'Close',
    permission: ['A', 'C'],
    handler: (row, index) => handleDelete(row)
  }
]

const searchLoading = ref(false)

const handleCreate = () => {
  router.push('/workbench/display/create')
}

const handleSearch = () => {
  searchLoading.value = true
  search_display_api(searchKey.value, pagination).then(res => {
    dataList.value = res.data || []
    Object.assign(pagination, res.pagination)
  }).catch(() => {
  }).finally(() => {
    searchLoading.value = false
  })
}
handleSearch()

const handleView = (row, done) => {
  router.push('/workbench/display/view/' + row.displayId)
}

const handleEdit = (row, done) => {
  router.push('/workbench/display/edit/' + row.displayId)
}

const handleReAnalysis = (row) => {
  re_analysis_api(row.displayId).then(res => {
    row.status = '0'
    row.statusName = '智能匹配中'
  }).catch(() => {
  }).finally(() => {
  })
}

const handleDelete = (row, done) => {
  ElMessageBox.confirm('确定删除该展示视图吗？', '确认', { type: 'warning' }).then(() => {
    searchLoading.value = true
    delete_display_api(row.displayId).then(res => {
      handleSearch()
    }).catch(() => {
    }).finally(() => {
      done && done()
      searchLoading.value = false
    })
  }).catch(e => {
    done && done()
  })
}

const getStatusType = (status) => {
  return status === '0' ? 'warning' : status === '2' ? 'danger' : 'success'
}

const handleRefresh = (row, done) => {
  refresh_status_api(row.displayId).then(res => {
    const data = res.dataMap
    row.status = data.status
    row.statusName = data.statusName
    row.progressRate = data.progressRate
  }).catch(e => {
  }).finally(() => {
    done()
  })
}

const isAdmin = computed(() => {
  return store.userType === '0' // admin
})

</script>

