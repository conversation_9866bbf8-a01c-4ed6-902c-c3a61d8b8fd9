<template>
  <div class="address-process-container">
    <el-card shadow="hover" class="main-card">
      <template #header>
        <div class="card-header">
          <span class="title">上海地址处理工具</span>
          <span class="subtitle">支持多种地址格式，自动提取弄号信息</span>
        </div>
      </template>

      <!-- 功能说明 -->
      <el-alert
        title="功能说明"
        type="info"
        :closable="false"
        class="mb-4">
        <template #default>
          <div class="description">
            <p>本工具可以处理Excel文件中的上海地址数据，自动提取弄号信息并生成标准化的地址列表。</p>
            <p><strong>Excel格式要求：</strong></p>
            <ul>
              <li>必须包含"地址"列（工具会自动识别此列）</li>
            </ul>
          </div>
        </template>
      </el-alert>

      <!-- 文件上传区域 -->
      <div class="upload-section">
        <el-upload
          ref="uploadRef"
          class="upload-demo"
          drag
          action=""
          :accept="'.xlsx,.xls'"
          :auto-upload="false"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          :limit="1"
          :file-list="fileList">
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将Excel文件拖拽到此处，或<em>点击选择文件</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              支持 .xlsx 和 .xls 格式的Excel文件
            </div>
          </template>
        </el-upload>
      </div>

      <!-- 操作按钮 -->
      <div class="action-section">
        <el-button 
          type="primary" 
          size="large"
          :loading="processing"
          :disabled="!selectedFile"
          @click="handleProcess">
          {{ processing ? '处理中...' : '开始处理' }}
        </el-button>
        
        <el-button
          v-if="taskInfo && taskStatus"
          type="info"
          size="large"
          @click="refreshStatus">
          <el-icon><refresh /></el-icon>
          刷新状态
        </el-button>

        <el-button
          v-if="canDownload"
          type="success"
          size="large"
          @click="handleDownload">
          <el-icon><download /></el-icon>
          下载结果
        </el-button>
      </div>

      <!-- 任务状态 -->
      <div v-if="taskInfo" class="result-section">
        <el-alert
          :title="taskStatus"
          :type="getAlertType(taskStatus)"
          :closable="false"
          class="mb-4">
          <template #default>
            <div class="task-info">
              <p><strong>任务信息：</strong></p>
              <ul>
                <li>任务ID：{{ taskInfo.taskId }}</li>
                <li>当前状态：{{ taskStatus }}</li>
                <li v-if="canDownload">✅ 文件已准备好，可以下载</li>
                <li v-else-if="taskStatus === '处理中'">⏳ 正在处理中，请稍候...</li>
                <li v-else-if="taskStatus === '处理失败'">❌ 处理失败，请重新上传文件</li>
              </ul>

              <!-- 进度信息 -->
              <div v-if="taskProgress && taskStatus === '处理中'" class="progress-info">
                <p><strong>处理进度：</strong></p>
                <el-progress
                  :percentage="taskProgress.progressPercentage"
                  :format="() => `${taskProgress.processedRows}/${taskProgress.totalRows}`"
                  class="progress-bar">
                </el-progress>
                <p class="current-step">{{ taskProgress.currentStep }}</p>
                <p class="refresh-tip">💡 系统会自动查询5次状态，之后请手动点击"刷新状态"按钮</p>
              </div>
            </div>
          </template>
        </el-alert>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { UploadFilled, MagicStick, Download, Refresh } from '@element-plus/icons-vue'
import { process_address_api, get_task_status_api, download_address_result_api } from '@/api/workbench/address'

// 响应式数据
const uploadRef = ref()
const fileList = ref([])
const selectedFile = ref(null)
const processing = ref(false)
const taskInfo = ref(null)
const taskStatus = ref('')
const canDownload = ref(false)
const taskProgress = ref(null)

// 文件选择处理
const handleFileChange = (file) => {
  selectedFile.value = file.raw
  taskInfo.value = null
  taskStatus.value = ''
  canDownload.value = false
  taskProgress.value = null
}

// 文件移除处理
const handleFileRemove = () => {
  selectedFile.value = null
  taskInfo.value = null
  taskStatus.value = ''
  canDownload.value = false
  taskProgress.value = null
}

// 处理地址
const handleProcess = async () => {
  if (!selectedFile.value) {
    ElMessage.warning('请先选择要处理的Excel文件')
    return
  }

  processing.value = true

  try {
    const response = await process_address_api(selectedFile.value)

    if (response.statusCode === '20000') {
      taskInfo.value = response.data
      taskStatus.value = response.data.status
      canDownload.value = false
      taskProgress.value = null

      ElMessage.success('任务已启动，正在后台异步处理...')

      // 立即开始轮询任务状态
      pollTaskStatus()
    } else {
      const errorMsg = response.messages && response.messages.length > 0
        ? response.messages[0].message
        : '启动任务失败'
      ElMessage.error(errorMsg)
    }
  } catch (error) {
    console.error('启动处理任务时发生错误:', error)
    ElMessage.error('启动失败，请检查文件格式或稍后重试')
  } finally {
    processing.value = false
  }
}

// 轮询任务状态（简化版本）
const pollTaskStatus = () => {
  if (!taskInfo.value?.taskId) return

  let pollCount = 0
  const maxPollCount = 5 // 最多轮询5次，然后提示手动刷新

  const poll = async () => {
    if (pollCount >= maxPollCount) {
      ElMessage.info('请手动点击"刷新状态"查看最新进度')
      return
    }

    try {
      const response = await get_task_status_api(taskInfo.value.taskId)

      if (response.statusCode === '20000') {
        taskStatus.value = response.data.status
        canDownload.value = response.data.canDownload

        // 更新进度信息
        if (response.data.totalRows) {
          taskProgress.value = {
            totalRows: response.data.totalRows,
            processedRows: response.data.processedRows,
            currentStep: response.data.currentStep,
            progressPercentage: response.data.progressPercentage
          }
        }

        if (response.data.status === '已完成') {
          ElMessage.success('地址处理完成！')
          return // 停止轮询
        } else if (response.data.status === '处理失败') {
          ElMessage.error('处理失败，请重试')
          return // 停止轮询
        }

        // 继续轮询，但次数有限
        pollCount++
        if (pollCount < maxPollCount) {
          setTimeout(poll, 10000) // 10秒后再次查询
        } else {
          ElMessage.info('已自动查询5次，请手动点击"刷新状态"查看最新进度')
        }
      }
    } catch (error) {
      console.error('查询任务状态时发生错误:', error)
      pollCount++
      if (pollCount < maxPollCount) {
        setTimeout(poll, 15000) // 出错后15秒重试
      }
    }
  }

  // 立即执行第一次查询
  poll()
}

// 手动刷新状态
const refreshStatus = async () => {
  if (!taskInfo.value?.taskId) return

  try {
    const response = await get_task_status_api(taskInfo.value.taskId)

    if (response.statusCode === '20000') {
      taskStatus.value = response.data.status
      canDownload.value = response.data.canDownload

      // 更新进度信息
      if (response.data.totalRows) {
        taskProgress.value = {
          totalRows: response.data.totalRows,
          processedRows: response.data.processedRows,
          currentStep: response.data.currentStep,
          progressPercentage: response.data.progressPercentage
        }
      }

      ElMessage.success('状态已刷新')
    }
  } catch (error) {
    console.error('刷新状态时发生错误:', error)
    ElMessage.error('刷新失败')
  }
}

// 下载结果
const handleDownload = async () => {
  if (!taskInfo.value?.taskId || !canDownload.value) {
    ElMessage.warning('文件还未准备好，请等待处理完成')
    return
  }

  try {
    const response = await download_address_result_api(taskInfo.value.taskId)

    // response已经是一个Blob对象，直接使用
    const url = window.URL.createObjectURL(response)
    const link = document.createElement('a')
    link.href = url
    link.download = 'processed_addresses.xlsx'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('文件下载成功')
  } catch (error) {
    console.error('下载文件时发生错误:', error)
    ElMessage.error('下载失败，请稍后重试')
  }
}

// 获取Alert类型
const getAlertType = (status) => {
  switch (status) {
    case '已完成':
      return 'success'
    case '处理失败':
      return 'error'
    case '处理中':
      return 'info'
    default:
      return 'info'
  }
}
</script>

<style scoped>
.address-process-container {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.main-card {
  border-radius: 8px;
}

.card-header {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.title {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
}

.subtitle {
  font-size: 14px;
  color: #909399;
}

.description {
  line-height: 1.6;
}

.description ul {
  margin: 10px 0;
  padding-left: 20px;
}

.description li {
  margin: 5px 0;
}

.upload-section {
  margin: 30px 0;
}

.upload-demo {
  width: 100%;
}

.action-section {
  text-align: center;
  margin: 30px 0;
  display: flex;
  justify-content: center;
  gap: 20px;
}

.result-section {
  margin: 30px 0;
}

.result-info {
  line-height: 1.6;
}

.result-info ul {
  margin: 10px 0;
  padding-left: 20px;
}

.result-info li {
  margin: 5px 0;
}

.example-section {
  margin-top: 40px;
}

.example-content {
  padding: 20px;
}

.example-content h4 {
  margin: 15px 0 10px 0;
  color: #303133;
}

.example-tag {
  margin: 5px 10px 5px 0;
  padding: 8px 12px;
  font-family: monospace;
}

.example-output {
  margin-top: 10px;
}

.result-tag {
  margin: 5px 10px 5px 0;
  padding: 6px 10px;
}

.mb-4 {
  margin-bottom: 20px;
}

.original-row td {
  background-color: #f8f9fa !important;
  font-weight: bold;
}

.processed-row td {
  background-color: #e8f5e8 !important;
}

.result-format-example {
  margin: 15px 0;
  overflow-x: auto;
}

.progress-info {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.progress-bar {
  margin: 10px 0;
}

.current-step {
  margin: 10px 0;
  color: #606266;
  font-size: 14px;
}

.refresh-tip {
  margin: 10px 0;
  color: #909399;
  font-size: 12px;
  font-style: italic;
}
</style>
