<template>
  <elx-page-card v-loading="loading" :save-button="true" @save="handleSave">
      <el-form ref="formRef" :model="form" :rules="validRules" label-width="140px">
        <elx-block-header title="基本信息">
          <el-form-item prop="roleName" label="角色名称">
            <el-input v-model="form.roleName" class="w-160px" clearable/>
          </el-form-item>
          <el-form-item prop="roleType" label="角色类型">
            <el-radio-group v-model="form.roleType">
              <el-radio label="1">个人</el-radio>
              <el-radio label="2">组织</el-radio>
            </el-radio-group>
          </el-form-item>
        </elx-block-header>
        <elx-block-header title="权限列表">
          <el-form-item v-if="form.authList.length > 0" label="">
            <el-row v-for="(e, index) in form.authList" :gutter="1">
              <el-card
                  :key="index"
                  class="box-card"
                  style="margin-bottom: 10px; width: 800px; padding-bottom: 20px">
                <template #header>
                  <div class="clearfix" style="line-height: 20px">
                    <span><b>{{ e.authName }}</b></span>
                    <el-switch
                        v-model="e.roleAuthValue"
                        style="float: right;"
                        active-value="1"
                        inactive-value="0"
                        @change="changeSwitch(e.roleAuthValue, e.children, 'masterSwitch')"/>
                  </div>
                </template>
                <div v-for="(a, _index) in e.children" :key="_index">
                  <el-form-item label-width="10px" style="width: 33%;float: left">
                    <el-switch
                        v-model="a.roleAuthValue"
                        :active-text="a.authName"
                        :active-color="color(a.authId) ? '#ff4949' : ''"
                        active-value="1"
                        inactive-value="0"
                        @change="changeSwitch(e, e.children, '')"/>
                  </el-form-item>
                </div>
              </el-card>
            </el-row>
          </el-form-item>
        </elx-block-header>
      </el-form>
  </elx-page-card>
</template>

<script setup>
import {ref, reactive} from 'vue'
import {useRouter, useRoute} from 'vue-router'
import { getRole, updateRole, addRole } from '@/api/system/role'
const router = useRouter()
const currentRoute = useRoute()

const loading = ref(false)

const dataList = ref([])
const form = reactive({
  roleId: '',
  roleName: '',
  roleType: '',
  showSequence: '',
  authList: []
})
const validRules = {
  roleName: [
    { required: true, message: '必须输入角色名称', trigger: ['blur'] }
  ],
  roleType: [
    { required: true, message: '必须选择角色类型', trigger: ['blur'] }
  ]
}

const color = (authId) => {
  return authId.endsWith('_W') || authId.endsWith('_VFY')
}

const changeSwitch = (masterRoleAuth, children, masterSwitch) => {
  if (masterSwitch === 'masterSwitch') {
    children.forEach(e => {
      if (masterRoleAuth === '1') {
        e.roleAuthValue = '1'
      } else {
        e.roleAuthValue = '0'
      }
    })
  } else {
    if (children.filter(auth => auth.roleAuthValue === '1').length === 0) {
      masterRoleAuth.roleAuthValue = '0'
    } else {
      masterRoleAuth.roleAuthValue = '1'
    }
  }
}

const handleInit = () => {
  const optType = currentRoute.meta.optType
  const roleId = currentRoute.params.key || ''
  loading.value = true
  getRole(optType, roleId).then(resp => {
    // form = resp.value.data
    console.log(resp)
    const data = resp.data
    form.roleName = data.roleName
    form.roleType = data.roleType
    form.showSequence = data.showSequence
    form.authList = data.authList || []
    if (oprType === 'add') {
      form.roleName = ''
    }
  }).catch(() => {
  }).finally(() => {
    loading.value = false
  })
}

const formRef = ref()

const handleSave = (done) => {

  const optType = currentRoute.meta.optType
  formRef.value.validate((valid) => {
    if (valid) {
      loading.value = true
      if (optType === 'add') {
        addRole(form).then(rsp => {
        }).catch(() => {
        }).finally(() => {
          loading.value = false
          done && done()
        })
      } else {
        form.roleId = currentRoute.params.key || ''
        updateRole(form).then(rsp => {
        }).catch(() => {
        }).finally(() => {
          loading.value = false
          done && done()
        })
      }
    } else {
      done && done()
    }
  })
}

handleInit()

</script>
