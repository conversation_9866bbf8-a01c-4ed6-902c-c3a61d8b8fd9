<template>
  <elx-table-panel ref="tablePanel" :search-loading="loading" :data="dataList" :pagination="pagination"
                   :search-function="handleSearch">
    <template #button-area-left>
      <el-input v-model="searchInput" placeholder="输入角色名称进行搜索 ..." class="search-button-append w-330px mr-2">
        <template #append>
          <elx-loading-button icon="Search" @click="handleSearch"/>
        </template>
      </el-input>
      <el-button v-permission="['ROL_CRE']" type="primary" icon="plus" @click="handleCreate">创建角色</el-button>
    </template>

    <el-table-column type="expand" width="50px">
      <template #default="scope">
        <el-form label-position="right" class="el-form__table-inner" style="margin-left: 100px;" label-width="100px">
          <el-form-item label="角色名称：">
            <div class="table_expand">{{ scope.row.roleName }}</div>
          </el-form-item>
          <el-form-item label="角色类型：">
            <div v-if="scope.row.roleType === '1'" class="table_expand">个人</div>
            <div v-if="scope.row.roleType === '2'" class="table_expand">组织</div>
          </el-form-item>
          <el-form-item label="权限：">
            <div v-for="(e, index) in scope.row.authList" :key="index">
              <span v-if="e.roleAuthValue === '1'" class="table_expand"><el-tag>{{ e.authName }}：</el-tag> {{ e.childrenAuthName }}</span>
            </div>
          </el-form-item>
        </el-form>
      </template>
    </el-table-column>
    <el-table-column type="index" align="center" label="#" width="60px" />
    <el-table-column prop="roleName" label="角色名称" width="260px" sortable="custom"/>
    <el-table-column :formatter="formatRoleType" prop="roleType" label="角色类型" sortable="custom" />
    <el-table-column label="操作" label-align="center" width="160px">
      <template #default="scope">
        <el-link :underline="false" type="primary" icon="el-icon-view" @click="handleShowRole(scope.$index, scope.row)">查看</el-link>
        <el-link v-permission="['ROL_EDT']" :underline="false" type="primary" icon="el-icon-edit" @click="handleEditRole(scope.$index, scope.row)">编辑</el-link>
        <el-link v-permission="['ROL_EDT']" :underline="false" type="danger" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-link>
      </template>
    </el-table-column>
  </elx-table-panel>
</template>

<script setup>
import { searchByPaging, deleteRole } from '@/api/system/role'
import {ref, reactive} from 'vue'
import {useRouter} from 'vue-router'

const loading = ref(false)
const searchInput = ref('')

const dataList = ref([])
const pagination = reactive({
  pageNumber: 1,
  pageSize: 20
})

const router = useRouter()

const handleSearch = (done, init) => {
  loading.value = true
  searchByPaging(searchInput.value, init, pagination).then(res => {
    dataList.value = res.data || []
    Object.assign(pagination, res.pagination)
  }).catch(() => {
    dataList.value = []
  }).finally(() => {
    loading.value = false
    done && done()
  })
}

handleSearch(() => {}, true)

const handleCreate = () => {
  router.push('/system/role-create')
}

const handleShowRole = (index, row) => {
  router.push('/system/role-show/' + row.roleId)
}

const handleEditRole = (index, row) => {
  router.push('/system/role-edit/' + row.roleId)
}

const handleDelete = (row) => {
  // TODO
}

const formatRoleType = (row, column) => {
  return row.roleType === '1' ? '个人' : row.roleType === '2' ? '组织' : ''
}

</script>
