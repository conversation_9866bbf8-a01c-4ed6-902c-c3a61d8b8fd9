<template>
  <elx-page-card v-loading="loading" :save-button="false">
      <el-form ref="formRef" :model="form" label-width="140px">
        <elx-block-header title="基本信息">
          <el-form-item prop="roleName" label="角色名称">
            <el-input v-model="form.roleName" class="w-160px" readonly/>
          </el-form-item>
          <el-form-item prop="roleType" label="角色类型">
            <el-radio-group v-model="form.roleType" disabled>
              <el-radio label="1">个人</el-radio>
              <el-radio label="2">组织</el-radio>
            </el-radio-group>
          </el-form-item>
        </elx-block-header>
        <elx-block-header title="权限列表">
          <el-form-item v-if="form.authList.length > 0" label="">
            <el-row v-for="(e, index) in form.authList" :gutter="1">
              <el-card :key="index" class="box-card" style="margin-bottom: 10px; width: 800px">
                <div slot="header" class="clearfix" style="line-height: 20px">
                  <span><b>{{ e.authName }}</b></span>
                  <el-switch v-model="e.roleAuthValue" style="float: right;" active-value="1" inactive-value="0" disabled/>
                </div>
                <div v-for="(a, _index) in e.children" :key="_index">
                  <el-form-item label-width="10px" style="width: 33%;float: left">
                    <el-switch v-model="a.roleAuthValue" :active-text="a.authName" active-value="1" inactive-value="0" disabled/>
                  </el-form-item>
                </div>
              </el-card>
            </el-row>
          </el-form-item>
        </elx-block-header>
      </el-form>
  </elx-page-card>
</template>

<script setup>
import {ref, reactive} from 'vue'
import {useRouter, useRoute} from 'vue-router'
import { getRole } from '@/api/system/role'
const router = useRouter()
const currentRoute = useRoute()

const loading = ref(false)

const dataList = ref([])
const form = reactive({
  roleId: '',
  roleName: '',
  roleType: '',
  authList: []
})


const handleInit = () => {
  const optType = 'edit'
  const roleId = currentRoute.params.key || ''
  loading.value = true
  getRole(optType, roleId).then(resp => {
    // form = resp.value.data
    console.log(resp)
    const data = resp.data
    form.roleName = data.roleName
    form.roleType = data.roleType
    form.showSequence = data.showSequence
    form.authList = data.authList || []
    if (oprType === 'add') {
      form.roleName = ''
    }
  }).catch(() => {
  }).finally(() => {
    loading.value = false
  })
}

handleInit()

</script>
