<template>
  <elx-table-panel ref="tablePanel" :search-loading="loading" :data="dataList" :pagination="pagination"
                   :search-function="handleSearch" :has-back="false" page-header height="calc(100vh - 260px)">
    <template #extra>
      <el-button v-permission="['A', 'C', 'D']" type="primary" icon="plus" @click="handleCreate">创建用户</el-button>
    </template>
    <template #button-area-left>
      <el-input v-model="searchInput" placeholder="输入用户ID或用户名进行搜索 ..." class="search-button-append w-400px mr-2">
        <template #append>
          <elx-loading-button icon="Search" @click="done => handleSearch(done)"/>
        </template>
      </el-input>
    </template>

    <el-table-column aria-disabled="true" type="index" label="#" width="50px"/>
    <!-- <el-table-column type="expand">
      <template #default="scope">
        <el-form label-position="right" min-width="600px" style="margin-left:54px;font-weight:300;font-size:30px" label-width="60px">
          <el-form-item label="用户ID">
            <div>{{ scope.row.userId }}</div>
          </el-form-item>
          <el-form-item label="用户名">
            <div>{{ scope.row.userName }}</div>
          </el-form-item>
          <el-form-item label=" 邮 箱">
            <div>{{ scope.row.userEmail }}</div>
          </el-form-item>
          <el-form-item label=" 角 色">
            <el-tag type="" effect="plain" disable-transitions>
              {{ scope.row.userType }}
            </el-tag>
          </el-form-item>
        </el-form>
      </template>
    </el-table-column>-->
    <el-table-column show-overflow-tooltip prop="userId" label="用户ID" width="150px"/>
    <el-table-column show-overflow-tooltip prop="userName" label="用户名" width="200px"/>
    <el-table-column show-overflow-tooltip prop="userEmail" label="邮箱" width="200px"/>
    <el-table-column show-overflow-tooltip prop="userType" label="角色" header-align="center" align="center" width="150px">
      <template #default="scope">
        <el-tag type="" effect="plain" disable-transitions>
          {{ scope.row.userType }}
        </el-tag>
      </template>
    </el-table-column>
    <el-table-column show-overflow-tooltip prop="city" label="市" width="130px"/>
    <el-table-column show-overflow-tooltip prop="district" label="区" width="200px"/>
    <el-table-column show-overflow-tooltip prop="street" label="镇/街道"/>
    <el-table-column label="操作" label-align="center" align="left" width="120px">
      <template #default="scope">
        <el-link v-permission="['A', 'C', 'D']" :underline="false" type="primary" icon="el-icon-edit" @click="handleEdit(scope.row)">编辑</el-link>
        <el-link v-permission="['A', 'C', 'D']" v-if="loginUserId !== scope.row.userId" :underline="false" type="danger" icon="el-icon-delete" @click="handleDelete(scope.$index, scope.row)">删除</el-link>
      </template>
    </el-table-column>
  </elx-table-panel>
</template>

<script setup>
import { searchUserByPaging, deleteUsers_api, deleteUser_api } from '@/api/system/user'
import { useUserStore } from '@/common/store/modules/user'
import {ref, reactive} from 'vue'
import {useRouter} from 'vue-router'

const loading = ref(false)
const searchInput = ref('')
const searchRoles = ref([])

const loginUserId = ref('')

const dataList = ref([])
const userTypeOptions = ref([])

const pagination = reactive({
  pageNumber: 1,
  pageSize: 20
})

const router = useRouter()
const userStore = useUserStore()

const handleSearch = (done, init) => {
  loading.value = true
  loginUserId.value = userStore.userCode
  searchUserByPaging(searchInput.value, searchRoles.value, pagination, init).then(res => {
    if (init) {
      userTypeOptions.value = res.dataMap.userTypeOptions || []
    }
    dataList.value = res.data || []
    Object.assign(pagination, res.pagination)
  }).catch((e) => {
  }).finally(() => {
    loading.value = false
    done && done()
  })
}

handleSearch(() => {}, true)

const handleCreate = () => {
  router.push('/system/user/create')
}

const handleShowRole = (index, row) => {
  router.push('/system/user/show/' + row.userId)
}

const handleEdit = (row) => {
  router.push('/system/user/edit/' + row.userId)
}

const handleDelete = (row) => {

}
</script>
