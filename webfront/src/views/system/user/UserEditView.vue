<template>
  <elx-page-card v-loading="loading" :save-button="true" style="width: 1300px; overflow-x: auto" @save="handleSave">
    <el-form ref="formRef" :model="form" :rules="validRules" label-width="140px">
      <el-form-item prop="userId" label="账号" >
        <el-input :disabled="optType !== 'add'" v-model="form.userId" class="w-280px" clearable maxlength="20" show-word-limit @blur="form.userId = form.userId.trim()"/>
      </el-form-item>
      <el-form-item prop="userName" label="姓名">
        <el-input v-model="form.userName" class="w-280px" clearable maxlength="15"/>
      </el-form-item>
      <el-form-item v-if="optType === 'edit'" label="更新密码">
        <el-switch v-model="form.updatePass"/>
      </el-form-item>
      <el-form-item v-if="optType === 'add' || (optType === 'edit' && form.updatePass)" label="密码" prop="password">
        <el-input v-model="form.password" maxlength="30" show-password class="w-280px" auto-complete="off" clearable @blur="form.password = form.password.trim()"/>
      </el-form-item>
      <el-form-item v-if="optType === 'add' || (optType === 'edit' && form.updatePass)">
        <elx-pwd-complexity :password="form.password" class="w-280px"/>
      </el-form-item>
      <div v-if="disableRole">
        <el-form-item prop="userType" label="角色">
          <el-select v-model="form.userType" placeholder="请选择" class="w-280px" clearable>
            <el-option v-for="item in roleOptions" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-form-item v-if="form.userType === '2'" prop="district" label="区" class="is-required">
          <el-select-v2 v-model="form.district" :options="districtOptions" placeholder="请选择" clearable/>
        </el-form-item>
        <el-form-item v-if="form.userType === '3'" prop="town" label="街道" class="is-required">
          <el-cascader v-model="form.town" :options="townOptions" placeholder="请选择" clearable/>
        </el-form-item>
      </div>
      <el-form-item prop="userEmail" label="用户邮箱">
        <el-input v-model="form.userEmail" class="w-280px" clearable/>
      </el-form-item>
    </el-form>
  </elx-page-card>
</template>

<script setup>
import {ref, reactive, computed} from 'vue'
import {useRouter, useRoute} from 'vue-router'
import { addUser_api, getUser_api, updateUser_api } from '@/api/system/user'
import {useUserStore} from '@/common/store/modules/user';
const router = useRouter()
const currentRoute = useRoute()

const loading = ref(false)
const optType = currentRoute.meta.optType
const roleOptions = ref([])
const districtOptions = ref([])
const townOptions = ref([])
const store = useUserStore()

const dataList = ref([])
const form = reactive({
  userId: '',
  userEmail: '',
  userName: '',
  password: '',
  updatePass: false,
  userType: '',
  district: '',
  town: []
})
const validRules = {
  userId: [
    {
      required: true, validator: (rule, value, callback) => {
        if (value === null) {
          callback(new Error('必须输入账号'))
        } else if (!(/^[A-Za-z][A-Za-z0-9\-_]{3,20}$/g.test(value))) {
          callback(new Error('账号不合法(字母开头，只能包含字母,数字,横杠，下划线，长度为4-20个字符)'))
        } else {
          callback()
        }
      }, trigger: ['change']
    },
    {max: 20, message: '长度不能超过20字符', trigger: ['blur']}
  ],
  userEmail: [
    { max: 128, message: '长度不能超过128个字符', trigger: ['blur'] }
  ],
  userName: [
    { max: 15, message: '长度不能超过15个文字', trigger: ['blur'] },
    { required: true, message: '必须输入姓名', trigger: ['blur'] }
  ],
  password: [
    { required: true, validator: (rule, value, callback) => {
        if ((optType === 'add' || form.updatePass) && value === null) {
          callback(new Error('必须输入密码'))
        } else {
          callback()
        }
      }, trigger: 'blur' },
    { min: 5, max: 30, message: '密码长度在5-30个字符范围内', trigger: ['blur'] }
  ],
  userType: [
    { required: true, message: '必须选择角色', trigger: ['blur'] }
  ],
  district: [
    {
      validator: (rule, value, callback) => {
        if (form.userType === '2') {
          if (value === null || value === '') {
            callback(new Error('必须选择区'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      }, trigger: ['change']
    }
  ],
  town: [
    {
      validator: (rule, value, callback) => {
        if (form.userType === '3') {
          if (value === null || value === '') {
            callback(new Error('必须选择街道'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      }, trigger: ['change']
    }
  ]
}

const disableRole = computed(() => {
  if (optType === 'add') {
    return true
  }
  if (form.userId === store.userCode) {
    return false
  }
  if (store.userType < '2') {
    return true
  }
  if (!form.userType || form.userType > '2') {
    return true
  }
  return false
})

const handleInit = () => {
  const optType = currentRoute.meta.optType
  const userId = currentRoute.params.key || ''
  loading.value = true
  getUser_api(optType, userId).then(resp => {
    const data = resp.data
    roleOptions.value = resp.dataMap.roleOptions || []
    districtOptions.value = resp.dataMap.districtOptions || []
    townOptions.value = resp.dataMap.townOptions || []

    if (optType === 'edit') {
      form.userId = data.userId
      form.userName = data.userName
      form.userEmail = data.userEmail
      form.userType = data.userType
      form.district = data.district
      form.town = data.town || []
    }
  }).catch(() => {
  }).finally(() => {
    loading.value = false
  })
}

const formRef = ref()

const handleSave = (done) => {

  const optType = currentRoute.meta.optType
  formRef.value.validate((valid) => {
    if (valid) {
      loading.value = true
      if (optType === 'add') {
        addUser_api(form).then(res => {
          router.push('/system/user/list')
        }).catch(() => {}).finally(() => {
          loading.value = false
          done && done()
        })
      } else {
        form.userId = currentRoute.params.key || ''
        updateUser_api(form).then(res => {
          router.push('/system/user/list')
        }).catch(() => {}).finally(() => {
          loading.value = false
          done && done()
        })
      }
    } else {
      done && done()
    }
  })
}

handleInit()

</script>
