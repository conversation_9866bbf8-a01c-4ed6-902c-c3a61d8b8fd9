<template>
  <div class="login-container">
    <div class="p-login">
      <h1 class="p-login__logo">
        欢迎管理员登录
      </h1>
      <div class="c-card p-card-login">
        <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" size="large">
          <div class="p-form-group">
            <label class="p-label-block">管理员账户</label>
            <el-form-item prop="username">
              <el-input
                  id="userid"
                  v-model="loginForm.username"
                  :clearable="false"
                  prefix-icon="User"
                  class="u-w280"
                  size="large"
                  @change="loginForm.username = loginForm.username.trim()"/>
            </el-form-item>
          </div>
          <div class="p-form-group u-mt10" style="margin-top: 20px">
            <label class="p-label-block">密码</label>
            <el-form-item prop="password">
              <el-input
                  id="password"
                  v-model="loginForm.password"
                  :clearable="false"
                  show-password
                  prefix-icon="Lock"
                  size="large"
                  class="u-w280"
                  type="password"
                  name="password"
                  @change="loginForm.password = loginForm.password.trim()"/>
            </el-form-item>
          </div>
          <div class="p-btn-login-div">
            <el-button :loading="loading" type="primary" class="p-btn-login u-w280" size="large" @click="handleLogin">登录</el-button>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {defineComponent, onBeforeMount, reactive, toRefs, ref, watch} from 'vue'
import {isEmpty} from '@/common/utils/validate'
import {useUserStore} from '@/common/store/modules/user'

import {useRouter} from 'vue-router'

export default defineComponent({
  setup: function () {
    const validateUsername = (rule: any, value: any, callback: any) => {
      if (isEmpty(value)) {
        callback(new Error('请输入管理员账户'))
      } else {
        callback()
      }
    }
    const validatePass = (rule: any, value: any, callback: any) => {
      if (isEmpty(value)) {
        callback(new Error('请输入密码'))
      } else {
        callback()
      }
    }

    const loginForm = reactive({
      username: '',
      password: ''
    })

    // const router = useRouter()
    const pwdType = ref('password')
    const showPwd = () => {
      if (pwdType.value === 'password') {
        pwdType.value = ''
      } else {
        pwdType.value = 'password'
      }
    }

    const loginFormRef = ref();
    const loading = ref(false)

    const router = useRouter()

    const handleLogin = () => {
      loginFormRef.value.validate((valid: any) => {
        if (valid) {
          loading.value = true

          useUserStore().Login(loginForm.username, loginForm.password).then((res: any) => {
            loading.value = false
            router.push({path: '/'})
          }).catch((error) => {
            loading.value = false
          })
        } else {
          return false
        }
      })
    }

    const loginRules = ref({
      username: [{required: true, trigger: 'blur', validator: validateUsername}],
      password: [{required: true, trigger: 'blur', validator: validatePass}]
    })

    return {
      loginFormRef,
      loginForm,
      loading,
      pwdType,
      loginRules,
      showPwd,
      handleLogin
    }
  }
})
</script>

<style rel="stylesheet/scss" lang="scss" scoped>

.login-container {
  display: flex;
  justify-content: center;
  min-height: 100vh;

  height: 100%;
  width: 100%;
  overflow-y: hidden;
  //background: url(../../assets/image/bg_login.png) repeat center top/cover;
  background: #e4ebf1;
  background-size: cover;

  .p-login {
    margin-top: 136px;
  }

  .p-login__logo {
    margin-bottom: 28px;
    text-align: center;
  }

  .c-card {
    border-radius: 5px;
    background: #fff;
  }

  .p-card-login {
    padding: 35px 40px 45px;
  }

  .p-label-block {
    display: block;
    margin-bottom: 2px;
    font-size: 12px;
    font-weight: bold;
  }

  .u-w280 {
    width: 280px !important;
  }

  .p-btn-login-div {
    height: 80px;
    line-height: 80px;
    text-align: center;
  }

  .p-btn-login {
    width: 160px;
    padding-top: 11px;
    padding-bottom: 11px;
  }

  .p-btn-login {
  }

  .p-btn-login:disabled {
    border-color: #bbbbbb;
    background-color: #bbbbbb;
    color: #fff;
    pointer-events: none;
  }

}
</style>
