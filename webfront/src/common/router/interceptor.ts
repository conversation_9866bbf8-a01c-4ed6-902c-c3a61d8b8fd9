import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import router from '@/common/router'
import type {RouteLocationNormalized} from 'vue-router'
import {useUserStore} from '@/common/store/modules/user'
import { getToken } from '@/common/utils/cookies'
import {ElMessage} from 'element-plus'

NProgress.configure({showSpinner: false})


const whiteList = ['/password-change']

router.beforeEach((to, _from, next) => {
  NProgress.start()
  const store = useUserStore()
  // setTimeout(() => ElMessage.closeAll(), 200)

  const token = getToken()
  if (token) {
    if (whiteList.indexOf(to.path) !== -1) {
      next()
    } else {
      // @ts-ignore
      if (to.path.startsWith('/workbench') && !store.currentProject.projectId) {
        ElMessage({ type: 'warning', message: '请先选择项目' })
        next('/project/list')
      } else {
        const toLogin = to.path === '/login'
        const toTop = to.path === '/index'
        store.HealthCheck(toLogin || toTop).then(res => {
          if (toLogin) {
            next({ path: '/' })
          } else {
            next()
          }
        }).catch((error) => {
          console.log('error', error)
          if (toLogin) {
            next()
          } else {
            next('/login')
          }
        })
      }
    }
  } else {
    if (whiteList.indexOf(to.path) !== -1 || to.path === '/login') {
      next()
    } else {
      next('/login')
    }
  }
})

router.afterEach((to: RouteLocationNormalized) => {
  // console.log(to)
  // Finish progress bar
  // hack: https://github.com/PanJiaChen/vue-element-admin/pull/2939
  NProgress.done()

  // set page title
  const title = to.meta.title
  if (title) {
    document.title = 'Data-Analytics - ' + title
  } else {
    document.title = 'Data-Analytics'
  }
})
