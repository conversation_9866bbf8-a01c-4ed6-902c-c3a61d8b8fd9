import { createRouter, createWebHashHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import LoginView from '@/views/login/LoginView.vue'
import Layout from '@/common/layout/Layout.vue'
import LayoutNoMenu from '@/common/layout/LayoutNoMenu.vue'

export const constantRouterMap: Array<RouteRecordRaw> = [
  {
    path: '/login',
    name: 'login',
    component: LoginView,
    meta: { hidden: true }
  },
  {
    path: '/',
    component: LayoutNoMenu,
    redirect: '/system',
    children: [],
    meta: { hidden: true }
  },
  {
    name: 'system',
    path: '/system',
    component: Layout,
    redirect: '/system/user',
    children: [
      {
        name: 'system_user',
        path: '/system/user',
        redirect: '/system/user/list',
        meta: { title: '人员管理', icon: 'user', permission: ['A', 'C', 'D'] },
        children: [
          {
            path: '/system/user/list',
            component: () => import('@/views/system/user/UserMainView.vue'),
            meta: { title: '人员管理', permission: ['A', 'C', 'D'] },
          },
          {
            name: 'user_create',
            path: '/system/user/create',
            component: () => import('@/views/system/user/UserEditView.vue'),
            meta: { title: '创建用户', optType: 'add', permission: ['A', 'C', 'D'], parentName: 'system_user' }
          },
          {
            name: 'user_edit',
            path: '/system/user/edit/:key',
            component: () => import('@/views/system/user/UserEditView.vue'),
            meta: { title: '修改用户', optType: 'edit', permission: ['A', 'C', 'D'], parentName: 'system_user' }
          },
          {
            path: '/system/user/show/:key',
            component: () => import('@/views/system/user/UserEditView.vue'),
            meta: { title: '查看用户' }
          }
        ]
      }
    ]
  },
  {
    name: 'workbench',
    path: '/workbench',
    component: Layout,
    redirect: '/workbench/table',
    children: [
      {
        name: 'workbench_table',
        path: '/workbench/table',
        redirect: '/workbench/table/index',
        meta: { title: '数据表', icon: 'data' },
        children: [
          {
            path: '/workbench/table/index',
            component: () => import('@/views/workbench/table/TableMainView.vue'),
            meta: { title: '数据表' }
          },
          {
            name: 'table_create',
            path: '/workbench/table/create',
            component: () => import('@/views/workbench/table/TableCreateView.vue'),
            meta: { title: '创建数据表', optType: 'add', permission: ['A', 'C'], parentName: 'workbench_table' }
          },
          {
            name: 'table_edit',
            path: '/workbench/table/edit/:key',
            component: () => import('@/views/workbench/table/TableCreateView.vue'),
            meta: { title: '修改数据表', optType: 'edit', permission: ['A', 'C'], parentName: 'workbench_table' }
          },
          {
            path: '/workbench/table/data/:key',
            component: () => import('@/views/workbench/table/TableDataView.vue'),
            meta: { title: '数据表 - 数据操作', parentName: 'workbench_table', icon: 'data' }
          }
        ]
      },
      {
        name: 'workbench_model',
        path: '/workbench/model',
        redirect: '/workbench/model/index',
        meta: { title: '匹配模型', icon: 'model', permission: ['A', 'C'] },
        children: [
          {
            path: '/workbench/model/index',
            component: () => import('@/views/workbench/model/ModelMainView.vue'),
            meta: { title: '匹配模型', permission: ['A', 'C'] }
          },
          {
            name: 'model_create',
            path: '/workbench/model/create',
            component: () => import('@/views/workbench/model/ModelCreateView.vue'),
            meta: { title: '创建匹配模型', optType: 'add', permission: ['A'], parentName: 'workbench_model' }
          },
          {
            name: 'model_edit',
            path: '/workbench/model/edit/:key',
            component: () => import('@/views/workbench/model/ModelCreateView.vue'),
            meta: { title: '修改匹配模型', optType: 'edit', permission: ['A'], parentName: 'workbench_model' }
          },
          {
            name: 'model_view',
            path: '/workbench/model/view/:key',
            component: () => import('@/views/workbench/model/ModelShowView.vue'),
            meta: { title: '查看匹配模型', optType: 'view', parentName: 'workbench_model' }
          }
        ]
      },
      {
        name: 'workbench_display',
        path: '/workbench/display',
        redirect: '/workbench/display/index',
        meta: { title: '视图展示', icon: 'show' },
        children: [
          {
            path: '/workbench/display/index',
            component: () => import('@/views/workbench/display/DisplayMainView.vue'),
            meta: { title: '视图展示' }
          },
          {
            name: 'display_create',
            path: '/workbench/display/create',
            component: () => import('@/views/workbench/display/DisplayCreateView.vue'),
            meta: { title: '创建视图模型', optType: 'add', permission: ['A', 'C'], parentName: 'workbench_display' }
          },
          {
            name: 'display_edit',
            path: '/workbench/display/edit/:key',
            component: () => import('@/views/workbench/display/DisplayCreateView.vue'),
            meta: { title: '修改视图模型', optType: 'edit', permission: ['A', 'C'], parentName: 'workbench_display' }
          },
          {
            name: 'display_view',
            path: '/workbench/display/view/:key',
            component: () => import('@/views/workbench/display/DisplayDataView.vue'),
            meta: { title: '视图查看', icon: 'show' }
          },
        ]
      },
      {
        name: 'workbench_chat',
        path: '/workbench/chat',
        redirect: '/workbench/chat/index',
        meta: { title: 'AI 标签', icon: 'chat' },
        children: [
          {
            path: '/workbench/chat/index',
            component: () => import('@/views/workbench/chat/ChatMain.vue'),
            meta: { title: 'AI Chat' }
          }
        ]
      },
      {
        name: 'workbench_address',
        path: '/workbench/address',
        redirect: '/workbench/address/index',
        meta: { title: '地址处理', icon: 'data' },
        children: [
          {
            path: '/workbench/address/index',
            component: () => import('@/views/workbench/address/AddressProcessView.vue'),
            meta: { title: '地址处理' }
          }
        ]
      }
    ]
  },
  {
    path: '/project',
    component: LayoutNoMenu,
    redirect: '/project/list',
    meta: { hidden: true },
    children: [
      {
        path: '/project/list',
        component: () => import('@/views/project/ProjectListView.vue'),
        meta: { title: '项目管理', hidden: true }
      }
    ]
  },
  {
    path: "/404",
    component: () => import("@/views/error-page/404.vue"),
    meta: { hidden: true },
    alias: "/:pathMatch(.*)*"
  }
]

const filterRoutes = (routes: any, authorities: any) => {
  function hasPermission(route: any) {
    if (route.meta && route.meta.authorities) {
      return authorities && authorities.some((auth: any) => route.meta.authorities.includes(auth))
    } else {
      return true
    }
  }
  function filter(children: any) {
    let num = 0
    if (children) {
      children.forEach((r: any) => {
        if (r.hidden !== true) {
          num++
        }
      })
    }
    return num === 0
  }
  const res: any = []
  routes.forEach((route: any) => {
    const tmp = { ...route }
    if (hasPermission(tmp)) {
      if (tmp.children) {
        tmp.children = filterRoutes(tmp.children, authorities)
      }
      if (filter(tmp.children)) {
        // tmp.children = null
        tmp.show = false
      }
      res.push(tmp)
    }
  })
  return res
}



const createNewRouter = (init: boolean) => {

  const authorities = []
  const userText = localStorage.getItem('user')
  if (userText) {
    const userStore = JSON.parse(userText)
    authorities.length = 0
    if (userStore.authorities) {
      authorities.push(...userStore.authorities)
    }
  }

  const accessRoutes = filterRoutes(constantRouterMap, authorities)
  return createRouter({
    history: createWebHashHistory(import.meta.env.BASE_URL),
    // mode: 'history', // require service support
    scrollBehavior: (to, from, s) => ({ top: 0 }),
    routes: accessRoutes
  })
}

const router = createNewRouter(true)

export default router
