import store from '@/common/store'
import {defineStore} from 'pinia'
import {removeToken, setToken} from '@/common/utils/cookies'
import {login_api, logout_api, health_check_api} from '@/api/auth/login'
import {project_switch_api} from '@/api/project'
import {nextTick} from 'vue'

export const useUserStore = defineStore('user', {
  state: () => {
    return {
      userCode: '',
      userName: '',
      userType: '',
      city: '',
      district: '',
      street: '',
      roles: [],
      authorities: [],
      currentProject: {},
      projectOptions: []
    }
  },
  actions: {
    Login(username: string, password: string) {
      return new Promise((resolve, reject) => {
        login_api(username, password).then((response) => {
          const {sessionId, userId, userName, userType, district, street, currentProject, roles, authorities, projectOptions} = response.data
          setToken(sessionId)
          this.userCode = userId
          this.userName = userName
          this.userType = userType
          this.district = district
          this.street = street
          this.roles = roles
          this.authorities = authorities
          this.projectOptions = projectOptions
          const cp = projectOptions.filter((p: any) => p.value === currentProject)
          if (cp && cp.length > 0) {
            this.currentProject = {
              projectId: cp[0].value,
              projectName: cp[0].label
            }
          }
          resolve(userId)
        }).catch((error) => {
          reject(error)
        })
      })
    },
    HealthCheck(toLogin: boolean) {
      return new Promise((resolve, reject) => {
        health_check_api(toLogin).then(response => {
          resolve(response)
        }).catch(error => {
          reject(error)
        })
      })
    },
    Logout() {
      return new Promise((resolve: any, reject) => {
        logout_api().then(() => {
          removeToken()
          this.clearStore()
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },
    SwitchProject(projectId: string) {
      return new Promise((resolve: any, reject) => {
        project_switch_api(projectId).then(res => {
          this.currentProject = res.data || {}
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },
    RemoveProject(projectId: string) {
      if ((this.currentProject as any).projectId === projectId) {
        this.currentProject = {}
      }
      const index = this.projectOptions.findIndex((e: any) => e.value === projectId)
      if (index > -1) {
        this.projectOptions.splice(index, 1)
      }
    },

    UpdateProject(opt: string, project: any) {
      const projectId = project.projectId
      if (opt === 'delete') {
        if ((this.currentProject as any).projectId === projectId) {
          this.currentProject = {}
        }
        const index = this.projectOptions.findIndex((e: any) => e.value === projectId)
        if (index > -1) {
          this.projectOptions.splice(index, 1)
        }
      } else {
        if ((this.currentProject as any).projectId === projectId) {
          (this.currentProject as any).projectName = project.projectName
        }

        if (opt === 'edit') {
          const option: any = this.projectOptions.find((e: any) => e.value === projectId)
          if (option) {
            option.label = project.projectName
          }
        } else if (opt === 'add') {
          const newOption: any = {
            label: project.projectName,
            value: project.projectId
          }
          // @ts-ignore
          this.projectOptions.splice(0, 0, newOption)
        }
      }
    },

    FedLogOut() {
      return new Promise((resolve: any) => {
        this.clearStore()
        removeToken()
        resolve()
      })
    },

    clearStore() {
      this.userCode = ''
      this.userName = ''
      this.userType = ''
      this.district = ''
      this.street = ''
      this.roles = []
      this.authorities = []
      this.currentProject = {}
      this.projectOptions = []
    }
  },
  persist: {
    enabled: true,
    strategies: [
      {storage: localStorage}
    ]
  }
})

/** 在 setup 外使用 */
export function useUserStoreHook() {
  return useUserStore(store)
}
