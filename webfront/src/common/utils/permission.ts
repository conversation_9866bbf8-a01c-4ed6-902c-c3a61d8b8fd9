import {useUserStoreHook} from '@/common/store/modules/user'

/**
 * @param {Array} value
 * @returns {Boolean}
 * @example see @/views/permission/directive.vue
 */
export const hasPermission = (value: any): boolean => {
  if (value && value instanceof Array && value.length > 0) {
    const store = useUserStoreHook()
    const permissionAuthorities = value
    const permissions = store.authorities || []
    return permissions.some(auth => {
      return permissionAuthorities.includes(auth)
    })
  } else {
    console.log(`need Authorities! Like v-permission="['A','C']"`)
    return false
  }
}
