import {read, utils} from 'xlsx'

export const readExcelHeader = async (file: any) => {

  function type(t: string) {
    // b Boolean, n Number, e error, s String, d Date, z Stub
    // 'b' | 'n' | 'e' | 's' | 'd' | 'z'
    if (t === 'n') return 2
    if (t === 'd') return 4
    return 1
  }

  const getHeaderRow = (sheet: any) => {
    const headers = [];
    const range = utils.decode_range(sheet["!ref"]);
    const R = range.s.r;
    let DR = -1
    if (range.e.r > R) {
      DR = R + 1
    }
    for (let C = range.s.c; C <= range.e.c; ++C) {
      /* walk every column in the range */
      const cell = sheet[utils.encode_cell({c: C, r: R})];
      let dataCell = cell
      if (DR != -1) {
        dataCell = sheet[utils.encode_cell({c: C, r: DR})];
        // console.log('dataCell', dataCell)
        if (!dataCell) {
          dataCell = cell
        }
      }
      /* find the cell in the first row */
      let hdr = "UNKNOWN " + C;
      if (cell && cell.t) {
        hdr = utils.format_cell(cell);
      }
      headers.push({
        n: hdr,
        t: type(dataCell ? dataCell.t : 's')
      });
    }
    return headers;
  }

  let dataBinary = await new Promise((resolve) => {
    const reader = new FileReader();
    reader.readAsBinaryString(file);
    reader.onload = (ev: any) => {
      resolve(ev.target.result);
    };
  });

  const workBook = read(dataBinary, {type: "binary", cellDates: true});
  let firstWorkSheet = workBook.Sheets[workBook.SheetNames[0]];
  return getHeaderRow(firstWorkSheet)
}

export const readExcel = async (file: any, func: Function) => {

  function type(t: string) {
    // b Boolean, n Number, e error, s String, d Date, z Stub
    // 'b' | 'n' | 'e' | 's' | 'd' | 'z'
    if (t === 'n') return 2
    if (t === 'd') return 4
    return 1
  }

  const getRow = function (sheet: any){
    const range = utils.decode_range(sheet["!ref"]);
    for (let R = 0; R <= range.e.r; R++) {
      const datas = [];
      for (let C = range.s.c; C <= range.e.c; ++C) {
        const cell = sheet[utils.encode_cell({c: C, r: R})];
        let hdr = "UNKNOWN " + C;
        if (cell && cell.t) {
          hdr = utils.format_cell(cell);
        }
        datas.push(hdr)
      }
      func(datas, R)
    }
  }

  let dataBinary = await new Promise((resolve) => {
    const reader = new FileReader();
    reader.readAsBinaryString(file);
    reader.onload = (ev: any) => {
      resolve(ev.target.result);
    };
  });

  const workBook = read(dataBinary, {type: "binary", cellDates: true});
  let firstWorkSheet = workBook.Sheets[workBook.SheetNames[0]];
  getRow(firstWorkSheet)
}
