// Parse the time to string
export const parseTime = (
    time?: object | string | number | null,
    cFormat?: string
): string | null => {
  if (time === undefined || !time) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date: Date
  if (typeof time === 'object') {
    date = time as Date
  } else {
    if (typeof time === 'string') {
      if (/^[0-9]+$/.test(time)) {
        // support "1548221490638"
        time = parseInt(time)
      } else {
        // support safari
        // https://stackoverflow.com/questions/4310953/invalid-date-in-safari
        time = time.replace(new RegExp(/-/gm), '/')
      }
    }
    if (typeof time === 'number' && time.toString().length === 10) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj: { [key: string]: number } = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const timeStr = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    return value.toString().padStart(2, '0')
  })
  return timeStr
}


export const guid = (): string => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    // const r = Math.random() * 16 | 0, v = c === 'x' ? r : (r & 0x3 | 0x8)
    const r = Math.random() * 16 | 0
    const v = c === 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}

export const uuid = (): string => {
  const s: any = []
  const hexDigits = '0123456789abcdef'
  for (let i = 0; i < 36; i++) {
    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
  }
  s[14] = '4' // bits 12-15 of the time_hi_and_version field to 0010
  s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1) // bits 6-7 of the clock_seq_hi_and_reserved to 01
  s[8] = s[13] = s[18] = s[23] = '-'
  return s.join('')
}


export const utf8ArrayToString = (array: Uint8Array): string => {
  let out = ''
  let char2, char3, c
  const len = array.length
  let i = 0
  while (i < len) {
    c = array[i++]
    switch (c >> 4) {
      case 0: case 1: case 2: case 3: case 4: case 5: case 6: case 7:
        // 0xxxxxxx
        out += String.fromCharCode(c)
        break
      case 12: case 13:
        // 110x xxxx   10xx xxxx
        char2 = array[i++]
        out += String.fromCharCode(((c & 0x1F) << 6) | (char2 & 0x3F))
        break
      case 14:
        // 1110 xxxx  10xx xxxx  10xx xxxx
        char2 = array[i++]
        char3 = array[i++]
        out += String.fromCharCode(((c & 0x0F) << 12) |
            ((char2 & 0x3F) << 6) |
            ((char3 & 0x3F) << 0))
        break
    }
  }
  return out
}

export const download = (response: any) => {
  const contentType = response.headers['content-type']
  const ct = contentType.split(';')
  const fileType = ct[0] || null
  let fileName: string = ct[1].split('=')[1]
  const blob = new Blob([response.data], {
    type: fileType
  })

  fileName = decodeURI(fileName)
  // if (window.navigator.msSaveOrOpenBlob) {
  //   navigator.msSaveBlob(blob, fileName)
  // } else {
  // }
  const link = document.createElement('a')
  link.href = window.URL.createObjectURL(blob)
  link.download = fileName
  link.click()
  window.URL.revokeObjectURL(link.href)
}

export const formData = (data: any) => {
  function process(fd: any, key: any, object: any) {
    const keys = Object.keys(object)
    const pkey = key ? key + '.' : ''
    keys.forEach(k => {
      const value = object[k]
      if (Array.isArray(value)) {
        value.forEach((e, index) => {
          if (e instanceof Object) {
            process(fd, pkey + k + '[' + index + ']', e)
          } else {
            fd.append(pkey + k + '[' + index + ']', e)
          }
        })
      } else if (value instanceof Object) {
        const fileType = Object.prototype.toString.call(value)
        if (fileType === '[object File]' || fileType === '[object Blob]') {
          fd.append(pkey + k, value)
        } else {
          process(fd, pkey + k, value)
        }
      } else {
        if (value != null) {
          fd.append(pkey + k, value)
        }
      }
    })
  }
  const fd = new FormData()
  process(fd, null, data)
  return fd
}

export const clone = (json: any) => {
  if (json instanceof Array) {
    const targetArray: any = []
    json.forEach(e => {
      targetArray.push(JSON.parse(JSON.stringify(e)))
    })
    return targetArray
  }
  return JSON.parse(JSON.stringify(json))
}

export const generateColumnId = () => {
  return randomString(5)
}

const randomString = (len: number) => {
  len = len || 32;
  const $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefhijkmnprstwxyz1234567890';
  const maxPos = $chars.length;
  let pwd = '';
  for (let i = 0; i < len - 1; i++) {
    pwd += $chars.charAt(Math.floor(Math.random() * maxPos));
  }
  const first = 'abcdefghijklmnopqrstuvwxyz'.charAt(Math.floor(Math.random() * 24))
  return first + pwd.toLowerCase();
}

export const swapArray = (arr: any, index1: number, index2: number) => {
  arr[index1] = arr.splice(index2, 1, arr[index1])[0]
  return arr
}

export const deleteArray = (arr: any, index: number) => {
  arr.splice(index, 1)
  return arr
}
