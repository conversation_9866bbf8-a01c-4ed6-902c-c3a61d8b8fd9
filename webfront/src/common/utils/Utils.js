// import CryptoJS from 'crypto-js'
//
// const key = CryptoJS.enc.Latin1.parse('m0CgGtFpEt2JpG01')
// const iv = CryptoJS.enc.Latin1.parse('gzAbttVaSVN0zPQV')

export const Utils = {

  isEmpty: function(text) {
    return !text || text.trim().length === 0
  },
  guid: function() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      // const r = Math.random() * 16 | 0, v = c === 'x' ? r : (r & 0x3 | 0x8)
      const r = Math.random() * 16 | 0
      const v = c === 'x' ? r : (r & 0x3 | 0x8)
      return v.toString(16)
    })
  },
  uuid: function() {
    const s = []
    const hexDigits = '0123456789abcdef'
    for (let i = 0; i < 36; i++) {
      s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
    }
    s[14] = '4' // bits 12-15 of the time_hi_and_version field to 0010
    s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1) // bits 6-7 of the clock_seq_hi_and_reserved to 01
    s[8] = s[13] = s[18] = s[23] = '-'
    return s.join('')
  },

  /**
   * @param {string} url
   * @returns {Object}
   */
  param2Obj: function(url) {
    const search = url.split('?')[1]
    if (!search) {
      return {}
    }
    return JSON.parse(
      '{"' +
      decodeURIComponent(search)
        .replace(/"/g, '\\"')
        .replace(/&/g, '","')
        .replace(/=/g, '":"')
        .replace(/\+/g, ' ') +
      '"}'
    )
  },
  generateNo: function() {
    const s = []
    const hexDigits = '01u5v6w2m3e7cdf4ghijkla89abnopqrstxiz'
    for (let i = 0; i < 6; i++) {
      s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
    }
    return '#' + s.join('')
  },
  generateYmdNo: function() {
    return '#' + this.formatDate(new Date(), 'yyyyMMddhhmmss')
  },
  sort: function(column, mapping) {
    if (column === null || !column.order || column.order === 'null') return ''
    let prop = column.property
    if (mapping && mapping[column.property]) {
      prop = mapping[column.property]
    }
    return prop + ':' + column.order
  },
  formatTimestamp: function(timestamp) {
    function f(t) {
      const date = new Date(t * 1000)
      return date.toLocaleTimeString()
    }
    return f(timestamp)
  },
  fillFile: function(add, formData, file) {
    if (file/* && file.raw*/) {
      if (add) {
        formData.append('file', file.raw)
      } else {
        if (file.update === true) {
          if (file.raw) {
            formData.append('file', file.raw)
          }
        }
        formData.append('fileUpdate', file.update)
      }
    }
  },
  download: function(response) {
    const contentType = response.headers['content-type']
    const ct = contentType.split(';')
    const fileType = ct[0] || null
    let fileName = ct[1].split('=')[1]
    const blob = new Blob([response.data], {
      type: fileType
    })

    fileName = decodeURI(fileName)
    if (window.navigator.msSaveOrOpenBlob) {
      navigator.msSaveBlob(blob, fileName)
    } else {
      const link = document.createElement('a')
      link.href = window.URL.createObjectURL(blob)
      link.download = fileName
      link.click()
      window.URL.revokeObjectURL(link.href)
    }
  },
  utf8ArrayToString: function(array) {
    let out = ''
    let char2, char3, c
    const len = array.length
    let i = 0
    while (i < len) {
      c = array[i++]
      switch (c >> 4) {
        case 0: case 1: case 2: case 3: case 4: case 5: case 6: case 7:
          // 0xxxxxxx
          out += String.fromCharCode(c)
          break
        case 12: case 13:
          // 110x xxxx   10xx xxxx
          char2 = array[i++]
          out += String.fromCharCode(((c & 0x1F) << 6) | (char2 & 0x3F))
          break
        case 14:
          // 1110 xxxx  10xx xxxx  10xx xxxx
          char2 = array[i++]
          char3 = array[i++]
          out += String.fromCharCode(((c & 0x0F) << 12) |
          ((char2 & 0x3F) << 6) |
          ((char3 & 0x3F) << 0))
          break
      }
    }
    return out
  },
  swapArray: function(arr, index1, index2) {
    arr[index1] = arr.splice(index2, 1, arr[index1])[0]
    return arr
  },
  clone: function(json) {
    if (json instanceof Array) {
      const targetArray = []
      json.forEach(e => {
        targetArray.push(JSON.parse(JSON.stringify(e)))
      })
      return targetArray
    }
    return JSON.parse(JSON.stringify(json))
  },
  on: (function() {
    if (document.addEventListener) {
      return function(element, event, handler) {
        if (element && event && handler) {
          element.addEventListener(event, handler, false)
        }
      }
    } else {
      return function(element, event, handler) {
        if (element && event && handler) {
          element.attachEvent('on' + event, handler)
        }
      }
    }
  })(),
  off: (function() {
    if (document.removeEventListener) {
      return function(element, event, handler) {
        if (element && event) {
          element.removeEventListener(event, handler, false)
        }
      }
    } else {
      return function(element, event, handler) {
        if (element && event) {
          element.detachEvent('on' + event, handler)
        }
      }
    }
  })(),
  keepFourDecimal: function(num) {
    const result = parseFloat(num)
    if (isNaN(result)) {
      return 0
    }
    return Math.round(num * 100000) / 100000
  },
  debounce(func, wait, immediate) {
    let timeout, args, context, timestamp, result
    const later = function() {
      // 据上一次触发时间间隔
      const last = +new Date() - timestamp

      // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
      if (last < wait && last > 0) {
        timeout = setTimeout(later, wait - last)
      } else {
        timeout = null
        // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
        if (!immediate) {
          result = func.apply(context, args)
          if (!timeout) context = args = null
        }
      }
    }

    return function(...args) {
      context = this
      timestamp = +new Date()
      const callNow = immediate && !timeout
      // 如果延时不存在，重新设定延时
      if (!timeout) timeout = setTimeout(later, wait)
      if (callNow) {
        result = func.apply(context, args)
        context = args = null
      }

      return result
    }
  },
  // 获取元素的绝对位置
  getPosition(node) {
    let left = node.offsetLeft
    let top = node.offsetTop
    let current = node.offsetParent // 取得元素的offsetParent
    // 一直循环直到根元素
    while (current !== null) {
      left += current.offsetLeft
      top += current.offsetTop
      current = current.offsetParent
    }
    return { left, top }
  },
  getScroll(node) {
    let scrollTop = node.scrollTop
    let scrollLeft = node.scrollLeft
    let current = node.parentNode // 取得元素的offsetParent
    // 一直循环直到根元素
    while (current !== null && current.tagName !== 'HTML') {
      scrollTop += current.scrollTop
      scrollLeft += current.scrollLeft
      current = current.parentNode
    }
    return { scrollLeft, scrollTop }
  },
  formatFileSize(fileSize) {
    if (fileSize) {
      if (fileSize > 1024 * 1024) {
        fileSize = fileSize / (1024 * 1024)
        return fileSize.toFixed(2) + 'M'
      } else {
        fileSize = fileSize / 1024
        return fileSize.toFixed(2) + 'KB'
      }
    } else {
      return '--'
    }
  },
  fix(n, fix) {
    return Number(n).toFixed(fix)
  },
  formatDate(date, format = 'yyyy-MM-dd') {
    const o = {
      'M+': date.getMonth() + 1, // 月份
      'd+': date.getDate(), // 日
      'h+': date.getHours(), // 小时
      'm+': date.getMinutes(), // 分
      's+': date.getSeconds(), // 秒
      'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
      'S': date.getMilliseconds() // 毫秒
    }
    if (/(y+)/.test(format)) format = format.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
    for (const k in o) {
      if (new RegExp('(' + k + ')').test(format)) {
        format = format.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length)))
      }
    }
    return format
  },
}
