
export const isEmpty = (str: any) => {
  return str !== null && str.trim().length === 0
}

export const isExternal = (path: any) => {
  return /^(https?:|mailto:|tel:)/.test(path)
}

// export function isValidNameCommon(str) {
//   return /^[^<>&\|\s!]*$/.test(str)
// }
//
// export function isValidEmailCharacter(str) {
//   return /^[a-zA-Z0-9!@#$%*.()_+^&\[\]\-]*$/.test(str)
// }
// export function isValidEmailStyle(str) {
//   return /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/.test(str)
// }
//
// export function isValidInteger(str) {
//   return /^-?(0|[1-9][0-9]*)$/.test(str)
// }
//
// export function isValidPhoneCharacter(str) {
//   return /^[0-9#-]*$/.test(str)
// }
//
// export function mergeList(listA, listB) {
//   var listResult = listA.concat(listB)
//   return listResult
// }

