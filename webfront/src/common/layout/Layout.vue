<template>
  <div :class="classObj">
    <navbar/>
    <sidebar v-if="hasAuth"/>
    <div v-if="hasAuth" class="main-content-wrapper">
      <router-view v-slot="{ Component }">
        <component :is="Component" v-if="computedPermission" :key="$route.fullPath" :ref="$route.fullPath"/>
        <forbidden v-else/>
      </router-view>
    </div>
  </div>
</template>

<script>
import { useRoute } from 'vue-router'
import { useAppStore } from '@/common/store/modules/app'
import { useUserStore } from '@/common/store/modules/user'
import { unref, computed, defineComponent, reactive, toRefs } from 'vue'
import { Navbar, Sidebar, AppMain, Forbidden } from './components'
import resize from './mixin/ResizeHandler'

export default defineComponent({
  name: 'Layout',
  components: {
    AppMain,
    Navbar,
    Sidebar,
    Forbidden
  },
  setup: function () {
    const store = useAppStore()
    const userStore = useUserStore()
    const currentRoute = useRoute()

    const {
      sidebar,
      device,
    } = resize()
    const state = reactive({
      handleClickOutside: () => {
        store.closeSidebar(false)
      }
    })

    const classObj = computed(() => {
      return {
        hideSidebar: !sidebar.value.opened,
        openSidebar: sidebar.value.opened
      }
    })

    const findPermission = () => {
      return unref(currentRoute).meta?.permission || []
    }

    const computedPermission = computed(() => {
      const pagePermission = findPermission()
      if (pagePermission.length > 0) {
        return pagePermission.some(auth => {
          return userStore.authorities.includes(auth)
        })
      }
      return true
    })

    const hasAuth = computed(() => {
      return userStore.authorities.length > 0
    })
    return {
      classObj,
      sidebar,
      hasAuth,
      computedPermission,
      ...toRefs(state)
    }
  }
})
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
  @import "src/assets/styles/mixin.scss";
  .main-content-wrapper{
    position: absolute;
    left: 163px;
    right: 0;
    top: 50px;
    bottom: 0;
    z-index: 998;
    width: auto;
    overflow-y: auto;
    box-sizing: border-box;
    background: #F7FAFC;
    padding: 10px;
    border-left: 1px solid #d0d2d0;
  }

  .hideSidebar {
    .main-content-wrapper {
      left: 50px !important;
    }
    .global-aside {
      width: 50px !important;
    }
  }

  .el-menu--collapse {
    width: 50px !important;
  }
</style>
