<template>
  <div class="wrap">
    <navbar/>
    <div class="main-content-wrapper">
      <router-view/>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
import { Navbar, AppMain } from './components'

export default defineComponent({
  name: 'Layout',
  components: {
    AppMain,
    Navbar
  },
  setup: function () {
    return {
    }
  }
})
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
@import "src/assets/styles/mixin.scss";
.main-content-wrapper{
  position: absolute;
  left: 0;
  right: 0;
  top: 52px;
  bottom: 0;
  z-index: 998;
  width: auto;
  overflow-y: auto;
  box-sizing: border-box;
  background: #e4ebf1;
  padding: 10px;
}
</style>
