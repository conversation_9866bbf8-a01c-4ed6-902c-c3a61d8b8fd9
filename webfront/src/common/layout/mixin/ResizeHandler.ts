
import { DeviceType } from '@/common/store/modules/app'
import { useAppStore } from '@/common/store/modules/app'
import { computed, watch } from 'vue'
import { useRoute } from 'vue-router'

const WIDTH = 992 // refer to Bootstrap's responsive design

export default function() {

  const store = useAppStore()

  const device: any = computed((): any => {
    return store.device
  })

  const sidebar = computed(() => {
    return store.sidebar
  })

  const currentRoute = useRoute()
  const watchRouter = watch(() => currentRoute.name, () => {
    if (store.device === DeviceType.Mobile && store.sidebar.opened) {
      store.toggleSidebar(false)
    }
  })

  const isMobile = () => {
    const rect = document.body.getBoundingClientRect()
    return rect.width - 1 < WIDTH
  }

  const resizeMounted = () => {
    if (isMobile()) {
      store.toggleDevice(DeviceType.Mobile)
      store.toggleSidebar(true)
    }
  }

  const resizeHandler = () => {
    if (!document.hidden) {
      store.toggleDevice(isMobile() ? DeviceType.Mobile : DeviceType.Desktop)
      if (isMobile()) {
        store.toggleSidebar(true)
      }
    }
  }
  const addEventListenerOnResize = () => {
    window.addEventListener('resize', resizeHandler)
  }

  const removeEventListenerResize = () => {
    window.removeEventListener('resize', resizeHandler)
  }

  return {
    device,
    sidebar,
    resizeMounted,
    addEventListenerOnResize,
    removeEventListenerResize,
    watchRouter
  }
}
