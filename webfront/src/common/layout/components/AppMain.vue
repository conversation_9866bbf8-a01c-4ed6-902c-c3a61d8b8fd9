<template>
  <section class="app-main">
    <router-view v-slot="{ Component }">
      <transition name="fade-transform" mode="out-in">
          <component :is="Component" />
      </transition>
    </router-view>
  </section>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
import { useRoute } from 'vue-router'

export default defineComponent({
  setup() {
    const route = useRoute()
    // const cachedViews = () => {
    //   return store.state.tagViews.cachedViews
    // }
    const key = () => {
      return route.path
    }
    return {
      // cachedViews,
      key
    }
  }
})
</script>

<style scoped>
.app-main {
  /*50 = navbar  */
  /*padding: 0px 10px 0 10px;*/
  /*min-height: calc(100vh - 48px - 70px);*/
  /*position: relative;*/
  /*overflow: hidden;*/
}
</style>
