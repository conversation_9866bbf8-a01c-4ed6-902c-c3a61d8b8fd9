<template>
  <div class="global-aside">
    <div class="global-aside-menu thin-scroll-bar">
      <el-menu
          :default-active="active"
          :collapse="isCollapse"
          :collapse-transition="false"
          class="main-sidebar"
          @select="handleSelect">
        <el-menu-item v-for="(r, index) in currentRouters" :index="r.path">
          <svg-icon :name="r.meta?.icon" style="height: 25px; width: 25px; margin-right: 10px"/>
          <span>{{r.meta.title}}</span>
        </el-menu-item>
      </el-menu>
    </div>
    <div class="sidebar-switch" :style="{'width': isCollapse ? '50px' : '160px'}" @click="handleToggleSidebar">
      <el-link :underline="false" :icon="isCollapse ? 'DArrowRight' : 'DArrowLeft'">
        <span v-if="!isCollapse" style="margin-left: 10px">折叠侧边栏</span>
      </el-link>
    </div>
  </div>
</template>

<script lang="ts">

import {defineComponent, reactive, ref, computed, watch} from 'vue'
import { useRouter} from 'vue-router'
import {constantRouterMap} from '@/common/router'
import type {RouteRecordRaw} from 'vue-router'
import SvgIcon from "@/common/icons/index.vue"
import {hasPermission} from '@/common/utils/permission'
import {useAppStore} from '@/common/store/modules/app'

export default defineComponent({
  components: {SvgIcon},
  setup() {
    const router = useRouter()

    let path = ref('')
    watch(() => router.currentRoute.value.path,
        (toPath) => {
          const p = toPath.split('/')
          path.value = '/' + p[1]
        }, {immediate: true, deep: true})

    const hasMetaPermission = (permission: any) => {
      if (permission instanceof Array) {
        if (permission.length > 0) {
          return hasPermission(permission)
        }
      }
      return true
    }

    const currentRouters = computed(() => {
      const current: Array<RouteRecordRaw> = []
      constantRouterMap.forEach(r => {
        // if (r.path === path.value) {
        // }
        r.children?.filter(e => (e.meta?.hidden !== true && hasMetaPermission(e.meta?.permission || []))).forEach(rr => {
          current.push(rr)
        })
      })
      return current
    })

    const active = computed(() => {
      const current = currentRouters.value.find(e => router.currentRoute.value.fullPath.startsWith(e.path));
      if (current) {
        return current.path
      }
      return ''
    })

    const handleSelect = (path: string) => {
      router.push(path)
    }

    const appStore = useAppStore()
    // const sidebar = ref(appStore.sidebar)

    const isCollapse = computed(() => {
      return appStore.sidebar.opened === false
    })

    const handleToggleSidebar = () => {
      appStore.toggleSidebar(true)
    }

    return {
      isCollapse,
      currentRouters,
      active,
      handleSelect,
      handleToggleSidebar
    }
  }
})
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.global-aside {
  position: fixed;
  left: 0;
  top: 50px;
  bottom: 0;
  overflow-x: hidden;
  background-color: #FEFEFE;
  font-size: 14px;
}

.global-aside-menu {
  display: block;
  width: 160px;
  height: calc(100vh - 100px);
  overflow-y: auto;
}

.main-sidebar {
  border-right: none !important;

  .el-menu-item {
    padding: 4px 10px;
    border-left: 4px solid transparent;
    height: 50px !important;
    &.is-active {
      border-left: 4px solid #007ACC;
      background: #F7FAFC;
      font-weight: bold;
    }
  }
}

.sidebar-switch {
  display: block;
  height: 40px;
  line-height: 40px;
  text-align: center;
  border-top: 1px solid #d0d2d0;
  position: fixed;
  bottom: 2px;
}

.sidebar-switch:hover {
  background: #f4f6f8;
  cursor: pointer;
}
</style>
