<template>
  <div class="global-header-container">
    <div class="header-logo">
      <span style="font-size: 20px; color: #FFF">Data Analytics</span>
    </div>
    <div class="header-project">
      <el-dropdown class="" trigger="hover" placement="bottom-start" max-height="500px" @command="handleSwitchProject">
        <span class="custom-dropdown">
          <el-text style="color: #FFF; max-width: 180px; font-size: 14px" truncated>{{ currentProjectName }}</el-text>
          <el-icon class="el-icon--right" color="#FFF">
            <ArrowDown/>
          </el-icon>
        </span>
        <template #dropdown>
          <el-dropdown-menu class="user-dropdown">
            <el-dropdown-item v-for="(o, index) in allProjectOption" :key="index" :command="o.value === currentProjectId ? {} : o" :icon="o.value === currentProjectId ? 'Select' : ''">
              <div class="w-150px" :style="{'padding-left': o.value === currentProjectId ? '0' : '20px'}">{{ o.label }}</div>
            </el-dropdown-item>
            <a class="inlineBlock" @click="handleProject">
              <el-dropdown-item icon="Setting" divided>
                <div class="w-150px">管理项目</div>
              </el-dropdown-item>
            </a>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <div class="header-user">
      <el-tag v-if="userTypeName" disable-transitions effect="light" type="" style="vertical-align: -2px; margin-right: 10px"> {{ userTypeName }}</el-tag>
      <el-dropdown class="avatar-container" trigger="hover" placement="bottom-start">
        <div class="custom-dropdown">
          <span>
           <el-icon :size="14" color="#FFF" style="margin-right: 5px; vertical-align: -2px">
              <UserFilled/>
            </el-icon>
            <b class="navbar-user-info">{{ userId }} [{{ userName }}]</b>
            <el-icon class="el-icon--right" color="#FFF">
              <caret-bottom/>
            </el-icon>
          </span>
        </div>
        <template #dropdown>
          <el-dropdown-menu class="user-dropdown">
            <router-link class="inlineBlock" to="/">
              <el-dropdown-item icon="HomeFilled">
                首页
              </el-dropdown-item>
            </router-link>
            <a class="inlineBlock" @click="handleChangePassword">
              <el-dropdown-item icon="Lock">
                修改密码
              </el-dropdown-item>
            </a>
            <a class="inlineBlock" @click="logout">
              <el-dropdown-item icon="SwitchButton" divided>
                退出
              </el-dropdown-item>
            </a>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
  <elx-dialog-ex ref="passwordDialogRef" title="修改密码" confirm-text="确认" width="600px">
    <el-form ref="passwordFormRef" :model="passwordForm" :rules="passwordFormRules" label-width="120px">
      <el-form-item label="旧密码" prop="oldPwd">
        <el-input v-model="passwordForm.oldPwd" maxlength="30" show-password style="width: 97%" auto-complete="off" clearable @blur="passwordForm.oldPwd = passwordForm.oldPwd.trim()"/>
      </el-form-item>
      <el-form-item label="新密码" prop="newPwd">
        <el-input v-model="passwordForm.newPwd" maxlength="30" show-password style="width: 97%" auto-complete="off" clearable @blur="passwordForm.newPwd = passwordForm.newPwd.trim()"/>
      </el-form-item>
      <el-form-item>
        <elx-pwd-complexity :password="passwordForm.newPwd" class="w-280px"/>
      </el-form-item>
      <el-form-item label="新密码确认" prop="confirmPwd">
        <el-input v-model="passwordForm.confirmPwd" maxlength="30" show-password style="width: 97%" auto-complete="off" clearable @blur="passwordForm.confirmPwd = passwordForm.confirmPwd.trim()"/>
      </el-form-item>
    </el-form>
  </elx-dialog-ex>
</template>

<script>

import {computed, reactive, toRefs, ref, inject} from 'vue'
import {useUserStore} from '@/common/store/modules/user'
import {useRoute, useRouter} from 'vue-router'
import {change_password_api} from '@/api/system/user'
import SvgIcon from '@/common/icons/index.vue';

export default {
  components: {SvgIcon},
  setup() {
    const store = useUserStore()
    const route = useRoute()
    const router = useRouter()
    const routerReload = inject('router-reload')

    const userId = ref(store.userCode)
    const userName = ref(store.userName)
    const userType = ref(store.userType)

    const userTypeName = computed(() => {
      const ut = store.userType
      let type = ''
      if (ut === '0') {
        type = '超级管理员'
      } else if (ut === '1') {
        type = '市管理员'
      } else if (ut === '2') {
        type = '区管理员(' + store.district + ')'
      } else if (ut === '3') {
        type = '街道管理员(' + store.district + ' / ' + store.street + ')'
      }
      return type
    })

    // const sidebar = computed(() => {
    //   return store.sidebar
    // })
    // const device = computed(() => {
    //   return store.device.toString()
    // })

    const currentProjectId = computed(() => {
      return store.currentProject.projectId
    })
    const currentProjectName = computed(() => {
      return store.currentProject.projectName || '选择项目'
    })

    const allProjectOption = computed(() => {
      return store.projectOptions || []
    })

    const passwordForm = reactive({
      oldPwd: '',
      newPwd: '',
      confirmPwd: ''
    })

    const passwordFormRules = {
      oldPwd: [
        { required: true, message: '必须输入旧密码', trigger: 'blur' },
        { min: 5, max: 30, message: '密码长度为5-30位', trigger: ['blur', 'change'] }
      ],
      newPwd: [
        { required: true, message: '必须输入新密码', trigger: 'blur' },
        { min: 5, max: 30, message: '密码长度为5-30位', trigger: ['blur', 'change'] }
      ],
      confirmPwd: [
        { required: true, message: '必须输入新密码确认', trigger: 'blur' },
        { validator: (rule, value, callback) => {
            if (passwordForm.newPwd !== '' && passwordForm.confirmPwd !== '' && passwordForm.confirmPwd !== passwordForm.newPwd) {
              callback(new Error('两次输入的新密码不一致'))
            } else {
              callback()
            }
          }, trigger: 'blur' }
      ]
    }

    const state = reactive({
      toggleSideBar: () => {
        // store.dispatch('ACTION_TOGGLE_SIDEBAR', false)
      },
      handleClick: (url) => {
        router.push(url)
      },
      logout: () => {
        store.Logout().then(res => {
          location.reload()
          // router.push(`/login`).catch(err => {
          //   console.warn(err)
          // })
        })
      }
    })

    const passwordDialogRef = ref()
    const passwordFormRef = ref()
    const handleChangePassword = () => {
      passwordDialogRef.value.open((done, close) => {
        passwordFormRef.value.validate(valid => {
          if (valid) {
            change_password_api(passwordForm).then(res => {
              close()
            }).catch(e => {
            }).finally(() => {
              done()
            })
          } else {
            done()
          }
        })
      })
    }

    const activeIndex = computed(() => {
      const fullPath = route.fullPath
      const index = fullPath.indexOf('/', 1)
      console.log(fullPath.substring(0, fullPath.indexOf('/', index + 1)))
      return fullPath.substring(0, fullPath.indexOf('/', index + 1))
    })

    const handleSelect = (key, keyPath) => {
      if (key.startsWith('/')) {
        router.push(key)
      } else {
      }
    }

    const handleProject = () => {
      router.push('/project/list')
    }

    const handleSwitchProject = (p) => {
      if (p.value) {
        ElMessageBox.confirm('确定要切换到项目【' + p.label + '】？', '确认', { type: 'warning' }).then(() => {
          store.SwitchProject(p.value).then(res => {
            if (route.path === '/workbench/table/index') {
              routerReload()
            } else {
              router.push('/workbench/table/index')
            }
          }).catch(err => {
          }).finally(() => {
          })
        }).catch(e => {
        })
      }
    }

    return {
      userId,
      userName,
      userType,
      userTypeName,
      activeIndex,
      handleSelect,
      handleProject,
      currentProjectId,
      currentProjectName,
      allProjectOption,
      handleSwitchProject,
      ...toRefs(state),
      handleChangePassword,
      passwordForm,
      passwordFormRules,
      passwordDialogRef,
      passwordFormRef
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.global-header-container {
  //background-color: var(--primary-color);
  background: linear-gradient(to bottom, rgba(64,158,255,0.9), rgba(64,158,255,0.8));
  border-bottom: 1px solid #409eff;
  z-index: 1000;
  height: 50px;
  position: relative;
  color: var(--nav-text-color, hsla(0deg, 0%, 100%, 0.6));
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.5);

  .el-menu--horizontal {
    .el-menu-item {
      &.is-active {
        border-bottom: 2px solid var(--el-menu-active-color);
        color: var(--el-menu-active-color) !important;
      }
    }
  }

  .el-sub-menu__icon-arrow {
    margin-left: -10px;
  }
}

.header-logo {
  height: 50px;
  line-height: 50px;
  width: 205px;
  margin-left: 10px;
  float: left;
  font-weight: bold;
}

.header-item {
  height: 52px;
  line-height: 52px;
  width: 90px;
  margin-left: 10px;
  float: left;
  font-size: 13px;
  font-weight: 500;
  text-align: center;
  cursor: pointer;
}

.header-item:hover {
  background: rgba(255, 255, 255, .1);
}

.header-project {
  line-height: 50px;
  display: inline-block;
  cursor: pointer;
  padding-top: 20px;
}

.header-user {
  height: 50px;
  line-height: 50px;
  margin-right: 10px;
  cursor: pointer;
  float: right;
}

.avatar-container {
  vertical-align: middle !important;
}

.navbar-user-info {
  color: #FFF
}

:deep(.el-menu-item) {
  font-weight: bold;
}
:deep(.el-dropdown) {
  border: none !important;
}
</style>

<style rel="stylesheet/scss" lang="scss">
.el-menu-item,
.el-sub-menu {
  &.is-active {
    font-weight: bold;
    //color: #FFF !important;
  }
}
</style>
