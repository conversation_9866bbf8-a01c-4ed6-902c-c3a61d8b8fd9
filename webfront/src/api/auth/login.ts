import { service } from '@/common/request'
import Qs from 'qs'

export const login_api = (username: string, password: string) => {
  const params = {
    username: username,
    password: password
  }
  return service({
    url: '/auth/login',
    method: 'post',
    data: Qs.stringify(params),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8' }
  })
}

export const health_check_api = (toLogin: boolean) => {
  return service({
    url: `/session/check?toLogin=${toLogin}`,
    method: 'get'
  })
}

export const about_api = () => {
  return service({
    url: '/version',
    method: 'get'
  })
}

export const logout_api = () => {
  return service({
    url: '/auth/logout',
    method: 'post'
  })
}
