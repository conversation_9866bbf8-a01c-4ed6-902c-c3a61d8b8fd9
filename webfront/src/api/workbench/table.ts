import {service} from '@/common/request'

export const table_save_api = (form: any, optType: string, columnList: Array<any>) => {

  const param = {
    ...form,
    optType,
    columnList
  }
  return service({
    url: '/workbench/table-save',
    method: 'post',
    data: param
  })
}

export const table_get_api = (tableId: string) => {
  return service({
    url: `/workbench/table-get/${tableId}`,
    method: 'get'
  })
}

export const search_table_api = (searchKey: string, pagination: any) => {
  const param = {
    searchKey,
    pagination
  }
  return service({
    url: '/workbench/select-table',
    method: 'post',
    data: param
  })
}

export const delete_table_api = (tableId: string) => {
  return service({
    url: '/workbench/delete-table/' + tableId,
    method: 'delete'
  })
}

export const clear_table_data_api = (tableId: string) => {
  return service({
    url: '/workbench/clear-table-data/' + tableId,
    method: 'delete'
  })
}

export const search_table_data_api = (init: boolean, tableId: string, filter: any, pagination: any, deleted: boolean = false) => {
  const param = {
    init: init || false,
    filter: filter,
    deleted: deleted,
    pagination
  }
  return service({
    url: '/workbench/select-table-data/' + tableId,
    method: 'post',
    data: param
  })
}

export const import_table_data_api = (form: any, tags: any) => {

  const {tableId, file} = form
  // @ts-ignore
  return service({
    url: '/workbench/import-table-data/' + tableId,
    method: 'post',
    formData: true,
    data: {file, tags}
  })
}

export const match_area_api = (tableId: string) => {
  return service({
    url: '/workbench/match-area/' + tableId,
    method: 'post'
  })
}

export const export_table_data_api = (tableId: string, form: any, filterForm: any) => {
  const param = {
    ...form,
    filter: filterForm
  }
  return service({
    url: '/workbench/export-table-data/' + tableId,
    method: 'post',
    responseType: 'arraybuffer',
    data: param
  })
}

export const edit_table_data_api = (tableId: string, rowId: number, data: any) => {
  const param = {
    tableId,
    rowId,
    data
  }
  return service({
    url: '/workbench/edit-table-data/' + tableId,
    method: 'post',
    data: param
  })
}

export const delete_table_data_api = (tableId: string, dataIds: any, del: boolean = false) => {
  const param = {
    dataIds,
    del
  }
  return service({
    url: '/workbench/delete-table-data/' + tableId,
    method: 'post',
    data: param
  })
}

export const save_filter_api = (tableId: string, filterName: string, filters: any) => {
  const param = {
    filterName,
    tableId,
    filters
  }
  return service({
    url: '/workbench/save-filter-template',
    method: 'post',
    data: param
  })
}

export const filter_history_api = (tableId: string) => {
  return service({
    url: '/workbench/filter-history/' + tableId,
    method: 'get'
  })
}

export const delete_filter_api = (tableId: string, id: number) => {
  return service({
    url: `/workbench/delete-filter/${tableId}/${id}`,
    method: 'post'
  })
}

export const data_duplication_check_api = (tableId: string, form: any, pagination: any) => {
  const param = {
    tableId,
    ...form,
    pagination
  }
  return service({
    url: '/workbench/data-duplication-check',
    method: 'post',
    data: param
  })
}

export const delete_duplication_data_api = (tableId: string, form: any) => {
  const param = {
    tableId,
    ...form
  }
  return service({
    url: '/workbench/data-duplication-delete',
    method: 'post',
    data: param
  })
}

export const batch_edit_table_data_api = (tableId: string, dataList: any, filters: any) => {
  const param = {
    dataList,
    filters
  }
  return service({
    url: '/workbench/batch-edit-table-data/' + tableId,
    method: 'post',
    data: param
  })
}

export const search_table_edit_history_api = (tableId: string, recordId: string) => {
  return service({
    url: `/workbench/table-edit-history-data/${tableId}/${recordId}`,
    method: 'post'
  })
}

export const table_quota_verify_api = (tableId: string, form: any, filter: any) => {
  const param = {
    ...form,
    ...filter
  }
  return service({
    url: `/workbench/table-data-quota-verify/${tableId}`,
    method: 'post',
    data: param
  })
}

export const template_download_api = (template: string) => {
  return service({
    url: '/workbench/template-download/' + template,
    method: 'post',
    responseType: 'arraybuffer'
  })
}

export const search_export_history_list_api = (tableId: string, tableMode: string) => {
  const param = {
    tableMode: tableMode
  }
  return service({
    url: `/workbench/export-history-list/${tableId}`,
    method: 'post',
    data: param
  })
}

export const download_file_api = (fileId: string) => {
  return service({
    url: `/workbench/download-file/${fileId}`,
    method: 'post',
    responseType: 'arraybuffer'
  })
}

export const change_area_api = (area: string[], road: string, address: string) => {
  const param = {
    area, road, address
  }
  return service({
    url: `/workbench/update-area`,
    method: 'post',
    data: param
  })
}
