import {service} from '@/common/request'

export const display_init_api = (opt: string, key: string) => {

  const param = {
    opt,
    displayId: key
  }
  return service({
    url: '/workbench/display-init',
    method: 'post',
    data: param
  })
}

export const display_save_api = (optType: string, displayId: string, form: any) => {

  const param = {
    optType,
    displayId,
    ...form,
  }
  return service({
    url: '/workbench/display-save',
    method: 'post',
    data: param
  })
}

export const display_get_api = (displayId: string) => {
  return service({
    url: `/workbench/display-get/${displayId}`,
    method: 'get'
  })
}

export const get_table_columns_api = (tableId: string) => {
  return service({
    url: '/workbench/table-columns/' + tableId,
    method: 'get'
  })
}

export const search_display_api = (searchKey: string, pagination: any) => {
  const param = {
    searchKey,
    pagination
  }
  return service({
    url: '/workbench/select-display',
    method: 'post',
    data: param
  })
}

export const delete_display_api = (displayId: string) => {
  return service({
    url: '/workbench/delete-display/' + displayId,
    method: 'delete'
  })
}

export const select_display_data_api = (displayId: string, filter: any, pagination: any, init: boolean) => {
  const param = {
    displayId,
    filter: filter,
    pagination,
    init
  }
  return service({
    url: '/workbench/select-display-data/' + displayId,
    method: 'post',
    data: param
  })
}

export const re_analysis_api = (displayId: string) => {
  return service({
    url: '/workbench/display-re-analysis/' + displayId,
    method: 'post'
  })
}

export const refresh_status_api = (displayId: string) => {
  return service({
    url: '/workbench/refresh-status/' + displayId,
    method: 'get'
  })
}

export const export_display_data_api = (displayId: string, form: any, filterForm: any) => {
  const param = {
    ...form,
    filter: filterForm
  }
  return service({
    url: '/workbench/export-display-data/' + displayId,
    method: 'post',
    data: param
  })
}

export const edit_display_match_data_api = (displayId: string, dataId: string, columnId: string, value: string) => {
  const param = {
    dataId,
    columnId,
    value
  }
  return service({
    url: '/workbench/edit-display-match-data/' + displayId,
    method: 'post',
    data: param
  })
}

export const display_submit_model_api = (displayId: string, dataId: string, columnId: string, correctContent: string) => {
  const param = {
    dataId,
    columnId,
    correctContent,
  }
  return service({
    url: '/workbench/display-submit-model/' + displayId,
    method: 'post',
    data: param
  })
}
