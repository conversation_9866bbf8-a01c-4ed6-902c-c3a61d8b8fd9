import {service} from '@/common/request'

/**
 * 处理地址Excel文件
 */
export const process_address_api = (file: File) => {
  const formData = new FormData()
  formData.append('file', file)
  
  return service({
    url: '/workbench/address/process',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 查询任务状态
 */
export const get_task_status_api = (taskId: string) => {
  return service({
    url: `/workbench/address/status/${taskId}`,
    method: 'get'
  })
}

/**
 * 下载处理结果
 */
export const download_address_result_api = (taskId: string, fileName?: string) => {
  const params = fileName ? { fileName } : {}

  return service({
    url: `/workbench/address/download/${taskId}`,
    method: 'get',
    params,
    responseType: 'blob'
  })
}
