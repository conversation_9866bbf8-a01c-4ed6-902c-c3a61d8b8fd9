import {service} from '@/common/request'

export const search_model_api = (searchKey: string, pagination: any) => {
  const param = {
    searchKey,
    pagination
  }
  return service({
    url: '/workbench/select-model',
    method: 'post',
    data: param
  })
}

export const model_save_api = (form: any, optType: string, labelList: Array<any>,
                               aiLabelList: Array<any>, aiRuleList: Array<any>) => {

  const param = {
    ...form,
    optType,
    labelList: labelList,
    aiLabelList: aiLabelList,
    aiRuleList: aiRuleList
  }
  return service({
    url: '/workbench/model-save',
    method: 'post',
    data: param
  })
}

export const model_get_api = (modelId: string, add: boolean) => {
  return service({
    url: `/workbench/model-get/${modelId}?add=` + add,
    method: 'get'
  })
}


export const delete_model_api = (modelId: string) => {
  return service({
    url: `/workbench/model-delete/${modelId}`,
    method: 'delete'
  })
}

export const export_model_api = (modelId: string) => {
  return service({
    url: `/workbench/model-export/${modelId}`,
    responseType: 'arraybuffer',
    method: 'get'
  })
}

export const import_model_api = (form: any) => {
  // @ts-ignore
  return service({
    url: '/workbench/import-model',
    method: 'post',
    formData: true,
    data: form
  })
}

export const refresh_model_api = (modelId: string) => {
  return service({
    url: `/workbench/model-refresh/${modelId}`,
    method: 'post'
  })
}
