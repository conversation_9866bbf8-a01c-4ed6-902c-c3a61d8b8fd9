import { service } from '@/common/request';

export const searchUserByPaging = (searchInput: string, searchRoles: any, pagination: any, init: boolean = false) => {
  const params = {
    searchInput,
    searchRoles,
    pagination,
    init: init || false
  }
  return service({
    url: '/user/search',
    method: 'post',
    data: params
  })
}

export const getUser_api = (optType: string, id: string) => {
  const params = {
    optType: optType,
    userId: id
  }
  return service({
    url: '/user/get',
    method: 'post',
    data: params
  })
}

export const addUser_api = (userInfo: any) => {
  const params = {
    userId: userInfo.userId,
    userEmail: userInfo.userEmail,
    userName: userInfo.userName,
    password: userInfo.password,
    project: userInfo.project,
    userType: userInfo.userType,
    district: userInfo.district,
    town: userInfo.town
  }
  return service({
    url: '/user/add',
    method: 'post',
    data: params
  })
}

export const updateUser_api = (userInfo: any) => {
  const params = {
    userId: userInfo.userId,
    userEmail: userInfo.userEmail,
    userName: userInfo.userName,
    password: userInfo.password,
    updatePass: userInfo.updatePass,
    project: userInfo.project,
    userType: userInfo.userType,
    district: userInfo.district,
    town: userInfo.town
  }
  return service({
    url: '/user/update',
    method: 'post',
    data: params
  })
}

export const deleteUser_api = (userId: string) => {
  const params = {
    userId: userId
  }
  return service({
    url: '/user/delete',
    method: 'post',
    data: params
  })
}

export const deleteUsers_api = (ids: any) => {
  const params = {
    userList: ids
  }
  return service({
    url: '/user/multipleDelete',
    method: 'post',
    data: params
  })
}

export const change_password_api = (form: any) => {
  const params = {
    ...form
  }
  return service({
    url: '/user/password',
    method: 'post',
    data: params
  })
}
