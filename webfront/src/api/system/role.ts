import {service} from '@/common/request'

export const searchByPaging = (roleName: string, init: boolean, pagination: any, sorts: any) => {
  const params = {
    roleName: roleName,
    pagination: pagination,
    init: init,
    sorts: sorts || []
  }
  return service({
    url: '/role/search',
    method: 'post',
    data: params
  })
}

export const getRole = (type: string, id: string) => {
  const param = {
    'optType': type,
    'id': id
  }
  return service({
    url: '/role/get',
    method: 'post',
    data: param
  })
}
export const getUserCountByRoleId = (roleId: string) => {
  const param = {
    'roleId': roleId
  }
  return service({
    url: '/role/getUserCountByRoleId',
    method: 'post',
    data: param
  })
}
export const deleteRole = (roleId: string) => {
  const param = {
    'roleId': roleId
  }
  return service({
    url: '/role/delete',
    method: 'post',
    data: param
  })
}

export const deleteRoles = (roleIds: any) => {
  return service({
    url: '/role/multipleDelete',
    method: 'post',
    data: roleIds
  })
}

export const addRole = (form: any) => {
  return service({
    url: '/role/add',
    method: 'post',
    data: form
  })
}

export const updateRole = (form: any) => {
  return service({
    url: '/role/modify',
    method: 'post',
    data: form
  })
}

