import {service} from '@/common/request'

export const project_search_api = () => {
  return service({
    url: '/projects',
    method: 'get'
  })
}

export const project_create_api = (opt: string, form: any) => {

  const param = {
    opt,
    ...form
  }
  return service({
    url: '/project/create',
    method: 'post',
    data: param
  })
}

export const delete_project_api = (projectId: string) => {
  return service({
    url: '/project/delete/' + projectId,
    method: 'post'
  })
}

export const project_switch_api = (projectId: string) => {
  return service({
    url: '/project/switch/' + projectId,
    method: 'post'
  })
}
