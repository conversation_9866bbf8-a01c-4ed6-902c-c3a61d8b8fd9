// @ts-nocheck

import { fileURLToPath, URL } from 'node:url'

import path, { resolve } from "path"
import { defineConfig, loadEnv } from 'vite'

import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import svgLoader from 'vite-svg-loader'
// import Icons from 'unplugin-icons/vite'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import Inspect from 'vite-plugin-inspect'
// import VueDevTools from 'vite-plugin-vue-devtools'

import ElementPlus from 'unplugin-element-plus/vite'

import Unocss from 'unocss/vite'
import {
  presetAttributify,
  presetIcons,
  presetUno,
  transformerDirectives,
  transformerVariantGroup,
} from 'unocss'


import DefineOptions from "unplugin-vue-define-options/vite"

// https://vitejs.dev/config/
export default defineConfig(({ command, mode, ssrBuild }) => {
  const env = loadEnv(mode, process.cwd(), '')
  return {
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      },
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "@/assets/styles/element/index.scss" as *;`,
        },
      },
    },
    define: {
      // 'process.env': {
      //   'BASE_API':"http://localhost:8089/"
      // }
    },
    plugins: [
      // VueDevTools(),
      vue(),
      ElementPlus({
        useSource: true,
        defaultLocale: 'zh-cn',
      }),
      vueJsx(),
      svgLoader({ defaultImport: "url" }),
      /** SVG */
      createSvgIconsPlugin({
        iconDirs: [path.resolve(process.cwd(), "src/common/icons/svg")],
        symbolId: "[name]"
      }),
      /** UnoCSS */
      Unocss({
        presets: [
          presetUno(),
          presetAttributify(),
          presetIcons({
            scale: 1.2,
            warn: true,
          }),
        ],
        transformers: [
          transformerDirectives(),
          transformerVariantGroup(),
        ]
      }),
      // Icons({
      //   autoInstall: true,
      // }),
      DefineOptions(),
      AutoImport({
        resolvers: [ElementPlusResolver({ importStyle: "sass" })],
        dts: 'src/components/element_plus_import.d.ts',
      }),
      Components({
        extensions: ['vue', 'md'],
        include: [/\.vue$/, /\.vue\?vue/, /\.md$/],
        resolvers: [ElementPlusResolver({ importStyle: "sass" })],
        dts: 'src/components/element_plus_components.d.ts',
      }),
      Inspect()
    ],
    server: {
      https: false,
      host: false, // host: "0.0.0.0"
      port: 8019,
      open: false,
      cors: true,
      strictPort: false
    },
    build: {
      chunkSizeWarningLimit: 2000,
      minify: "terser",
      terserOptions: {
        compress: {
          drop_console: false,
          drop_debugger: true,
          pure_funcs: ["console.log"]
        },
        format: {
          comments: false
        }
      },
      assetsDir: "static"
    },
  }
})
