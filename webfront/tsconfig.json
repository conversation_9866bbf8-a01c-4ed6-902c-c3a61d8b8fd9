{
  "extends": "@vue/tsconfig/tsconfig.web.json",
  "exclude": ["node_modules", "dist"],
  "include": [
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "tests/**/*.ts",
    "types/**/*.d.ts",
    "vite.config.ts"
  ],
  "compilerOptions": {
    "target": "esnext",
    /** https://cn.vitejs.dev/guide/features.html#typescript-compiler-options */
    "useDefineForClassFields": true,
    "module": "esnext",
    "moduleResolution": "node",
    /** TS 严格模式 */
    "strict": true,
    "allowJs": true,
    "jsx": "preserve",
    "importHelpers": true,
    "experimentalDecorators": true,
    "allowSyntheticDefaultImports": true,
    "sourceMap": true,
    "resolveJsonModule": true,
    /** https://cn.vitejs.dev/guide/features.html#typescript-compiler-options */
    "isolatedModules": true,
    "esModuleInterop": true,
    "lib": ["esnext", "dom"],
    "skipLibCheck": true,
    "types": [
      "node",
      "vite/client",
      /** Element Plus 的 Volar 插件支持 */
      "element-plus/global",
      "unplugin-vue-define-options/macros-global"
    ],
    "outDir": "./dist",
    /** baseUrl 用来告诉编译器到哪里去查找模块，使用非相对模块时必须配置此项 */
    "baseUrl": ".",
    /** 非相对模块导入的路径映射配置，根据 baseUrl 配置进行路径计算 */
    "paths": {
      "@/*": ["src/*"]
    }
  },

  "references": [
    {
      "path": "./tsconfig.node.json"
    }
  ]
}
