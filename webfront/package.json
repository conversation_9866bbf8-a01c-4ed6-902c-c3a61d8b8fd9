{"name": "webfront", "version": "0.0.1-beta", "private": true, "scripts": {"dev": "vite --mode dev", "build": "vite build --mode prod --base=/data-analytics/", "preview": "vite preview", "build-only": "run-p build-only", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "axios": "^1.3.5", "crypto-js": "^4.1.1", "element-plus": "^2.3.5", "js-cookie": "^3.0.1", "nprogress": "^0.2.0", "path-to-regexp": "^6.2.1", "pinia": "^2.0.34", "pinia-plugin-persist": "^1.0.0", "qs": "^6.11.1", "vue": "^3.2.47", "vue-router": "^4.1.6", "xlsx": "^0.18.5"}, "devDependencies": {"@rushstack/eslint-patch": "^1.2.0", "@types/js-cookie": "^3.0.3", "@types/node": "^18.14.2", "@types/nprogress": "^0.2.0", "@types/qs": "^6.9.7", "@vitejs/plugin-vue": "^4.0.0", "@vitejs/plugin-vue-jsx": "^3.0.1", "@vue/eslint-config-prettier": "^7.1.0", "@vue/eslint-config-typescript": "^11.0.2", "@vue/tsconfig": "^0.1.3", "eslint": "^8.34.0", "eslint-plugin-vue": "^9.9.0", "npm-run-all": "^4.1.5", "prettier": "^2.8.4", "sass": "^1.62.0", "terser": "^5.17.1", "typescript": "~4.8.4", "unocss": "^0.51.4", "unplugin-auto-import": "^0.15.3", "unplugin-element-plus": "^0.7.1", "unplugin-icons": "^0.16.1", "unplugin-vue-components": "^0.24.1", "unplugin-vue-define-options": "^1.3.3", "vite": "^4.1.4", "vite-plugin-html": "^3.2.0", "vite-plugin-inspect": "^0.7.24", "vite-plugin-svg-icons": "^2.0.1", "vite-svg-loader": "^4.0.0", "vue-tsc": "^1.2.0"}, "lint-staged": {"*.{js,jsx,vue,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{scss,less,css,html,md}": ["prettier --write"], "package.json": ["prettier --write"], "{!(package)*.json,.!(browserslist)*rc}": ["prettier --write--parser json"]}}